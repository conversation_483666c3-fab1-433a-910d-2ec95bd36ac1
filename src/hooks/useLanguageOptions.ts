import { TFun<PERSON><PERSON><PERSON> } from 'i18next';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { country } from 'utils/context';

type LanguageOption = {
  value: string;
  text: string;
};

const MY_LANGUAGE_OPTIONS: LanguageOption[] = [
  {
    value: 'en',
    text: 'common:en',
  },
  {
    value: 'my',
    text: 'common:my',
  },
];

export const useLanguageOptions = () => {
  const { t } = useTranslation();
  return useMemo(() => {
    switch (country) {
      case 'my':
      case 'ib':
        return MY_LANGUAGE_OPTIONS.map(i => ({
          ...i,
          text: t(i.text as T<PERSON>un<PERSON><PERSON><PERSON>),
        }));
      case 'ph':
      case 'id':
        return [];
    }
  }, [t]);
};
