import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Box, Typography, XView } from 'cube-ui-components';
import AddNewLeadIconV2SVG from 'features/home/<USER>/overview/AddLeadIconV2SVG';
import CreateSiShortcutV2SVG from 'features/home/<USER>/overview/CreateSiShortcutV2SCG';
import FNAShortcutV2SVG from 'features/home/<USER>/overview/FNAShortcutV2SVG';
import { SHORTCUTS_ICON_SIZE } from 'features/home/<USER>/Overview/ShortcutsSection/constants';
import { useHasPermission } from 'hooks/useCheckClientScope';
import { useResetAgentProfileCache } from 'hooks/useGetAgentProfile';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { TouchableOpacityProps } from 'react-native';
import { RootStackParamList } from 'types';
import GATracking from 'utils/helper/gaTracking';
import { countryModuleFnaConfig } from 'utils/config/module';

export default function ShortcutsSection() {
  const { t } = useTranslation('home');
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const { resetAgentProfile } = useResetAgentProfileCache();

  const { isWideScreen } = useWindowAdaptationHelpers();
  const SVG_SIZE = isWideScreen
    ? SHORTCUTS_ICON_SIZE.wideScreen
    : SHORTCUTS_ICON_SIZE.default;

  const hasPermission = useHasPermission();
  const canWorkOnSi = hasPermission('si');

  const showLeadModal = useCallback(() => {
    resetAgentProfile();
    navigation.navigate('AddNewLeadOrEntity');
  }, [navigation, resetAgentProfile]);

  const SHORTCUTS_CONFIG = [
    {
      type: 'addNewLead',
      icon: <AddNewLeadIconV2SVG size={SVG_SIZE} />,
      label: t('home.shortcutSection.addNewLead.normal'),
      isDisabled: !hasPermission('lead'),
      tabletLabel: t('home.shortcutSection.addNewLead.tablet'),
      onShortcutPress: () => {
        showLeadModal();
      },
    },
    {
      type: 'fna',
      icon: <FNAShortcutV2SVG size={SVG_SIZE} />,
      label: t('home.shortcutSection.fna.normalV2'),
      tabletLabel: t('home.shortcutSection.fna.tabletV2'),
      isDisabled: !canWorkOnSi || !countryModuleFnaConfig.hasShortcut,
      onShortcutPress: () => {
        resetAgentProfile();
        navigation.navigate('Fna');
        GATracking.logCustomEvent('fn_assessment', {
          form_source: 'homepage_cta',
          action_type: 'fna_open_form',
        });
      },
    },
    {
      type: 'createQuickQuote',
      icon: <CreateSiShortcutV2SVG size={SVG_SIZE} />,
      label: t('home.shortcutSection.createQuickQuote.normal'),
      tabletLabel: t('home.shortcutSection.createQuickQuote.tablet'),
      isDisabled: !canWorkOnSi,
      onShortcutPress: () => {
        resetAgentProfile();
        navigation.navigate('CoverageDetailsScreen');
        console.log(
          '🚀 ~ file: ShortcutsSection.tsx:35 ~ ShortcutsSection ~ CoverageDetailsScreen:',
        );
      },
    },
  ] as const;

  return (
    <>
      <Title>{t('home.shortcutSection.title')}</Title>
      <ShortcutsContainer>
        {SHORTCUTS_CONFIG.map(
          ({ type, label, tabletLabel, icon, onShortcutPress, isDisabled }) => (
            <ShortcutsItem
              key={'shortcuts_' + type}
              label={isWideScreen ? tabletLabel : label}
              icon={icon}
              onPress={onShortcutPress}
              disabled={isDisabled}
            />
          ),
        )}
      </ShortcutsContainer>
    </>
  );
}

interface ShortcutsItem extends TouchableOpacityProps {
  label: string;
  icon: React.ReactNode;
}

function ShortcutsItem({ label, icon, ...touchableProps }: ShortcutsItem) {
  const { colors, borderRadius, space } = useTheme();
  const isDisabled = touchableProps?.disabled ?? false;

  return (
    <TouchableItemContainer {...touchableProps}>
      <Box
        p={space[2]}
        borderRadius={borderRadius['x-large']}
        backgroundColor={colors.palette.fwdOrange[20]}
        style={{ opacity: isDisabled ? 0.5 : 1 }}>
        {icon}
      </Box>
      <ItemLabel fontWeight="medium">{label}</ItemLabel>
    </TouchableItemContainer>
  );
}

const Title = styled(Typography.LargeBody)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
  marginBottom: theme.space[4],
}));

const ShortcutsContainer = styled(XView)(({ theme }) => ({
  width: '100%',
  marginBottom: theme.space[6],
}));

const TouchableItemContainer = styled.TouchableOpacity(({ theme }) => ({
  flex: 1,
  alignItems: 'center',
  gap: theme.space[2],
}));

const ItemLabel = styled(Typography.H8)(({ theme }) => ({
  marginTop: theme.space[1],
  textAlign: 'center',
}));
