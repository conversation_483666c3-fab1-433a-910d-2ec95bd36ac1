export default {
  // Welcome section
  'home.welcomeSection.target': 'task completed',

  // Shortcut section
  'home.shortcutSection.title': 'What would you like to do?',
  'home.shortcutSection.addNewLead.normal': 'Add a\nnew lead',
  'home.shortcutSection.addNewLead.tablet': 'Add a new lead',
  'home.shortcutSection.fna.normalV2': 'Customer needs analysis (FNA)',
  'home.shortcutSection.fna.tabletV2': 'Customer needs analysis (FNA)',
  'home.shortcutSection.fna.normal': 'Know your\ncustomer (FNA)',
  'home.shortcutSection.fna.tablet': 'Customer needs analysis (FNA)',
  'home.shortcutSection.createQuickQuote.normal': 'Create quick quote',
  'home.shortcutSection.createQuickQuote.tablet': 'Create quick quote',
  'home.shortcutSection.createSaleIllustrationModalcommon:home.welcome.hi.title':
    'Create Benefit illustration for:',
  'home.shortcutSection.createSaleIllustrationModal.newLead': 'New Lead',
  'home.shortcutSection.createSaleIllustrationModal.existingLead':
    'Existing Lead',
  'home.shortcutSection.createSaleIllustrationModal.buttonCancelText': 'Cancel',
  'home.shortcutSection.createSaleIllustrationModal.buttonContinueText':
    'Continue',
  'home.shortcutSection.createSaleIllustrationModal.buttonConfirmText':
    'Confirm',
  'home.shortcutSection.createSaleIllustrationModal.foundLeads.title':
    'Is the same person?',
  'home.shortcutSection.createSaleIllustrationModal.foundLeads.subtitle':
    'We found someone having these information in your record.',
  'home.shortcutSection.createSaleIllustrationModal.foundLeads.mobile':
    'mobile: +{{code}}{{mobile}}',
  'home.shortcutSection.createSaleIllustrationModal.foundLeads.email':
    'email: {{email}}',
  'home.shortcutSection.createSaleIllustrationModal.foundLeads.buttonYesText':
    'Yes',
  'home.shortcutSection.createSaleIllustrationModal.foundLeads.buttonNoText':
    'No',
  'home.shortcutSection.createSaleIllustrationModal.newCompany': 'New Company',
  'home.shortcutSection.createSaleIllustrationModal.existingCompany':
    'Existing Company',
  'home.shortcutSection.createSaleIllustrationModal.individual': 'Individual',
  'home.shortcutSection.createSaleIllustrationModal.organization':
    'Organization',
  'home.shortcutSection.createSaleIllustrationModal.foundCompany.title':
    'Is the same company?',
  'home.shortcutSection.createSaleIllustrationModal.foundCompany.companyName':
    'Company name: {{companyName}}',
  'home.shortcutSection.createSaleIllustrationModal.foundCompany.registrationNumberNew':
    'Registration number(New): {{registrationNumber}}',

  // errors
  'home.shortcutSection.createSaleIllustrationModal.validation.requiredInput':
    'Required field',
  'home.shortcutSection.createSaleIllustrationModal.validation.invalidDate':
    'Invalid date',
  'home.shortcutSection.createSaleIllustrationModal.validation.notFoundLead':
    'No result from the FWD data base. Please try again.',

  // Dashboard section
  'home.dashboard.title': 'Dashboard',

  // Tooltip
  'home.tooltip.title': 'Did you know',
  'home.tooltip.content': 'You can customise your dashboard content',
  'home.tooltip.button': 'Try now',

  // FWD news card
  'home.fwdNews.title': 'FWD news',

  // Performance card
  'home.performance.title': 'Performance',
  'home.performance.title.performanceTracking': 'Performance snapshot',
  'home.performance.case': 'Case',
  'home.performance.case.submitted': 'Submitted',
  'home.performance.case.issued': 'Issued',
  'home.performance.persistency': 'Persistency',
  'home.performance.issued': 'Issued',
  'home.performance.completed': 'Completed',

  // Business opp card, or Prospect highlight
  'home.businessOpp.title': 'Prospect highlight',
  'home.businessOpp.reminderText': 'Displaying data from last 30 days',
  'home.businessOpp.inProgress': 'Proposals in progress',
  'home.businessOpp.inSI': 'Proposal in Sales illustration',
  'home.businessOpp.inApplication': 'Proposals in application',
  'home.businessOpp.submitted': 'New business pending',
  'home.businessOpp.submitted.urgent': 'Urgent',

  // Reorder panel
  'home.reorder.title': 'Change order',
  'home.reorder.dashboard.title': 'Change order',
  'home.reorder.dashboard.news': 'FWD News',
  'home.reorder.dashboard.marketing': 'Marketing activity',
  'home.reorder.dashboard.performance': 'Performance',
  'home.reorder.dashboard.businessOpportunity': 'Prospect highlight',
  'home.reorder.dashboard.trainerGuru': 'Trainer Guru',

  'home.reorder.task.birthday': 'Birthday',
  'home.reorder.task.leads': 'Contact leads',
  'home.reorder.task.paymentReminder': 'Payment reminders',
  'home.reorder.task.policyIssue': 'Policy issues',

  // Other
  'home.viewMore': 'View more',
  'home.viewAll': 'View all',

  // Marketing activity
  'home.marketingActivity.title': 'Marketing activity',
  'home.marketingActivity.first.label':
    'Get <bold>real impact</bold> over time',
  'home.marketingActivity.second.label': 'OVER LAST WEEK',
  'home.marketingActivity.posts': 'Posts',
  'home.marketingActivity.views': 'Views',

  // Tasks
  policyOwner: 'Policy owner',
  insured: 'Insured',
  teammate: 'Teammate',
  currency: 'RM',
  'task.completion.fail.msg': 'failed to complete the task',
  'task.payment.title': 'Payment reminders',
  'task.payment.card.title': 'Payment reminder',
  'task.payment.card.msg.overdue': '{{daysDiff}} days overdue',
  'task.payment.card.msg.due': 'Due in {{daysDiff}} days',
  'task.payment.emptyCase':
    'There are no policies nearly due or that are overdue at the moment, but you can keep an eye on other policies statuses.',
  'task.payment.card.nextContribution': 'Next premium',
  'task.payment.notification.title': 'Payment reminder',
  'task.payment.notification.body': 'Remind {{displayName}}',

  'task.policy.title': 'Policy issues',
  'task.policy.newBusinessPending': 'New Business Pending',
  'task.policy.emptyCase':
    'There are no follow-up issues so far, but you can keep an eye on other policy statuses to take action in advance.',
  'task.policy.notification.title': 'Policy reminder',
  'task.policy.notification.body': 'Remind to follow up Lead {{displayName}}',

  'task.reminder.title': 'Set reminder',
  'task.reminder.functionDescription':
    'The system will remind you to do the task in every ',
  'task.reminder.label.30mins': 'Remind me every 30 minutes',
  'task.reminder.label.1hr': 'Remind me every 1 hour',
  'task.reminder.label.2hrs': 'Remind me every 2 hours',
  'task.reminder.lead': 'Remind to follow up Lead ',
  'task.birthday.cardDescription': 'Send birthday card to',
  'task.birthday.actionDescription': 'Send birthday card to ',
  'task.birthday.action.title': 'Birthday reminder',
  'task.payment.contactMsg':
    'Dear {{displayName}}, keep your takaful protection going! Your contribution for FWD certificate {{policyNo}} is overdue on {{paidToDate}}. Contribution due: {{basicPremium}}. I will contact and follow up with you shortly.',
  // Notification
  'home.notification.clear': 'Clear',
  'home.termsAndConditions': 'Terms and conditions',
  'home.agree': 'Agree',
};
