import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { Path, Rect } from 'react-native-svg';

export function EmptyCase(props: SvgIconProps) {
  return (

    <Svg width="161" height="136" viewBox="0 0 161 136" fill="none"
         {...props}>
      <Rect x="19.5" y="13.8389" width="112" height="97.2896" rx="8" fill="#F8F9F9" />
      <Rect x="27.5" y="21.8628" width="96" height="41.1224" rx="4" fill="#D1D6D4" />
      <Rect x="27.5" y="71.009" width="76" height="4.01194" rx="2.00597" fill="#D1D6D4" />
      <Rect x="27.5" y="83.0448" width="66" height="4.01194" rx="2.00597" fill="#D1D6D4" />
      <Rect x="27.5" y="95.0807" width="56" height="4.01194" rx="2.00597" fill="#D1D6D4" />
      <Path
        d="M107.537 108.995L110.681 108.963L139.266 80.2921L146.422 87.4694C147.71 88.7613 147.688 90.8802 146.373 92.199L120.17 118.48L117.026 118.513L107.537 108.995Z"
        fill="#D1D6D4" />
      <Path
        d="M96.8346 95.0751L123.038 68.7939C124.353 67.4751 126.465 67.4532 127.753 68.7452L134.598 75.6104L106.013 104.281L105.98 107.434L96.8022 98.2282L96.8346 95.0751Z"
        fill="#D1D6D4" />
      <Path
        d="M106.021 104.289L134.606 75.6182L139.273 80.2991L110.688 108.969L107.544 109.002L105.988 107.442L106.021 104.289Z"
        fill="#8B9793" />
      <Path
        d="M96.8304 95.0812L107.431 96.1507L108.498 106.783L119.099 107.853L120.165 118.485C119.697 118.955 119.101 119.278 118.457 119.413L94.8823 124.334C93.7161 124.577 92.626 124.188 91.8855 123.445C91.4219 122.98 91.0975 122.379 90.9777 121.706C90.9553 121.573 90.9392 121.436 90.931 121.298C90.9176 121.018 90.9389 120.731 90.9994 120.439L95.906 96.7948C96.0401 96.1485 96.3621 95.551 96.8304 95.0812Z"
        fill="#FFECDE" />
      <Path
        d="M91.8837 123.549C92.6242 124.292 93.7144 124.681 94.8805 124.438L102.327 122.883L92.5316 113.152L90.9977 120.543C90.9371 120.835 90.9159 121.121 90.9292 121.401C90.9375 121.54 90.9536 121.677 90.976 121.81C91.0958 122.483 91.4201 123.084 91.8837 123.549Z"
        fill="#F3BB90" />
      <Path
        d="M33.256 29.478C33.256 30.3286 32.9333 31.0766 32.288 31.722C31.672 32.3673 30.8507 32.69 29.824 32.69C28.6507 32.69 27.712 32.2793 27.008 31.458C26.3333 30.6366 25.996 29.6393 25.996 28.466C25.996 26.9993 26.216 25.7673 26.656 24.77C27.1253 23.7433 27.6973 22.922 28.372 22.306C29.0467 21.6606 29.78 21.1913 30.572 20.898C31.364 20.5753 32.112 20.3846 32.816 20.326V23.01C31.9653 23.1566 31.1587 23.5526 30.396 24.198C29.6627 24.814 29.2667 25.6353 29.208 26.662C29.4427 26.5153 29.7507 26.442 30.132 26.442C31.1293 26.442 31.892 26.7206 32.42 27.278C32.9773 27.806 33.256 28.5393 33.256 29.478ZM41.924 29.478C41.924 30.3286 41.6013 31.0766 40.956 31.722C40.34 32.3673 39.5187 32.69 38.492 32.69C37.3187 32.69 36.38 32.2793 35.676 31.458C35.0013 30.6366 34.664 29.6393 34.664 28.466C34.664 26.9993 34.884 25.7673 35.324 24.77C35.7933 23.7433 36.3653 22.922 37.04 22.306C37.7147 21.6606 38.448 21.1913 39.24 20.898C40.032 20.5753 40.78 20.3846 41.484 20.326V23.01C40.6333 23.1566 39.8267 23.5526 39.064 24.198C38.3307 24.814 37.9347 25.6353 37.876 26.662C38.1107 26.5153 38.4187 26.442 38.8 26.442C39.7973 26.442 40.56 26.7206 41.088 27.278C41.6453 27.806 41.924 28.5393 41.924 29.478Z"
        fill="#8B9793" />
      <Path
        d="M108.996 55.6334C108.996 54.7827 109.319 54.0347 109.964 53.3894C110.639 52.7441 111.489 52.4214 112.516 52.4214C113.689 52.4214 114.613 52.8467 115.288 53.6974C115.992 54.5187 116.344 55.5161 116.344 56.6894C116.344 58.1561 116.109 59.4027 115.64 60.4294C115.2 61.4561 114.643 62.3067 113.968 62.9814C113.293 63.6267 112.56 64.1107 111.768 64.4334C110.976 64.7561 110.228 64.9467 109.524 65.0054V62.3214C110.375 62.1747 111.167 61.7787 111.9 61.1334C112.663 60.4587 113.073 59.6081 113.132 58.5814C112.897 58.7281 112.589 58.8014 112.208 58.8014C111.211 58.8014 110.419 58.5081 109.832 57.9214C109.275 57.3347 108.996 56.5721 108.996 55.6334ZM117.62 55.6334C117.62 54.7827 117.943 54.0347 118.588 53.3894C119.263 52.7441 120.113 52.4214 121.14 52.4214C122.313 52.4214 123.237 52.8467 123.912 53.6974C124.616 54.5187 124.968 55.5161 124.968 56.6894C124.968 58.1561 124.733 59.4027 124.264 60.4294C123.824 61.4561 123.267 62.3067 122.592 62.9814C121.917 63.6267 121.184 64.1107 120.392 64.4334C119.6 64.7561 118.852 64.9467 118.148 65.0054V62.3214C118.999 62.1747 119.791 61.7787 120.524 61.1334C121.287 60.4587 121.697 59.6081 121.756 58.5814C121.521 58.7281 121.213 58.8014 120.832 58.8014C119.835 58.8014 119.043 58.5081 118.456 57.9214C117.899 57.3347 117.62 56.5721 117.62 55.6334Z"
        fill="#8B9793" />
    </Svg>
  );
}
