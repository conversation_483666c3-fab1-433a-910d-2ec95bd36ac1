import { useTheme } from '@emotion/react';
import AutocompletePopup from 'components/AutocompletePopup';
import Input from 'components/Input';
import NameField from 'components/NameField';
import PhoneField from 'components/PhoneField';
import { getCountryCodeDisplayedLabel } from 'constants/optionList';
import {
  Button,
  Column,
  PictogramIcon,
  Row,
  TextField,
  Typography,
} from 'cube-ui-components';
import { MAX_PHONE_LENGTH } from 'features/coverageDetails/validation/common/constant';
import { MyEntityFormValues } from 'features/coverageDetails/validation/common/entitySchema';
import { useGetOptionList } from 'hooks/useGetOptionList';
import React from 'react';
import { Control } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { CountryCode } from 'types/optionList';

interface EntityFormProps {
  control: Control<MyEntityFormValues>;
  onSearchLeadButtonPress: () => void;
}

export default function EntityForm({
  control,
  onSearchLeadButtonPress,
}: EntityFormProps) {
  const { space, colors, sizes } = useTheme();
  const { data: optionList } = useGetOptionList();

  const { t } = useTranslation(['coverageDetails', 'common']);

  return (
    <>
      <Row
        alignItems="center"
        bgColor={colors.background}
        pt={space[6]}
        px={space[6]}
        width="100%"
        borderTopLeftRadius={space[4]}
        borderTopRightRadius={space[4]}
        justifyContent="space-between">
        <Row gap={space[2]} alignItems="center">
          <PictogramIcon.Building2 size={sizes[10]} />
          <Typography.H6 fontWeight="bold">
            {t('coverageDetails:formFields.entity.title')}
          </Typography.H6>
        </Row>

        <Button
          variant="secondary"
          text={t('coverageDetails:existingCompany')}
          textStyle={{ paddingHorizontal: space[6] }}
          onPress={onSearchLeadButtonPress}
        />
      </Row>

      <Column
        justifyContent="flex-start"
        w="100%"
        bgColor={colors.palette.white}
        p={space[6]}
        borderBottomLeftRadius={space[4]}
        borderBottomRightRadius={space[4]}
        gap={space[5]}>
        <Row>
          <Input
            style={{
              flex: 1,
            }}
            control={control}
            as={NameField}
            name="name"
            label={t('coverageDetails:formFields.companyName')}
          />
        </Row>

        <Row gap={space[5]}>
          <Column flex={1}>
            <Input
              style={{
                flex: 1,
              }}
              control={control}
              as={NameField}
              name="firstName"
              label={t('coverageDetails:formFields.representativeName')}
            />
          </Column>
          <Column flex={1}>
            <Row>
              <Input
                control={control}
                as={AutocompletePopup<CountryCode, string>}
                name="code"
                data={optionList?.COUNTRY_CODE.options || []}
                getItemValue={item => item.value}
                getItemLabel={item => item.label}
                getDisplayedLabel={item => getCountryCodeDisplayedLabel(item)}
                disabled={true}
                label={t('coverageDetails:formFields.phoneCode')}
                style={{ width: space[27], marginRight: space[3] }}
              />
              <Input
                style={{
                  flex: 1,
                }}
                control={control}
                as={PhoneField}
                name="phoneMobile"
                maxLength={MAX_PHONE_LENGTH}
                label={t('coverageDetails:formFields.businessPhoneNumber')}
                keyboardType="number-pad"
                returnKeyType="done"
                size={'large'}
              />
            </Row>
          </Column>

          <Column flex={1}>
            <Input
              style={{
                flex: 1,
              }}
              control={control}
              as={TextField}
              name="email"
              label={t('coverageDetails:formFields.email')}
            />
          </Column>
        </Row>
      </Column>
    </>
  );
}
