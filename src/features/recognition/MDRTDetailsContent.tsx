import { useTheme } from '@emotion/react';
import { Typography } from 'cube-ui-components';
import React from 'react';
import { View } from 'react-native';
import RecognitionDetailsAchievement from './components/RecognitionDetailsAchievement';
import { useTranslation } from 'react-i18next';
import HistoricalAchievementBox from './ib/tablet/HistoricalAchievementBox';
import AchievementBar from './ib/AchievementBar';
import { RecognitionCategoryType } from './RecognitionDetailsContent';
import { MdrtDetailsDataItem, MdrtRankData } from './type';
import { country } from 'utils/context';
import CurrentRankSection from './ib/CurrentRankSection';
import { TeamIndividualMDRT } from 'types/performance';
import RecognitionDetailsTiersV2 from './components/RecognitionDetailsTiersV2';
import { getFIBMDRTTiersConfig } from './components/MDRTTiersConfigs';
import RecognitionDetailsRequirementTablet from './components/tablet/RecognitionDetailsRequirementTablet';
import { mdrtRequirementConfigHandler } from './components/tablet/RecognitionDetailsContentTablet';
import { useGetAllPerformance } from 'hooks/useGetPerformance';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';

const IS_PH = country === 'ph';

export default function MDRTDetailsContent({
  isAtTopTier,
  category,
  mdrtDetailsData,
  teamMdrtData,
  activeYear,
  title,
  isHistoricalAchievement,
}: {
  isAtTopTier: boolean;
  category: RecognitionCategoryType;
  mdrtDetailsData: MdrtDetailsDataItem & MdrtRankData;
  teamMdrtData: TeamIndividualMDRT | undefined;
  activeYear: number;
  title: string;
  isHistoricalAchievement: boolean;
}) {
  const { t } = useTranslation('performance');
  const theme = useTheme();
  const { sizes } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();

  const { data } = useGetAllPerformance();

  const percent = mdrtDetailsData?.percent ?? 0;
  const agentRank = mdrtDetailsData.rank;
  const totalActiveAgents = mdrtDetailsData.totalAgent;
  const isRankShown = agentRank != totalActiveAgents && Boolean(percent);
  const nextYear = activeYear + 1;

  return (
    <>
      <RecognitionDetailsAchievement percent={mdrtDetailsData?.percent}>
        <View style={{ gap: sizes[1], marginBottom: sizes[4] }}>
          <Typography.H7 fontWeight={'bold'}>
            {t('performance.recognition.overAllAchievementWithYear', {
              year: activeYear,
            })}
          </Typography.H7>
        </View>

        {isHistoricalAchievement ? (
          <HistoricalAchievementBox completion={mdrtDetailsData.completion} />
        ) : (
          <AchievementBar
            percent={percent}
            completion={mdrtDetailsData.completion}
            shortfall={mdrtDetailsData.shortfall}
            target={mdrtDetailsData.target}
          />
        )}

        {/* DMS not ready */}
        {/* {country === 'ib' &&
          !isHistoricalAchievement &&
          mdrtDetailsData?.completion > 0 && (
            <CurrentRankSection
              isRankShown={isRankShown}
              agentRank={agentRank}
              totalActiveAgents={totalActiveAgents}
            />
          )} */}
      </RecognitionDetailsAchievement>

      <RecognitionDetailsTiersV2
        tiersArray={
          getFIBMDRTTiersConfig({ teamMdrtData, title, percent })
            .map(item => ({
              ...item,
              isInProgress:
                Boolean(teamMdrtData?.mdrt?.fypCompleted) &&
                !isHistoricalAchievement,
            }))
            .filter(item => !(IS_PH ? item?.title === 'FWDElite' : false)) // filter out FWDElite for PH
        }
        activeYear={nextYear}
      />

      {IS_PH && (
        <RecognitionDetailsRequirementTablet
          MDRTRequirementConfig={mdrtRequirementConfigHandler({
            data,
            isTabletMode,
            theme,
          })}
        />
      )}
    </>
  );
}
