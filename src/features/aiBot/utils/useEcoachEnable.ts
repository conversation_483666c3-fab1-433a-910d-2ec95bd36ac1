import useBoundStore from 'hooks/useBoundStore';
import { country } from 'utils/context';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { moduleConfigs } from 'utils/config/module';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { CubeIdToken } from 'types';
import { jwtDecode } from 'jwt-decode';

export const useEcoachEnable = () => {
  // Checking logic from agent profile api
  const { isTabletMode } = useLayoutAdoptionCheck();
  const channel = useGetCubeChannel();
  const idToken = useBoundStore.getState().auth.authInfo?.idToken;
  const productConfig = useBoundStore(state => state.ecoach.productConfig);
  const config = moduleConfigs[country]?.ecoachConfig[channel];
  // Checking logic for token and FE config
  if (!idToken) return false;

  // Checking logic from token (controlled by IDP)
  const { trainer_guru_enabled } = jwtDecode(String(idToken)) as CubeIdToken;
  if (!trainer_guru_enabled) return false;

  // Only show TG card if configuration data is loaded
  if (!productConfig) return false;

  if (isTabletMode) {
    return config?.tablet;
  } else {
    return config?.mobile;
  }
};
