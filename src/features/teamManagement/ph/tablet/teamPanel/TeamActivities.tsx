import React from 'react';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Column, H6, H7, Icon, Row } from 'cube-ui-components';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import RowSeparator from 'components/Table/RowSeparator';
// import IconData from 'features/teamManagement/assets/IconData';
// import IconConversion from 'features/teamManagement/assets/IconConversion';
import IconTarget from 'features/teamManagement/assets/IconTarget';
import { RootStackParamList } from 'types';

export default function TeamActivities() {
  const { space } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const ACTIVITY_TABS = [
    // {
    //   type: 'viewOperationData',
    //   label: 'View operation data',
    //   icon: <IconData />,
    //   onPress: () => console.log('Press View operation data'),
    // },
    // {
    //   type: 'leadsConversion',
    //   label: 'Leads conversion',
    //   icon: <IconConversion />,
    //   onPress: () => console.log('Press Leads conversion'),
    // },
    {
      type: 'teamTarget',
      label: 'Team target',
      icon: <IconTarget />,
      onPress: () => navigation.navigate('TeamTarget'),
    },
  ];

  return (
    <Column gap={space[5]}>
      <H6 fontWeight="bold" children={'Team activities'} />
      <Container>
        {ACTIVITY_TABS.map(({ type, label, icon, onPress }, index) => {
          return (
            <>
              <TabContainer key={type} onPress={onPress}>
                <Row gap={space[4]} alignItems="center">
                  {icon}
                  <H7 fontWeight="bold" children={label} />
                </Row>
                <Icon.ChevronRight />
              </TabContainer>

              {ACTIVITY_TABS.length - 1 > index && (
                <RowSeparator style={{ marginVertical: space[2] }} />
              )}
            </>
          );
        })}
      </Container>
    </Column>
  );
}

const Container = styled(Column)(({ theme }) => ({
  padding: theme.space[4],
  borderRadius: theme.borderRadius.large,
  backgroundColor: theme.colors.background,
}));

const TabContainer = styled.TouchableOpacity(({ theme }) => ({
  flexDirection: 'row',
  paddingVertical: theme.space[2],
  justifyContent: 'space-between',
}));
