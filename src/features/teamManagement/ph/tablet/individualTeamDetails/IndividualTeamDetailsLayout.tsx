import { TouchableOpacity } from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import { Column, H6, Icon, Row } from 'cube-ui-components';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { useTranslation } from 'react-i18next';
import Overview from './Overview';
import Contribution from './Contribution';
import Ranking from './Ranking';
import Achievement from './Achievement';
import { useTheme } from '@emotion/react';
import SearchableDropdown from 'components/SearchableDropdown';
import AppTopTabBar from 'components/AppTopTabBar';
import { useGetTeamMainByTeam } from 'hooks/useGetTeam';
import useBoundStore from 'hooks/useBoundStore';
import { AgentAchievements } from 'types/team';
import { agentRoleCategories } from 'features/teamManagement/types';
import TeamManagementContext from 'features/teamManagement/context/TeamManagementContext';
import ViewShot from 'react-native-view-shot';
import {
  onCaptureAndDownload,
  onShare,
} from 'features/teamManagement/Utils/DownloadSnapShotUtils';
import { build } from 'utils/context';

const Tab = createMaterialTopTabNavigator();

export default function IndividualTeamDetailsLayout() {
  const { t } = useTranslation('teamManagement');
  const { colors, space, sizes } = useTheme();

  const agentCode = useBoundStore().auth.agentCode;
  const { data: teamData } = useGetTeamMainByTeam(true, agentCode ?? '');

  const viewShotRefArray = new Array<React.RefObject<ViewShot>>();

  const [teamLeaderSelected, setTeamLeaderSelected] = useState<string>('');
  const [topTabIndex, setTopTabIndex] = useState<number>(0);
  const [leaderList, setLeaderList] = useState<
    { value: string; label: string }[]
  >([]);

  useEffect(() => {
    if (teamData && Object.keys(teamData)?.length > 0) {
      const leaders: { value: string; label: string }[] = [];
      agentRoleCategories.forEach(level => {
        if (level in teamData) {
          leaders.push(
            teamData[level as keyof typeof teamData]?.map(
              (leaderData: AgentAchievements) => ({
                label: t('teamView.agentsTeam', {
                  agent: leaderData?.agentName,
                }),
                value: leaderData?.agentId,
              }),
            ),
          );
        }
      });
      const leaderList = leaders?.flat();
      setLeaderList(leaderList);
      setTeamLeaderSelected(leaderList[0]?.value);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [teamData]);

  const TABS_CONFIG = [
    {
      type: 'overview',
      name: t('teamView.overview'),
      Component: Overview,
    },
    {
      type: 'contribution',
      name: t('teamView.contribution'),
      Component: Contribution,
    },
    {
      type: 'ranking',
      name: t('teamView.ranking'),
      Component: Ranking,
    },
    {
      type: 'achievement',
      name: t('teamView.achievement'),
      Component: Achievement,
    },
  ];

  return (
    <Column flex={1}>
      <Column gap={space[4]} marginBottom={space[4]}>
        <Row justifyContent="space-between" alignItems="center">
          <H6 color={colors.secondary} fontWeight="bold">
            {t('teamView.title')}
          </H6>
          <Row gap={space[2]}>
            <TouchableOpacity
              onPress={() =>
                onCaptureAndDownload(viewShotRefArray, topTabIndex, t)
              }>
              <Icon.Download
                width={sizes[6]}
                height={sizes[6]}
                fill={colors.secondary}
              />
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => onShare(viewShotRefArray, topTabIndex)}>
              <Icon.Share
                width={sizes[6]}
                height={sizes[6]}
                fill={colors.secondary}
              />
            </TouchableOpacity>
          </Row>
        </Row>

        <SearchableDropdown<{ value: string; label: string }, string>
          label={t('teamView.selectedTeam')}
          data={leaderList ?? []}
          getItemValue={item => item.value}
          getItemLabel={item => item.label}
          getDisplayedLabel={item => item.label}
          value={teamLeaderSelected}
          onChange={(value: string) => setTeamLeaderSelected(value)}
          isModal
          dropDownModalStyle={{ height: '80%' }}
        />
      </Column>

      <Column flex={1}>
        <Column flex={1}>
          <TeamManagementContext.Provider
            value={{ selectedAgentId: teamLeaderSelected }}>
            <Tab.Navigator
              tabBar={props => (
                <AppTopTabBar
                  {...props}
                  onPressCallBack={routeName =>
                    setTopTabIndex(
                      TABS_CONFIG?.findIndex(tab => tab.name === routeName),
                    )
                  }
                  tabStyle={{
                    paddingTop: 10,
                    paddingBottom: 6,
                    height: sizes[10],
                  }}
                  variant="square"
                />
              )}>
              {TABS_CONFIG?.map(({ type, name, Component }) => {
                const viewShotRef = useRef<ViewShot>(null);
                viewShotRefArray.push(viewShotRef);
                return (
                  <Tab.Screen
                    key={type}
                    name={name}
                    children={() => (
                      <ViewShot
                        style={{ flex: 1 }}
                        ref={viewShotRef}
                        options={{
                          fileName: 'team-performance',
                          format: 'jpg',
                          quality: 1.0,
                        }}>
                        <Component />
                      </ViewShot>
                    )}
                  />
                );
              })}
            </Tab.Navigator>
          </TeamManagementContext.Provider>
        </Column>
      </Column>
    </Column>
  );
}
