import React from 'react';
import { Column, Label, LargeLabel, Row } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';

export default function KPISection({
  title,
  date,
  teamSales,
  cases,
}: {
  title: string;
  date: string;
  teamSales: number | string;
  cases: number | string;
}) {
  const { t } = useTranslation('teamManagement');
  const { colors, space } = useTheme();
  return (
    <Column gap={space[4]}>
      <Column gap={space[2]}>
        <LargeLabel fontWeight="bold" color={colors.secondary}>
          {title}
        </LargeLabel>
        <Label color={colors.palette.fwdGreyDarker}>{date}</Label>
      </Column>
      <Column>
        <Row backgroundColor={colors.palette.fwdGrey[20]}>
          <DescriptionContainer />
          <DescriptionContainer>
            <Label color={colors.palette.fwdGreyDarkest}>{t('issued')}</Label>
          </DescriptionContainer>
        </Row>
        <Row>
          <DescriptionContainer>
            <Label color={colors.palette.fwdGreyDarker}>
              {t('teamView.teamSalesAPE')}
            </Label>
          </DescriptionContainer>
          <DescriptionContainer>
            <Label color={colors.secondary}>{teamSales}</Label>
          </DescriptionContainer>
        </Row>
        <Seperator />
        <Row>
          <DescriptionContainer>
            <Label color={colors.palette.fwdGreyDarker}>
              {t('teamView.noOfCases')}
            </Label>
          </DescriptionContainer>
          <DescriptionContainer>
            <Label color={colors.secondary}>{cases}</Label>
          </DescriptionContainer>
        </Row>
      </Column>
    </Column>
  );
}

const DescriptionContainer = styled(Column)(({ theme }) => ({
  flex: 1,
  padding: theme.space[2],
}));

const Seperator = styled(Column)(({ theme }) => ({
  height: 1,
  backgroundColor: theme.colors.palette.fwdGrey[100],
}));
