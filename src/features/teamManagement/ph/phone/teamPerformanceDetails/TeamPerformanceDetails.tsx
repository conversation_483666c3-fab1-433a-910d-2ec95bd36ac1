import { useTheme } from '@emotion/react';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import { RouteProp, useRoute } from '@react-navigation/native';
import AppTopTabBar from 'components/AppTopTabBar';
import {
  addToast,
  Column,
  Icon,
  LoadingIndicator,
  Row,
} from 'cube-ui-components';
import * as ImagePicker from 'expo-image-picker';
import * as Linking from 'expo-linking';
import * as MediaLibrary from 'expo-media-library';
import * as Sharing from 'expo-sharing';
import TeamManagementContext from 'features/teamManagement/context/TeamManagementContext';
import CustomHeaderBackButton from 'navigation/components/HeaderBackButton';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import React, { useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { Alert, Platform, Share, TouchableOpacity } from 'react-native';
import ViewShot from 'react-native-view-shot';
import AchievementTab from './tabs/Achievement';
import ContributionTab from './tabs/Contribution';
import OverviewTab from './tabs/Overview';
import RankingTab from './tabs/Ranking';
import { TeamPerformanceParamList } from '/types/navigation';

const Tab = createMaterialTopTabNavigator();
let topTabIndex = 0;

export default function TeamPerformanceDetails() {
  const { params } =
    useRoute<RouteProp<TeamPerformanceParamList, 'TeamView'>>();

  const { t } = useTranslation('teamManagement');
  const { data } = params;
  const { colors, space } = useTheme();
  const viewShotRefArray = new Array<React.RefObject<ViewShot>>();

  const setTopTabIndex = (_topTabIndex: number) => {
    topTabIndex = _topTabIndex;
  };

  const onCaptureAndDownload = async (
    viewShotRefArray: Array<React.RefObject<ViewShot>>,
  ) => {
    const onOpenSettings = () => {
      Alert.alert(t('downloadReport.title'), t('downloadReport.message'), [
        {
          text: t('downloadReport.cancel'),
          style: 'cancel',
        },
        {
          text: t('downloadReport.settings'),
          onPress: () => {
            if (Platform.OS === 'ios') {
              Linking.openURL('app-settings:');
            } else {
              Linking.openSettings();
            }
          },
        },
      ]);
    };

    try {
      const uri = await viewShotRefArray[topTabIndex].current?.capture?.();
      if (uri) {
        try {
          if (Platform.OS === 'ios') {
            const permission =
              await ImagePicker.getMediaLibraryPermissionsAsync(true);
            if (!permission.granted) {
              const { status } = permission;
              const reqPermission =
                await ImagePicker.requestMediaLibraryPermissionsAsync(true);
              if (!reqPermission.granted) {
                if (status === 'denied') {
                  onOpenSettings();
                }
                return;
              }
            }
          } else {
            const permission = await MediaLibrary.getPermissionsAsync();
            const reqPermission = await MediaLibrary.requestPermissionsAsync();
            const { canAskAgain } = permission;
            const { status } = reqPermission;
            if (status === 'denied') {
              if (!canAskAgain) {
                onOpenSettings();
              }
              return;
            }
          }
          addToast([
            {
              message: t('teamView.downloading'),
              IconLeft: LoadingIndicator,
            },
          ]);
          await MediaLibrary.saveToLibraryAsync(uri);
          addToast([
            {
              message: t('teamView.downloadedSuccessful'),
              IconLeft: <Icon.Tick />,
            },
          ]);
        } catch (error) {
          console.log(error);
        }
      }
    } catch (error) {
      console.error('Snapshot failed', error);
      return;
    }
  };

  const onShare = async () => {
    const uri = await viewShotRefArray[topTabIndex].current?.capture?.();
    if (uri) {
      if (Platform.OS == 'android') {
        await Sharing.shareAsync('file://' + uri);
      } else if (Platform.OS == 'ios') {
        await Share.share(
          { url: 'file://' + uri },
          {
            dialogTitle: data.title,
          },
        );
      }
    }
  };

  const TABS_CONFIG = [
    {
      type: 'overview',
      name: t('teamView.overview'),
      component: OverviewTab,
    },
    {
      type: 'contribution',
      name: t('teamView.contribution'),
      component: ContributionTab,
    },
    {
      type: 'ranking',
      name: t('teamView.ranking'),
      component: RankingTab,
    },
    {
      type: 'achievement',
      name: t('teamView.achievement'),
      component: AchievementTab,
    },
  ];

  return (
    <Column flex={1}>
      <ScreenHeader
        customTitle={data?.agentName}
        leftChildren={<CustomHeaderBackButton />}
        rightChildren={
          <Row gap={space[4]}>
            <TouchableOpacity
              onPress={() => onCaptureAndDownload(viewShotRefArray)}>
              <Icon.Download fill={colors.secondary} />
            </TouchableOpacity>
            <TouchableOpacity onPress={onShare}>
              <Icon.Share fill={colors.secondary} />
            </TouchableOpacity>
          </Row>
        }
        route={'TeamView'}
        showBottomSeparator={false}
      />

      <TeamManagementContext.Provider value={{ selectedAgentId: data.agentId }}>
        <Tab.Navigator
          tabBar={props => <AppTopTabBar {...props} variant="scrollable" />}>
          {TABS_CONFIG.map(props => {
            const viewShotRef = useRef<ViewShot>(null);
            viewShotRefArray.push(viewShotRef);
            return (
              <Tab.Screen
                key={props?.type}
                name={props?.name}
                component={props?.component}
                initialParams={{ viewShotRef, setTopTabIndex }}
              />
            );
          })}
        </Tab.Navigator>
      </TeamManagementContext.Provider>
    </Column>
  );
}
