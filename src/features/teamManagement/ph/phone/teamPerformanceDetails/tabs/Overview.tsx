import { TouchableOpacity, View, ViewProps } from 'react-native';
import React, { useContext, useState } from 'react';
import styled from '@emotion/native';
import { Column, Icon, Row, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { ScrollView } from 'react-native';
import { useTranslation } from 'react-i18next';
import { ProgressGauge } from 'components/Chart';
import { formatCurrency } from 'utils';
import {
  useGetTeamBranchByAgentId,
  useGetTeamOverviewByAgentId,
} from 'hooks/useGetTeam';
import ViewShot from 'react-native-view-shot';
import {
  NavigationProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { TeamPerformanceParamList } from 'types';
import ResponsiveView from 'components/ResponsiveView';
import numberToKFormat from 'utils/helper/numberToKFormat';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import ListMemberActionPanel from 'features/teamManagement/components/ListMemberActionPanel';
import TeamManagementContext from 'features/teamManagement/context/TeamManagementContext';

export default function OverviewTab() {
  const { colors, space } = useTheme();
  const { t } = useTranslation('teamManagement');
  const navigation = useNavigation<NavigationProp<TeamPerformanceParamList>>();

  const {
    params: { viewShotRef, setTopTabIndex },
  } = useRoute();
  setTopTabIndex(navigation.getState().index);

  const [isShowListMemberBS, setIsShowListMemberBS] = useState<boolean>(false);
  const { selectedAgentId } = useContext(TeamManagementContext);
  const { data: agentTeamBranch } = useGetTeamBranchByAgentId(selectedAgentId);
  const { data: overviewData } = useGetTeamOverviewByAgentId(selectedAgentId);

  const completedPercent =
    overviewData?.target && overviewData?.shortFall
      ? 100 -
        parseInt(`${(overviewData?.shortFall / overviewData?.target) * 100}`)
      : 0;

  const completedAPE =
    overviewData?.target && overviewData?.shortFall
      ? overviewData?.target - overviewData?.shortFall
      : 0;

  const renderCompletedPercent =
    completedPercent > 100 ? 100 : completedPercent;

  const renderShortfall =
    overviewData && overviewData?.shortFall >= 0 ? overviewData?.shortFall : 0;

  const topSectionAsOfDate = overviewData?.asOfDate
    ? `  |  ${t('asOf')} ${dateFormatUtil(overviewData?.asOfDate)}`
    : '  |  --';

  const teammatesNumber =
    (agentTeamBranch?.downlineAgents &&
      agentTeamBranch?.downlineAgents?.length > 0 &&
      agentTeamBranch?.downlineAgents?.length) ||
    '0';

  return (
    <ScrollView
      style={{ padding: space[4], backgroundColor: colors.surface }}
      contentContainerStyle={{ paddingBottom: space[8] }}>
      <ViewShotContainer
        ref={viewShotRef}
        options={{
          fileName: 'team-performance',
          format: 'jpg',
          quality: 1.0,
        }}>
        <SectionContainer>
          <Row justifyContent="space-between" alignItems="flex-start">
            <TeamTitle
              fontWeight="bold"
              children={overviewData?.displayName?.en || '--'}
            />
            <TouchableOpacity onPress={() => setIsShowListMemberBS(true)}>
              <Row alignItems="center" gap={space[1]}>
                <Icon.Team />
                <Typography.H8
                  fontWeight="bold"
                  children={t('teamView.view')}
                  color={colors.primary}
                />
              </Row>
            </TouchableOpacity>
          </Row>
          <RowDescription>
            <Description
              children={`${teammatesNumber} ${t(
                'teamView.teammates',
              )}${topSectionAsOfDate}`}
            />
          </RowDescription>
          <TeamChartContainer>
            <ProgressGauge
              percent={renderCompletedPercent}
              style={{ width: '100%', height: '100%' }}
              padding={0}
              strokeWidth={10}
              color={
                renderCompletedPercent === 100
                  ? colors.palette.alertGreen
                  : colors.primary
              }
              center={
                <Column alignItems="center" gap={space[1]}>
                  <PercentBox isCompleted={renderCompletedPercent === 100}>
                    <Typography.SmallBody
                      children={`${renderCompletedPercent}% ${t(
                        'teamView.done',
                      )}`}
                      color={colors.palette.white}
                    />
                  </PercentBox>
                  <Typography.LargeLabel
                    fontWeight="bold"
                    children={completedAPE ? formatCurrency(completedAPE) : 0}
                  />
                  <Typography.SmallLabel children={t('completedAPE')} />
                </Column>
              }
            />
          </TeamChartContainer>
          <Column gap={space[2]}>
            <ChartDescription
              children={`Target: ${
                overviewData?.target ? formatCurrency(overviewData?.target) : 0
              }`}
            />
            <ChartDescription
              children={`Shortfall: ${formatCurrency(renderShortfall)}`}
            />
          </Column>
        </SectionContainer>

        <SectionContainer>
          <TeamTitle fontWeight="bold" children={t('teamView.yearToDateKPI')} />
          <RowDescription>
            <Description
              children={
                overviewData?.asOfDate
                  ? dateFormatUtil(overviewData?.asOfDate)
                  : '--'
              }
            />
          </RowDescription>
          <TargetDataContainer>
            <TitleRow>
              <DataTitle />
              <DataTitle>{t('issued')}</DataTitle>
            </TitleRow>
            <DataRow>
              <DataTitle>{t('teamView.teamSalesAPE')}</DataTitle>
              <DataValue>
                {overviewData
                  ? numberToKFormat(overviewData?.branchYTDAPECompletion ?? 0)
                  : 0}
              </DataValue>
            </DataRow>
            <SeparateLine />
            <DataRow>
              <DataTitle>{t('teamView.noOfCases')}</DataTitle>
              <DataValue>
                {overviewData
                  ? numberToKFormat(overviewData?.branchYTDCASECompletion ?? 0)
                  : 0}
              </DataValue>
            </DataRow>
          </TargetDataContainer>
        </SectionContainer>

        <SectionContainer>
          <TeamTitle
            fontWeight="bold"
            children={t('teamView.monthToDateKPI')}
          />
          <RowDescription>
            <Description
              children={
                overviewData?.asOfDate
                  ? dateFormatUtil(overviewData?.asOfDate)
                  : '--'
              }
            />
          </RowDescription>
          <TargetDataContainer>
            <TitleRow>
              <DataTitle />
              <DataTitle>{t('issued')}</DataTitle>
            </TitleRow>
            <DataRow>
              <DataTitle>{t('teamView.teamSalesAPE')}</DataTitle>
              <DataValue>
                {overviewData
                  ? numberToKFormat(overviewData?.branchMTDAPECompletion ?? 0)
                  : 0}
              </DataValue>
            </DataRow>
            <SeparateLine />
            <DataRow>
              <DataTitle>{t('teamView.noOfCases')}</DataTitle>
              <DataValue>
                {overviewData
                  ? numberToKFormat(overviewData?.branchMTDCASECompletion ?? 0)
                  : 0}
              </DataValue>
            </DataRow>
          </TargetDataContainer>
        </SectionContainer>

        {agentTeamBranch && (
          <ListMemberActionPanel
            isVisible={isShowListMemberBS}
            onClose={() => setIsShowListMemberBS(false)}
            disabled={true}
            data={agentTeamBranch}
          />
        )}
      </ViewShotContainer>
    </ScrollView>
  );
}

const TitleRow = styled(Row)(({ theme }) => ({
  backgroundColor: theme.colors.palette.fwdGrey[20],
  padding: theme.space[2],
}));

const DataRow = styled(Row)(({ theme }) => ({
  padding: theme.space[2],
}));

const SeparateLine = styled(View)(({ theme }) => ({
  height: 1,
  backgroundColor: theme.colors.palette.fwdGrey[100],
}));

const DataTitle = styled(Typography.H8)(({ theme }) => ({
  flex: 1,
  color: theme.colors.palette.fwdGreyDarkest,
}));

const DataValue = styled(Typography.H8)(({ theme }) => ({
  flex: 1,
  color: theme.colors.palette.fwdDarkGreen[100],
}));

const TargetDataContainer = styled(View)(({ theme }) => ({
  marginVertical: theme.space[2],
}));

const SectionContainer = styled(ResponsiveView)(({ theme }) => ({
  backgroundColor: theme.colors.background,
  padding: theme.space[4],
  borderRadius: theme.space[4],
  marginBottom: theme.space[4],
}));

const TeamTitle = styled(Typography.H7)(() => ({
  maxWidth: '70%',
}));

const ChartDescription = styled(Typography.Label)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
}));

const Description = styled(Typography.H8)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarker,
}));

const TeamChartContainer = styled(View)(({ theme }) => ({
  alignItems: 'center',
  justifyContent: 'space-between',
  marginTop: theme.space[3],
  width: '100%',
  height: theme.space[38],
}));

const RowDescription = styled(Row)(({ theme }) => ({
  marginVertical: theme.space[1],
}));

const PercentBox = styled(View)<ViewProps & { isCompleted: boolean }>(
  ({ theme, isCompleted }) => ({
    backgroundColor: isCompleted
      ? theme.colors.palette.alertGreen
      : theme.colors.primary,
    borderRadius: 2,
    paddingVertical: theme.space[1],
    paddingHorizontal: theme.space[2],
    alignItems: 'center',
  }),
);

const ViewShotContainer = styled(ViewShot)(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.colors.surface,
}));
