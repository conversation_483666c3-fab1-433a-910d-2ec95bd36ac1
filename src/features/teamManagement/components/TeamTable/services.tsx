import { Icon, SvgIconProps } from 'cube-ui-components';
import {
  Mapa,
  SalesBalancePerformance,
  TeamHierarchyPerformance,
} from 'types/team';
import { TimePeriod } from '../TimePeriodTabs';
import numberToKFormat from 'utils/helper/numberToKFormat';
import AvatarPlaceholderSVG from 'features/home/<USER>/AvatarPlaceholderSVG';
import {
  isNullOrUndefined,
  sortAndAssignMedals,
  TeamComparisonChartViewYAxisInfo,
  TeamComparisonTabs,
} from 'features/teamManagement/Utils/utils';
import { cloneDeep } from 'utils/helper/objectUtil';
import { DataConfig, TableColumnType } from './type';
import { build } from 'utils/context';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';

type TeamTableAgentPerformance = {
  text: string | number;
  type?: TableColumnType;
}[];

export type Stat = {
  highestApeSubmmited: number;
  lowestApeSubmmited: number;
  highestApeIssued: number;
  lowestApeIssued: number;
  highestCaseSubmitted: number;
  lowestCaseSubmitted: number;
  highestCaseIssued: number;
  lowestCaseIssued: number;
  highest1stYearPersistency: number;
  lowest1stYearPersistency: number;
  highest2ndYearPersistency: number;
  lowest2ndYearPersistency: number;
};

export const transformPersistencyData = (
  agent: SalesBalancePerformance,
  isExpandedList: boolean,
) => {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const isAgentHasMember = agent?.members && agent.members?.length > 0;
  const teamOrIndvidualPerformance = isAgentHasMember
    ? 'teamPerformance'
    : 'individualPerformance';
  const MONTH_13_BENCHMARK = 90;
  const MONTH_25_BENCHMARK = 80;
  const MONTH_12_BENCHMARK = 90;
  const MONTH_24_BENCHMARK = 80;

  const resultAgent: DataConfig = {
    id: '',
    agentName: '',
    data: [],
    members: [],
    onPress: undefined,
  };
  resultAgent.id = agent.agentCode;
  const displayName =
    agent.designation && agent.designation !== 'AGT'
      ? `${agent.agentName} (${agent.designation})`
      : agent.agentName;
  resultAgent.agentName = displayName;

  const teamColumn: TeamTableAgentPerformance[] = [
    {
      text: displayName,
      type: !isExpandedList
        ? TableColumnType.leaderIcon
        : TableColumnType.index,
    },
  ];
  resultAgent.data.push(teamColumn);
  const rollingData = agent?.[teamOrIndvidualPerformance]?.persistency?.rolling;
  const month13Column = [
    {
      text: isNullOrUndefined(rollingData?.month13)
        ? '--'
        : `${rollingData?.month13}%`,

      type:
        !isNullOrUndefined(rollingData?.month13) &&
        rollingData?.month13 < MONTH_13_BENCHMARK
          ? TableColumnType.highlighted
          : TableColumnType.text,
    },
  ];
  resultAgent.data.push(month13Column);

  const month25Column = [
    {
      text: isNullOrUndefined(rollingData?.month25)
        ? '--'
        : `${rollingData?.month25}%`,
      type:
        !isNullOrUndefined(rollingData?.month25) &&
        rollingData?.month25 < MONTH_25_BENCHMARK
          ? TableColumnType.highlighted
          : TableColumnType.text,
    },
  ];
  resultAgent.data.push(month25Column);

  const month12Column = [
    {
      text:
        isAgentHasMember || isNullOrUndefined(rollingData?.month12)
          ? '--'
          : `${rollingData?.month12}%`,

      type:
        !isNullOrUndefined(rollingData?.month12) &&
        rollingData?.month12 < MONTH_12_BENCHMARK
          ? TableColumnType.highlighted
          : TableColumnType.text,
    },
  ];

  resultAgent.data.push(month12Column);

  const month24Column = [
    {
      text:
        isAgentHasMember || isNullOrUndefined(rollingData?.month24)
          ? '--'
          : `${rollingData?.month24 ?? 0}%`,

      type:
        !isNullOrUndefined(rollingData?.month24) &&
        rollingData?.month24 < MONTH_24_BENCHMARK
          ? TableColumnType.highlighted
          : TableColumnType.text,
    },
  ];
  resultAgent.data.push(month24Column);

  const viewPoliciesColumn = [
    {
      text: ' ',
      type: !isAgentHasMember
        ? TableColumnType.viewPolicies
        : isAgentHasMember
        ? TableColumnType.cheveron
        : TableColumnType.text,
    },
  ];
  resultAgent.data.push(viewPoliciesColumn);

  if (!isAgentHasMember) {
    resultAgent.onPress = () =>
      navigation.navigate('TeamIndividualPersistency', {
        agentCode: agent?.agentCode,
        agentName: agent?.agentName,
      }); //TODO: change to persistency page
  }
  return resultAgent;
};

const complimentType = (
  currentValue?: number,
  maxValue?: number,
  minValue?: number,
  isLeader?: boolean,
) => {
  if (isLeader || !currentValue) return {}; //if data is 0, show no highlight

  return currentValue === maxValue
    ? TableColumnType.compliment
    : currentValue === minValue
    ? TableColumnType.fail
    : TableColumnType.text;
};

const transformSalesMemberData = (
  agent: SalesBalancePerformance,
  timePeriodKey: 'mtd' | 'ytd',
  isLeader: boolean,
  stat?: Stat,
) => {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const isAgentHasMember = agent?.members && agent.members?.length > 0;

  const teamOrIndvidualPerformance = isAgentHasMember
    ? 'teamPerformance'
    : 'individualPerformance';
  const resultAgent: DataConfig = {
    id: '',
    agentName: '',
    data: [],
    members: [],
    onPress: undefined,
  };

  resultAgent.id = agent.agentCode;
  const displayName =
    agent.designation && agent.designation !== 'AGT' && isLeader
      ? `${agent.agentName} (${agent.designation})`
      : agent.agentName;
  resultAgent.agentName = displayName;
  const teamColumn: TeamTableAgentPerformance[] = [
    {
      text: displayName,
      type: isLeader ? TableColumnType.leaderIcon : TableColumnType.index,
    },
  ];
  resultAgent.data.push(teamColumn);

  const apeColumn = [
    {
      text: numberToKFormat(
        agent?.[teamOrIndvidualPerformance]?.[timePeriodKey]?.issuedApe ?? 0,
      ),
      type: complimentType(
        agent?.[teamOrIndvidualPerformance]?.[timePeriodKey]?.issuedApe,
        stat?.highestApeIssued,
        stat?.lowestApeIssued,
        isLeader,
      ),
    },
    {
      text: numberToKFormat(
        agent?.[teamOrIndvidualPerformance]?.[timePeriodKey].submittedApe ?? 0,
      ),
      type: complimentType(
        agent?.[teamOrIndvidualPerformance]?.[timePeriodKey]?.submittedApe,
        stat?.highestApeSubmmited,
        stat?.lowestApeSubmmited,
        isLeader,
      ),
    },
  ];
  resultAgent.data.push(apeColumn);

  const caseColumn = [
    {
      text: numberToKFormat(
        agent?.[teamOrIndvidualPerformance]?.[timePeriodKey].issuedCase ?? 0,
      ),
      type: complimentType(
        agent?.[teamOrIndvidualPerformance]?.[timePeriodKey].issuedCase,
        stat?.highestCaseIssued,
        stat?.lowestCaseIssued,
        isLeader,
      ),
    },
    {
      text: numberToKFormat(
        agent?.[teamOrIndvidualPerformance]?.[timePeriodKey].submittedCase ?? 0,
      ),
      type: complimentType(
        agent?.[teamOrIndvidualPerformance]?.[timePeriodKey].submittedCase,
        stat?.highestCaseSubmitted,
        stat?.lowestCaseSubmitted,
        isLeader,
      ),
    },
  ];
  resultAgent.data.push(caseColumn);

  const firstYearPersistency = agent?.[teamOrIndvidualPerformance]?.persistency
    .firstYear
    ? `${agent?.[teamOrIndvidualPerformance]?.persistency.firstYear ?? 0}%`
    : '--';
  const secondYearPersistency = agent?.[teamOrIndvidualPerformance]?.persistency
    .secondYear
    ? `${agent?.[teamOrIndvidualPerformance]?.persistency.secondYear ?? 0}%`
    : '--';

  const persistencyColumn = [
    {
      text: firstYearPersistency,
      type: complimentType(
        agent?.[teamOrIndvidualPerformance]?.persistency.firstYear,
        stat?.highest1stYearPersistency,
        stat?.lowest1stYearPersistency,
        isLeader,
      ),
    },
    {
      text: secondYearPersistency,
      type: complimentType(
        agent?.[teamOrIndvidualPerformance]?.persistency.secondYear,
        stat?.highest2ndYearPersistency,
        stat?.lowest2ndYearPersistency,
        isLeader,
      ),
    },
  ];
  resultAgent.data.push(persistencyColumn);

  const arrowColumn = [
    {
      text: ' ',
      type: !isAgentHasMember
        ? TableColumnType.viewProfile
        : isAgentHasMember
        ? TableColumnType.cheveron
        : TableColumnType.text,
    },
  ];

  resultAgent.data.push(arrowColumn);

  if (!isAgentHasMember) {
    resultAgent.onPress = () =>
      navigation.navigate('TeamIndividualProfile', {
        agentCode: agent?.agentCode,
        agentName: agent?.agentName,
      });
  }
  return resultAgent;
};
const getPersistencyMappingData = (data?: SalesBalancePerformance | null) => {
  const inData = cloneDeep(data);
  const resultAgentList = [] as DataConfig[];

  if (!inData) return resultAgentList;
  const designationOrder = { UM: 1, AGT: 2 };

  //UM with members should be on top, UM with no memebrs and AGT should be at bottom
  const tableData = inData?.members?.sort((a, b) => {
    const aHasMembers = a.members && a.members.length > 0;
    const bHasMembers = b.members && b.members.length > 0;
    if (aHasMembers !== bHasMembers) {
      return aHasMembers ? -1 : 1;
    }
    return designationOrder[a.designation] - designationOrder[b.designation];
  });

  tableData?.forEach(member => {
    const resultAgent = transformPersistencyData(member, false);
    const nestedResultAgentList: DataConfig[] = [];

    if (member?.members?.length > 0) {
      const memberClone = cloneDeep(member);
      memberClone.members = [];
      member?.members?.unshift(memberClone);
      member?.members?.forEach(nestedMember => {
        const nestedResultAgent = transformPersistencyData(nestedMember, true);
        nestedResultAgentList.push(nestedResultAgent);
      });
    }

    resultAgent.members = nestedResultAgentList;
    resultAgentList.push(resultAgent);
  });
  return resultAgentList;
};

const getSalesMappingData = (
  timePeriod: TimePeriod,
  data?: SalesBalancePerformance | null,
) => {
  const inData = cloneDeep(data);

  const emptyData: DataConfig = {
    id: '',
    agentName: '',
    data: [],
    members: [],
  };
  const timePeriodKey = timePeriod.toLowerCase() as 'mtd' | 'ytd';

  const resultAgentList = [] as DataConfig[];

  if (!inData) return resultAgentList;

  const tableData = inData.members;

  tableData
    ?.sort((a, b) => a.agentName.localeCompare(b.agentName))
    .forEach(member => {
      const resultAgent = transformSalesMemberData(member, timePeriodKey, true);
      const nestedResultAgentList: DataConfig[] = [];
      const allApeSubmitted = member.members?.map(
        data =>
          data?.['individualPerformance']?.[timePeriodKey]?.submittedApe ?? 0,
      );
      const allApeIssued = member.members?.map(
        data =>
          data?.['individualPerformance']?.[timePeriodKey]?.issuedApe ?? 0,
      );
      const allIssuedCase = member.members?.map(
        data =>
          data?.['individualPerformance']?.[timePeriodKey]?.issuedCase ?? 0,
      );
      const allSubmittedCase = member.members?.map(
        data =>
          data?.['individualPerformance']?.[timePeriodKey]?.submittedCase ?? 0,
      );
      const allFirstYearPersistency = member.members?.map(
        data => data?.['individualPerformance']?.persistency?.firstYear ?? 0,
      );
      const allsecondYearPersistency = member.members?.map(
        data => data?.['individualPerformance']?.persistency?.secondYear ?? 0,
      );
      const stat = {
        highestApeSubmmited: allApeSubmitted?.length
          ? Math.max(...allApeSubmitted)
          : 0,
        lowestApeSubmmited: allApeSubmitted?.length
          ? Math.min(...allApeSubmitted)
          : 0,
        highestApeIssued: allApeIssued?.length ? Math.max(...allApeIssued) : 0,
        lowestApeIssued: allApeIssued?.length ? Math.min(...allApeIssued) : 0,
        highestCaseSubmitted: allSubmittedCase?.length
          ? Math.max(...allSubmittedCase)
          : 0,
        lowestCaseSubmitted: allSubmittedCase?.length
          ? Math.min(...allSubmittedCase)
          : 0,
        highestCaseIssued: allIssuedCase?.length
          ? Math.max(...allIssuedCase)
          : 0,
        lowestCaseIssued: allIssuedCase?.length
          ? Math.min(...allIssuedCase)
          : 0,
        highest1stYearPersistency: allFirstYearPersistency?.length
          ? Math.max(...allFirstYearPersistency)
          : 0,
        lowest1stYearPersistency: allFirstYearPersistency?.length
          ? Math.min(...allFirstYearPersistency)
          : 0,
        highest2ndYearPersistency: allsecondYearPersistency?.length
          ? Math.max(...allsecondYearPersistency)
          : 0,
        lowest2ndYearPersistency: allsecondYearPersistency?.length
          ? Math.min(...allsecondYearPersistency)
          : 0,
      };

      member?.members
        ?.sort((a, b) => a.agentName.localeCompare(b.agentName))
        .forEach(nestedMember => {
          const nestedResultAgent = transformSalesMemberData(
            nestedMember,
            timePeriodKey,
            false,
            stat,
          );
          nestedResultAgentList.push(nestedResultAgent);
        });
      resultAgent.members = nestedResultAgentList;
      resultAgentList.push(resultAgent);
    });
  return resultAgentList;
};

const getBarChartTableData = ({
  selectedAgentData,
  performanceIndex,
  noOfMedals,
  timePeriod,
  designation,
  businessTab,
}: {
  selectedAgentData?: SalesBalancePerformance | Mapa | null;
  performanceIndex?: TeamComparisonTabs;
  noOfMedals: number;
  timePeriod: TimePeriod;
  designation?: 'AM' | 'UM' | 'AGT' | string; //RD
  businessTab: 'SBS' | 'MAPA' | 'LC';
}) => {
  if (!selectedAgentData) {
    return [];
  }
  const barChartTableData = cloneDeep(selectedAgentData);

  const sortedPerformanceData = sortAndAssignMedals({
    selectAgentData: barChartTableData,
    performanceIndex,
    noOfMedals,
    timePeriod,
    designation,
    businessTab,
  });

  return sortedPerformanceData;
};

const transformMapaMemberData = (mapa: Mapa, timePeriod: TimePeriod) => {
  const timePeriodKey = timePeriod.toLowerCase() as 'mtd' | 'ytd';
  const resultAgent: DataConfig = {
    id: '',
    agentName: '',
    data: [],
    members: [],
  };

  resultAgent.id = mapa.agentCode;

  const displayName =
    mapa.designation && mapa.designation !== 'AGT'
      ? `${mapa.agentName} (${mapa.designation})`
      : mapa.agentName;
  resultAgent.agentName = displayName;
  const teamColumn: TeamTableAgentPerformance = [
    {
      text: displayName,
      type: TableColumnType.leaderIcon,
    },
  ];
  resultAgent.data.push(teamColumn);

  const activeAgentColumn = [
    {
      text: numberToKFormat(mapa?.totalAgent ?? 0),
      type: TableColumnType.text,
    },
    {
      text: numberToKFormat(
        mapa?.[timePeriodKey]?.activityRatio?.numberOfIssuedActiveAgent ?? 0,
      ),
      type: TableColumnType.text,
    },
  ];
  resultAgent.data.push(activeAgentColumn);

  const activityRatioColumn = [
    {
      text: `${
        mapa?.[timePeriodKey]?.activityRatio?.submittedActivityPercentage ?? 0
      }%`,
      type: TableColumnType.text,
    },
    {
      text: `${
        mapa?.[timePeriodKey]?.activityRatio?.issuedActivityPercentage ?? 0
      }%`,
      type: TableColumnType.text,
    },
  ];
  resultAgent.data.push(activityRatioColumn);

  const productivityColumn = [
    {
      text: mapa?.[timePeriodKey]?.productivity?.submittedProductivity ?? 0,
      type: TableColumnType.text,
    },

    {
      text: mapa?.[timePeriodKey]?.productivity?.issuedProductivity ?? 0,
      type: TableColumnType.text,
    },
  ];
  resultAgent.data.push(productivityColumn);

  const averageCaseColumn = [
    {
      text: mapa?.[timePeriodKey]?.averageCaseSize?.submittedAvgCaseSize ?? 0,
      type: TableColumnType.text,
    },
    {
      text: mapa?.[timePeriodKey]?.averageCaseSize?.issuedAvgCaseSize ?? 0,
      type: TableColumnType.text,
    },
  ];
  resultAgent.data.push(averageCaseColumn);

  return resultAgent;
};

const getMapaMappingData = (timePeriod: TimePeriod, inData?: Mapa | null) => {
  const resultAgentList = [] as DataConfig[];

  if (!inData) return resultAgentList;
  inData?.members
    ?.sort((a, b) => a.agentName.localeCompare(b.agentName))
    .forEach(member => {
      const resultAgent = transformMapaMemberData(member, timePeriod);
      // const nestedResultAgentList: TeamTableAgentData[] = [];
      // member.members?.forEach(nestedMember => {
      //   const nestedResultAgent = transformMapaMemberData(
      //     nestedMember,
      //     timePeriod,
      //   );
      //   nestedResultAgentList.push(nestedResultAgent);
      // });
      // resultAgent.members = nestedResultAgentList;
      resultAgentList.push(resultAgent);
    });
  return resultAgentList;
};

//* check any agent members from an agent has members or not ( depth more than one level )
const hasNestedMembers = (
  agent: SalesBalancePerformance | Mapa | null | undefined,
) => {
  if (!agent || !agent.members) return false;
  return (
    agent?.members?.some(
      member => member?.members && member?.members?.length > 0,
    ) ?? false
  );
};

export {
  getSalesMappingData,
  getMapaMappingData,
  getBarChartTableData,
  getPersistencyMappingData,
  hasNestedMembers,
};
