import { SvgIconProps } from 'cube-ui-components';
import React from 'react';
import { Svg, Path } from 'react-native-svg';

const OrgChartSVG = (props: SvgIconProps) => {
  return (
    <Svg
      width={props.size || 24}
      height={props.size || 24}
      viewBox="0 0 24 24"
      fill="none">
      <Path
        d="M21.2655 15.6026V11.5835C21.2655 11.1527 20.9075 10.8038 20.4655 10.8038H12.8V8.39667C14.364 8.04193 15.5345 6.67511 15.5345 5.04419C15.5345 3.14477 13.949 1.59961 12 1.59961C10.051 1.59961 8.4655 3.14477 8.4655 5.0437C8.4655 6.67462 9.6355 8.04144 11.2 8.39618V10.8033H3.5345C3.0925 10.8033 2.7345 11.1522 2.7345 11.583V15.6021C1.17 15.9573 0 17.3236 0 18.9545C0 20.854 1.5855 22.3996 3.5345 22.3996C5.4835 22.3996 7.069 20.854 7.069 18.9545C7.069 17.3236 5.8985 15.9568 4.3345 15.6026V12.3626H11.2V15.6021C9.636 15.9568 8.4655 17.3231 8.4655 18.9541C8.4655 20.8535 10.051 22.3991 12 22.3991C13.949 22.3991 15.5345 20.8535 15.5345 18.9541C15.5345 17.3231 14.3645 15.9568 12.8 15.6021V12.3626H19.6655V15.6021C18.1015 15.9568 16.931 17.3231 16.931 18.9541C16.931 20.8535 18.5165 22.3991 20.4655 22.3991C22.4145 22.3991 24 20.8535 24 18.9541C24 17.3231 22.83 15.9573 21.2655 15.6026ZM10.0655 5.0437C10.0655 4.00433 10.9335 3.15842 12 3.15842C13.0665 3.15842 13.9345 4.00433 13.9345 5.0437C13.9345 6.08307 13.0665 6.92898 12 6.92898C10.9335 6.92898 10.0655 6.08307 10.0655 5.0437ZM5.469 18.9545C5.469 19.9944 4.601 20.8403 3.5345 20.8403C2.468 20.8403 1.6 19.9944 1.6 18.9545C1.6 17.9147 2.468 17.0693 3.5345 17.0693C4.601 17.0693 5.469 17.9152 5.469 18.9545ZM13.9345 18.9545C13.9345 19.9944 13.0665 20.8403 12 20.8403C10.9335 20.8403 10.0655 19.9944 10.0655 18.9545C10.0655 17.9147 10.9335 17.0693 12 17.0693C13.0665 17.0693 13.9345 17.9152 13.9345 18.9545ZM20.4655 20.8403C19.3985 20.8403 18.531 19.9944 18.531 18.9545C18.531 17.9147 19.399 17.0693 20.4655 17.0693C21.532 17.0693 22.4 17.9152 22.4 18.9545C22.4 19.9939 21.532 20.8403 20.4655 20.8403Z"
        fill="#E87722"
      />
    </Svg>
  );
};

export default OrgChartSVG;
