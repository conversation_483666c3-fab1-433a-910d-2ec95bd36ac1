import { SvgIconProps } from 'cube-ui-components';
import { Filter } from 'cube-ui-components/dist/cjs/icons';
import React from 'react';
import Svg, { Circle, Defs, G, Mask, Path } from 'react-native-svg';

export default function GoldMedalSVG(props: SvgIconProps) {
  return (
    <Svg
      width={props.size || 31}
      height={props.size || 31}
      viewBox="0 0 31 31"
      fill="none"
      {...props}>
      <Path
        d="M15.05 0C6.74 0 0 6.74 0 15.05S6.74 30.1 15.05 30.1 30.1 23.36 30.1 15.05 23.36 0 15.05 0zm0 28.27c-7.3 0-13.22-5.92-13.22-13.22 0-7.3 5.92-13.22 13.22-13.22 7.3 0 13.22 5.92 13.22 13.22 0 7.3-5.92 13.22-13.22 13.22z"
        fill="#FED141"
      />
      <Path
        d="M25.88 7.47L6.45 25.09s-.07-.05-.1-.08a13.196 13.196 0 01-3.26-8.7c0-7.3 5.92-13.22 13.22-13.22 3.33 0 6.38 1.23 8.7 3.26.31.36.6.73.87 1.12z"
        fill="#FED141"
      />
      <Path
        d="M25.01 6.35a13.176 13.176 0 00-8.7-3.26c-7.3 0-13.22 5.92-13.22 13.22 0 3.33 1.23 6.37 3.26 8.7a13.213 13.213 0 01-4.52-9.96c0-7.3 5.92-13.22 13.22-13.22 3.97 0 7.53 1.75 9.96 4.52z"
        fill="#DDB031"
      />
      <Path
        d="M25.88 7.47L6.45 25.09s-.07-.05-.1-.08a13.196 13.196 0 01-3.26-8.7c0-7.3 5.92-13.22 13.22-13.22 3.33 0 6.38 1.23 8.7 3.26.31.36.6.73.87 1.12z"
        fill="#FED141"
      />
      <Path
        d="M28.27 15.05c0 7.3-5.92 13.22-13.22 13.22-3.28 0-6.29-1.2-8.6-3.18L25.88 7.47c1.51 2.14 2.39 4.76 2.39 7.58z"
        fill="#FEE8A0"
      />
      <Path
        d="M15.48 19.59v-6.4h-2.2v-.97c.63-.02 1.16-.18 1.57-.49.41-.32.67-.72.77-1.22h1.2v9.07h-1.34v.01z"
        fill="#183028"
      />
    </Svg>
  );
}
