import { NativeStackScreenProps } from '@react-navigation/native-stack';
import React from 'react';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import AppTopTabBar from 'components/AppTopTabBar';
import CandidatesRejectedScreen from './components/CandidatesStatus/CandidatesRejectedScreen';
import CandidatesApprovedScreen from './components/CandidatesStatus/CandidatesApprovedScreen';
import { ERecruitApplicationList } from 'types';
import { SearchButton } from './components/CandidateSearch/SearchButton';
import ReviewApplicationListScreen from './components/ReviewApplication/ReviewApplicationListScreen';
import { useTranslation } from 'react-i18next';

type ERAppStatusProps = NativeStackScreenProps<
  ERecruitApplicationList,
  'ERecruitApplicationStatus'
>;

export default function ERecruitReviewAgentSubmissionLayout() {
  const ApplicationStatusTab = createMaterialTopTabNavigator();
  const { t } = useTranslation('eRecruit');

  return (
    <>
      <ScreenHeader
        route={'ERecruitApplication'}
        isLeftArrowBackShown
        customTitle={t('eRecruit.application.applicationStatus')}
        showBottomSeparator={false}
      />
      <ApplicationStatusTab.Navigator
        tabBar={props => <AppTopTabBar variant="scrollable" {...props} />}
        screenOptions={{ swipeEnabled: false }}
        initialRouteName={'ERecruitApplicationStatusList'}>
        <ApplicationStatusTab.Screen
          name="ERecruitApplicationStatusList"
          options={{
            tabBarLabel: '    Review\napplication',
            // tabBarLabelStyle: { textAlign: 'center' },
          }}
          component={ReviewApplicationListScreen}
          initialParams={{ status: 'PENDING_LEADER_APPROVAL' }}
        />
        <ApplicationStatusTab.Screen
          name="Approved"
          options={{ tabBarLabel: 'Approved' }}
          component={ReviewApplicationListScreen}
          initialParams={{ status: 'APPROVED' }}
        />
        <ApplicationStatusTab.Screen
          name="Rejected"
          options={{ tabBarLabel: 'Rejected' }}
          component={ReviewApplicationListScreen}
          initialParams={{ status: 'REJECTED' }}
        />
      </ApplicationStatusTab.Navigator>
    </>
  );
}
