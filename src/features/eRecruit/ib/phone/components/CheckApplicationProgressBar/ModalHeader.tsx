import { memo } from 'react';
import { H7, Icon } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { Platform, TouchableOpacity, ViewProps } from 'react-native';
import styled from '@emotion/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';

export const ModalHeader = memo(function ModalHeader({
  title,
  goBack,
  ...props
}: {
  title: string;
  goBack: () => void;
} & ViewProps) {
  const { colors, sizes } = useTheme();
  const { top: topInset } = useSafeAreaInsets();

  return (
    <Header
      style={{ marginTop: Platform.select({ android: 0, default: topInset }) }}
      {...props}>
      <Label fontWeight="bold">{title}</Label>
      <BackButton onPress={goBack}>
        <Icon.Close size={sizes[6]} fill={colors.onBackground} />
      </BackButton>
    </Header>
  );
});
export default ModalHeader;

const Header = styled.View(() => ({
  flexDirection: 'row',
}));

const Label = styled(H7)(() => ({
  left: 0,
  right: 0,
  position: 'absolute',
  textAlign: 'center',
  alignSelf: 'center',
}));

const BackButton = styled(TouchableOpacity)(({ theme }) => {
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    width: theme.sizes[11],
    height: theme.sizes[11],
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: theme.space[isNarrowScreen ? 0 : 1],
  };
});
