import { useTheme } from '@emotion/react';
import { Box, Column, H6, H8, Row, Typography } from 'cube-ui-components';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import RecruitApprovedSVG from 'features/eRecruit/assets/RecruitApprovedSVG';
import RecruitDeclinedSVG from 'features/eRecruit/assets/RecruitDelinedSVG';
import RecruitPendingSVG from 'features/eRecruit/assets/RecruitPendingSVG';
import { formatPhoneNumber } from 'utils';
import { country } from 'utils/context';

export type LabelsType = {
  label: string;
  info: string | number | undefined | string[];
};

function StatusSVG({ status }: { status: string }) {
  if (status === 'APPROVED') {
    return <RecruitApprovedSVG />;
  } else if (status === 'REJECTED') {
    return <RecruitDeclinedSVG />;
  }
  return <RecruitPendingSVG />;
}

export default function CandidatePersonalInfo({
  fullName,
  mobilePhone,
  email,
  gender,
  source,
  candidateNo,
}: {
  fullName?: string;
  mobilePhone?: string;
  email?: string;
  gender?: string;
  source?: 'I' | 'C' | 'E' | 'R' | 'A';
  candidateNo?: string | undefined | null;
}) {
  const { colors, space } = useTheme();
  const { t } = useTranslation('eRecruit');
  const { t: tLead } = useTranslation('lead');

  const candidateSource = useMemo(() => {
    const sourceObject = {
      I: 'IRIS',
      C: 'CUBE',
      A: 'Affiliate',
      E: 'eRecruit',
      R: 'Remote eRecruit',
    };
    if (!source) return '--';
    //CUBEIDN-765
    if (country == 'id' && (source === 'C' || source === 'I')) {
      return tLead('MYSELF_SOURCE');
    }
    return sourceObject[source];
  }, [source, tLead]);

  return (
    <Box paddingX={space[4]}>
      <Column
        gap={space[4]}
        style={{
          backgroundColor: colors.background,
          padding: space[5],
          borderRadius: space[4],
        }}>
        <H6 fontWeight="bold">{t('eRecruit.candidateProfile.personalInfo')}</H6>
        <Column gap={space[2]}>
          <InfoField
            label={t('eRecruit.candidateProfile.name')}
            content={fullName ?? '--'}
          />
          <InfoField
            label={t('eRecruit.candidateProfile.gender')}
            content={
              gender === 'M' ? 'Male' : gender === 'F' ? 'Female' : 'N/A'
            }
          />
          <InfoField
            label={t('eRecruit.candidateProfile.contactNo')}
            content={formatPhoneNumber(mobilePhone?.slice(2) ?? '')}
          />
          <InfoField
            label={t('eRecruit.candidateProfile.email')}
            content={email ?? '--'}
          />
          <InfoField
            label={t('eRecruit.candidateProfile.source')}
            content={candidateSource}
          />
          <InfoField
            label={t('eRecruit.candidateProfile.candiateNo')}
            content={candidateNo ? `#${candidateNo}` : '--'}
          />
        </Column>
      </Column>
    </Box>
  );
}

const InfoField = ({ label, content }: { label: string; content: string }) => {
  const { colors } = useTheme();
  return (
    <Row justifyContent="space-between" alignItems="center">
      <Box flex={1}>
        <Typography.H8 color={colors.palette.fwdGreyDarkest}>
          {label}
        </Typography.H8>
      </Box>
      <Box flex={1}>
        <Typography.H8>{content}</Typography.H8>
      </Box>
    </Row>
  );
};
