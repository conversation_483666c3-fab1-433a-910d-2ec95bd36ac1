import { useTheme } from '@emotion/react';
import {
  Box,
  Checkbox,
  DatePicker,
  Icon,
  Row,
  TextField,
  Typography,
  addToast,
} from 'cube-ui-components';
import React, {
  Dispatch,
  SetStateAction,
  useCallback,
  useMemo,
  useRef,
  useState,
} from 'react';
import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetModal,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import { BottomSheetDefaultBackdropProps } from '@gorhom/bottom-sheet/lib/typescript/components/bottomSheetBackdrop/types';
import { Portal } from '@gorhom/portal';
import { usePostApproveRejectComment } from 'features/eRecruit/hooks/usePostApproveRejectComment';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { RootStackParamList } from 'types';
import useBoundStore from 'hooks/useBoundStore';
import ERecruitFooter from '../utils/ERecruitFooter';
import { useTranslation } from 'react-i18next';
import { Platform, useWindowDimensions } from 'react-native';
// @ts-expect-error no export member
import { listenToKeyboardEvents } from 'react-native-keyboard-aware-scroll-view';
import { useERecruitReviewApplicationProgressBarStore } from 'features/eRecruit/util/store/ERecruitReviewApplicationProgressBarStore';
import { shallow } from 'zustand/shallow';
import { country } from 'utils/context';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const KeyboardAwareBottomSheetScrollView = listenToKeyboardEvents(
  BottomSheetScrollView,
);

export default function ReviewApplicationDecisionForm({
  setShowForm,
  applicationId,
}: {
  applicationId: number;
  setShowForm: Dispatch<SetStateAction<boolean>>;
}) {
  const { colors, space, sizes } = useTheme();
  const { t } = useTranslation('eRecruit');
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const { height } = useWindowDimensions();
  const { top, bottom } = useSafeAreaInsets();

  const snapPoints = useMemo(() => [height - top], [height, top]);
  const renderBackdrop = useCallback(
    (props: BottomSheetDefaultBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        appearsOnIndex={0}
        disappearsOnIndex={-1}
        pressBehavior={'close'}
        onPress={() => onCancel()}
      />
    ),
    [],
  );
  const onCancel = () => {
    setShowForm(false);
  };
  const [isSelected, setIsSelected] = useState(false);
  const [date, setDate] = useState(new Date());
  const [decision, setDecision] = useState('');

  const { mutateAsync, isLoading } = usePostApproveRejectComment();
  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);

  const onSettled = () => {
    setShowForm(false);
    setAppIdle();
    navigation.navigate('ERecruitReviewAgentsSubmission');
  };

  const { resetProgressBarState } =
    useERecruitReviewApplicationProgressBarStore(
      state => ({
        resetProgressBarState: state.resetProgressBarState,
      }),
      shallow,
    );

  const onRejectPress = () => {
    const request: {
      applicationId: number;
      comment: string;
      action: 'REJECT' | 'APPROVE';
      interviewDate: string;
    } = {
      applicationId: applicationId,
      comment: decision,
      action: 'REJECT',
      interviewDate: date.toISOString().split('T')[0],
    };
    setAppLoading();
    resetProgressBarState();
    mutateAsync(request, {
      onSuccess: () => {
        addToast([
          {
            IconLeft: Icon.Tick,
            message: t('review.application.rejectedMessage'),
          },
        ]);
      },
      onSettled: () => onSettled(),
    });
  };

  const onApprovePress = () => {
    const request: {
      applicationId: number;
      comment: string;
      action: 'REJECT' | 'APPROVE';
      interviewDate: string;
    } = {
      applicationId: applicationId,
      comment: decision,
      action: 'APPROVE',
      interviewDate: date.toISOString().split('T')[0],
    };
    setAppLoading();
    resetProgressBarState();
    mutateAsync(request, {
      onSuccess: () => {
        addToast([
          {
            IconLeft: Icon.Tick,
            message: t('review.application.approvedMessage'),
          },
        ]);
      },
      onSettled: () => onSettled(),
    });
  };

  const checkFillInAll = () => {
    if (isSelected && decision.length > 0) {
      return false;
    } else return true;
  };

  const handleIndicatorStyle = useMemo(
    () => ({
      backgroundColor: colors.palette.fwdGrey[100],
      width: 40,
      height: 5,
    }),
    [colors],
  );

  const handleStyle = useMemo(
    () => ({
      paddingTop: 8,
      paddingBottom: 16,
    }),
    [],
  );

  return (
    <Portal>
      <BottomSheet
        ref={bottomSheetModalRef}
        index={0}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        enableOverDrag={country === 'id'}
        enablePanDownToClose={country === 'id'}
        onClose={onCancel}
        handleIndicatorStyle={handleIndicatorStyle}
        handleStyle={handleStyle}>
        <Row alignItems="center" padding={space[4]}>
          <Typography.H6 fontWeight="bold">
            {t('review.application.yourDecision')}
          </Typography.H6>
        </Row>
        <KeyboardAwareBottomSheetScrollView
          keyboardDismissMode="interactive"
          enableAutomaticScroll={true}
          enableOnAndroid={true}
          extraScrollHeight={
            space[64] + (Platform.OS === 'ios' && bottom ? space[5] : 0)
          }
          keyboardShouldPersistTaps="handled"
          contentContainerStyle={{
            flexGrow: 1,
            paddingHorizontal: space[4],
          }}
          showsVerticalScrollIndicator={false}>
          <Row pb={space[3]}>
            <Typography.LargeBody fontWeight="bold" color={colors.onBackground}>
              {t('review.application.confirmInterview')}
            </Typography.LargeBody>
          </Row>
          <Row
            flex={1}
            alignItems="flex-start"
            gap={space[2]}
            paddingX={space[3]}
            paddingY={space[4]}
            backgroundColor={
              isSelected ? colors.palette.fwdOrange[5] : colors.background
            }
            borderRadius={space[2]}
            borderColor={
              isSelected ? colors.primary : colors.palette.fwdGrey[100]
            }
            borderWidth={1}>
            <Checkbox value={isSelected} onChange={setIsSelected} />
            <Typography.LargeBody style={{ flex: 1 }}>
              {t('review.application.confirmDeclaration')}
            </Typography.LargeBody>
          </Row>
          <Row marginY={space[5]}>
            <Typography.LargeBody fontWeight="bold" color={colors.onBackground}>
              {t('review.application.enterInterviewDate')}
            </Typography.LargeBody>
          </Row>
          <Row>
            <DatePicker
              style={{
                flex: 1,
              }}
              hint={
                country === 'id'
                  ? 'DD/MM/YYYY'
                  : t('review.application.enterInterviewDate')
              }
              label={t('review.application.interviewdate')}
              maxDate={new Date()}
              value={date}
              onChange={setDate}
            />
          </Row>
          <Row marginY={space[5]}>
            <Typography.LargeBody fontWeight="bold" color={colors.onBackground}>
              {t('review.application.decisionDetails')}
            </Typography.LargeBody>
          </Row>
          <Row>
            <TextField
              style={{ width: '100%' }}
              label={t('review.application.comments')}
              inputStyle={{ lineHeight: space[6] }}
              defaultValue={decision}
              onChangeText={setDecision}
              maxLength={500}
              textarea
              inputContainerStyle={{
                height: sizes[56],
              }}
            />
          </Row>
          <Box alignItems="flex-end">
            <Typography.SmallLabel
              color={colors.palette.fwdGreyDarkest}
              style={{
                paddingRight: space[4],
              }}>
              {decision?.length ?? 0}/500
            </Typography.SmallLabel>
          </Box>
          <Box height={200} />
        </KeyboardAwareBottomSheetScrollView>
      </BottomSheet>
      <ERecruitFooter
        primaryLoading={false}
        primaryDisabled={checkFillInAll()}
        primaryLabel={t('review.application.approve')}
        onPrimaryPress={onApprovePress}
        secondaryLoading={false}
        secondaryDisabled={checkFillInAll()}
        secondaryLabel={t('review.application.reject')}
        onSecondaryPress={onRejectPress}
      />
    </Portal>
  );
}
