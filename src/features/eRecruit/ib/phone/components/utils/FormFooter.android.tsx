import { useTheme } from '@emotion/react';
import {
  BottomSheetFooterProps,
  KEYBOARD_STATE,
  useBottomSheetInternal,
} from '@gorhom/bottom-sheet';
import { Button } from 'cube-ui-components';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import Animated, {
  cancelAnimation,
  Extrapolation,
  interpolate,
  useAnimatedReaction,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useKeyboardShown } from 'hooks/useKeyboardShown';
import { LayoutChangeEvent, StyleSheet } from 'react-native';

interface Props extends BottomSheetFooterProps {
  primaryDisabled?: boolean;
  primaryLoading?: boolean;
  primaryLabel?: string;
  onPrimaryPress: () => void;
  secondaryDisabled?: boolean;
  secondaryLoading?: boolean;
  secondaryLabel?: string;
  onSecondaryPress?: () => void;
}

export default function FormFooter({
  primaryDisabled,
  primaryLoading,
  primaryLabel,
  onPrimaryPress,
  secondaryDisabled,
  secondaryLoading,
  secondaryLabel,
  onSecondaryPress,
}: Props) {
  const { space, colors, animation } = useTheme();
  const { bottom: bottomInset } = useSafeAreaInsets();
  const { t } = useTranslation(['eApp']);
  const { animatedKeyboardState, animatedFooterHeight } =
    useBottomSheetInternal();
  const keyboardShown = useKeyboardShown();

  const { isNarrowScreen, isWideScreen } = useWindowAdaptationHelpers();

  const progress = useSharedValue(
    animatedKeyboardState.value === KEYBOARD_STATE.SHOWN ? 1 : 0,
  );

  const animatedStyle = useAnimatedStyle(
    () => ({
      opacity: interpolate(
        progress.value,
        [0, 0.1],
        [1, 0],
        Extrapolation.CLAMP,
      ),
      transform: [
        { translateY: interpolate(progress.value, [0, 1], [0, 500]) },
      ],
    }),
    [progress],
  );

  useAnimatedReaction(
    () => animatedKeyboardState.value,
    value => {
      const toValue = value === KEYBOARD_STATE.SHOWN ? 1 : 0;
      if (progress.value !== toValue) {
        cancelAnimation(progress);
        progress.value = withTiming(toValue, {
          duration: animation.duration,
        });
      }
    },
    [],
  );

  const handleContainerLayout = useCallback(
    ({
      nativeEvent: {
        layout: { height },
      },
    }: LayoutChangeEvent) => {
      animatedFooterHeight.value = height;
    },
    [animatedFooterHeight],
  );

  return (
    <Animated.View
      onLayout={handleContainerLayout}
      pointerEvents={keyboardShown ? 'none' : 'auto'}
      style={[
        styles.container,
        animatedStyle,
        {
          paddingHorizontal: space[isNarrowScreen ? 3 : 4],
          paddingTop: space[4],
          paddingBottom: space[4] + bottomInset,
          backgroundColor: colors.background,
          borderTopWidth: 1,
          borderColor: colors.palette.fwdGrey[100],
          flexDirection: 'row',
          justifyContent: 'center',
          gap: space[2],
        },
      ]}>
      {secondaryLabel && (
        <Button
          variant="secondary"
          loading={secondaryLoading}
          disabled={secondaryDisabled}
          onPress={onSecondaryPress}
          text={secondaryLabel}
          style={{ flex: 1, maxWidth: isWideScreen ? 400 : undefined }}
        />
      )}
      <Button
        text={primaryLabel ?? t('eApp:done')}
        disabled={primaryDisabled}
        onPress={onPrimaryPress}
        loading={primaryLoading}
        style={{ flex: 1, maxWidth: isWideScreen ? 400 : undefined }}
      />
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 9999,
  },
});
