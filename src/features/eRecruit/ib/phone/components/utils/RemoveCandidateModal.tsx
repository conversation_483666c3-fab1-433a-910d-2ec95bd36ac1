import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  NavigationProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { Button, H6, H7, Icon, Row, addToast } from 'cube-ui-components';
import React from 'react';
import { Modal } from 'react-native';
import { Portal } from '@gorhom/portal';
import { ERAppStatusProps } from 'screens/ERecruitScreen/ERecruitCandidateProfileScreen/ERecruitCandidateProfileScreen.tablet';
import { RootStackParamList } from 'types';
import { useTranslation } from 'react-i18next';
import { useRemoveERCandidate } from 'hooks/useRemoveCandidate';

export type DeleteCandidateProps = {
  visible: boolean;
  onClose: () => void;
  registrationId: number | null | undefined;
  registrationStagingId: number | null | undefined;
};

export default function RemoveCandidateModal({
  visible,
  onClose,
  registrationId,
  registrationStagingId,
}: DeleteCandidateProps) {
  const { space } = useTheme();
  const route = useRoute<ERAppStatusProps['route']>();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { t } = useTranslation('eRecruit');
  const eRecruit = 'eRecruit.candidateProfile';

  const closeModalHandler = () => {
    onClose();
  };

  const backToRecruitPage = () => {
    navigation.navigate('Main');
  };

  const { mutateAsync: removeCandidate, isLoading } = useRemoveERCandidate({
    registrationId: registrationId,
    registrationStagingId: registrationStagingId,
  });

  const submitHandler = async () => {
    await removeCandidate(undefined, {
      onSuccess: () => {
        onClose();
        backToRecruitPage();
        addToast([
          {
            IconLeft: Icon.Delete,
            message: t(`${eRecruit}.candidateIsRemove`),
          },
        ]);
      },
      onError: () => {
        onClose();
        addToast([
          {
            IconLeft: Icon.Error,
            message: t(`${eRecruit}.candidateIsRemoveFail`),
          },
        ]);
      },
    });
  };

  return (
    <Portal>
      <Modal visible={visible} animationType="fade" transparent={true}>
        <ModalContainer>
          <Container>
            <Row style={{ paddingBottom: space[4] }}>
              <H6 fontWeight="bold">{t(`${eRecruit}.removecandidate`)}</H6>
            </Row>
            <Row style={{ paddingBottom: space[6] }}>
              <H7>{t(`${eRecruit}.areYouSure`)}</H7>
            </Row>
            <Row gap={16}>
              <Button
                style={{ flex: 1 }}
                text="Cancel"
                onPress={closeModalHandler}
                variant="secondary"
              />
              <Button
                style={{ flex: 1 }}
                text="Yes, I am sure"
                onPress={() => submitHandler()}
                loading={isLoading}
              />
            </Row>
          </Container>
        </ModalContainer>
      </Modal>
    </Portal>
  );
}

const ModalContainer = styled.View(({ theme: { space } }) => ({
  alignItems: 'center',
  justifyContent: 'center',
  width: '100%',
  height: '100%',
  paddingHorizontal: space[4],
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
}));

const Container = styled.View(({ theme: { colors, space } }) => ({
  borderRadius: space[4],
  backgroundColor: colors.background,
  padding: space[6],
}));
