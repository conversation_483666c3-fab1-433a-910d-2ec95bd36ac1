import { memo } from 'react';
import { shallow } from 'zustand/shallow';
import { View } from 'react-native';
import ProgressGroup from './ProgressGroup';
import { useERecruitReviewApplicationProgressBarStore } from 'features/eRecruit/util/store/ERecruitReviewApplicationProgressBarStore';

export const ProgressList = memo(function ProgressList() {
  const { groups } = useERecruitReviewApplicationProgressBarStore(
    state => ({
      groups: state.groups,
    }),
    shallow,
  );

  return (
    <View>
      {groups.map((group, index) => (
        <ProgressGroup key={index} index={index} group={group} />
      ))}
    </View>
  );
});
export default ProgressList;
