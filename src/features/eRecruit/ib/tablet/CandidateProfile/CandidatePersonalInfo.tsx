import { useTheme } from '@emotion/react';
import { Box, Column, H6, H8, Row, Typography } from 'cube-ui-components';
import React, { useMemo } from 'react';
import { ERAppStatusProps } from 'screens/ERecruitScreen/ERecruitCandidateProfileScreen/ERecruitCandidateProfileScreen.tablet';
import { useRoute } from '@react-navigation/native';
import { useTranslation } from 'react-i18next';
import RecruitApprovedSVG from 'features/eRecruit/assets/RecruitApprovedSVG';
import RecruitDeclinedSVG from 'features/eRecruit/assets/RecruitDelinedSVG';
import RecruitPendingSVG from 'features/eRecruit/assets/RecruitPendingSVG';
import { formatPhoneNumber } from 'utils';
import { country } from 'utils/context';

export type LabelsType = {
  label: string;
  info: string | number | undefined | string[];
};

function StatusSVG({ status }: { status: string }) {
  if (status === 'APPROVED') {
    return <RecruitApprovedSVG />;
  } else if (status === 'REJECTED') {
    return <RecruitDeclinedSVG />;
  }
  return <RecruitPendingSVG />;
}

export default function CandidatePersonalInfo({
  fullName,
  mobilePhone,
  email,
  gender,
  source,
  candidateNo,
}: {
  fullName?: string;
  mobilePhone?: string;
  email?: string;
  gender?: string;
  source?: 'I' | 'C' | 'E' | 'R' | 'A';
  candidateNo?: string | undefined | null;
}) {
  const route = useRoute<ERAppStatusProps['route']>();
  const { cubeStatus } = route.params;
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation('eRecruit');
  const { t: tLead } = useTranslation('lead');

  const removeCountryCodeMobilePhone = mobilePhone?.slice(2);
  const newMobilePhone =
    country === 'ib' || country === 'id'
      ? removeCountryCodeMobilePhone
      : mobilePhone;

  const candidateSource = useMemo(() => {
    const sourceObject = {
      I: 'IRIS',
      C: 'CUBE',
      A: 'Affiliate',
      E: 'eRecruit',
      R: 'Remote eRecruit',
    };
    if (!source) return '--';
    //CUBEIDN-765
    if (country == 'id' && (source === 'C' || source === 'I')) {
      return tLead('MYSELF_SOURCE');
    }
    return sourceObject[source];
  }, [source, tLead]);

  return (
    <Box padding={space[5]}>
      <H6 fontWeight="bold" style={{ paddingBottom: space[5] }}>
        {t('eRecruit.candidateProfile.personalInfo')}
      </H6>
      <Column
        gap={space[4]}
        style={{
          shadowColor: '#000',
          shadowOffset: {
            width: 0,
            height: 0,
          },
          shadowOpacity: 0.16,
          shadowRadius: 10,
          elevation: 5, // This is for Android
          backgroundColor: colors.background,
          padding: space[5],
          borderRadius: space[4],
        }}>
        <Row justifyContent="space-between" gap={space[4]}>
          <Column gap={space[1]} flex={1}>
            <Typography.SmallLabel color={colors.palette.fwdGreyDarker}>
              {t('eRecruit.candidateProfile.name')}
            </Typography.SmallLabel>
            <Typography.LargeBody>{fullName}</Typography.LargeBody>
          </Column>
          <Column gap={space[1]} flex={1}>
            <Typography.SmallLabel color={colors.palette.fwdGreyDarker}>
              {t('eRecruit.candidateProfile.gender')}
            </Typography.SmallLabel>
            <Typography.LargeBody>
              {gender === 'M' ? 'Male' : gender === 'F' ? 'Female' : 'N/A'}
            </Typography.LargeBody>
          </Column>
          <Column gap={space[1]} flex={1}>
            <Typography.SmallLabel color={colors.palette.fwdGreyDarker}>
              {t('eRecruit.candidateProfile.contactNo')}
            </Typography.SmallLabel>
            <Typography.LargeBody>
              {formatPhoneNumber(newMobilePhone ?? '')}
            </Typography.LargeBody>
          </Column>
        </Row>
        <Row justifyContent="space-between" gap={space[4]}>
          <Column gap={space[1]} flex={1}>
            <Typography.SmallLabel color={colors.palette.fwdGreyDarker}>
              {t('eRecruit.candidateProfile.email')}
            </Typography.SmallLabel>
            <Typography.LargeBody>{email}</Typography.LargeBody>
          </Column>
          <Column gap={space[1]} flex={1}>
            <Typography.SmallLabel color={colors.palette.fwdGreyDarker}>
              {t('eRecruit.candidateProfile.source')}
            </Typography.SmallLabel>
            <Typography.LargeBody>{candidateSource}</Typography.LargeBody>
          </Column>
          <Column gap={space[1]} flex={1}>
            <Typography.SmallLabel color={colors.palette.fwdGreyDarker}>
              {t('eRecruit.candidateProfile.candiateNo')}
            </Typography.SmallLabel>
            <Typography.LargeBody>
              {candidateNo ? `#${candidateNo}` : '--'}
            </Typography.LargeBody>
          </Column>
        </Row>
      </Column>
    </Box>
  );
}
