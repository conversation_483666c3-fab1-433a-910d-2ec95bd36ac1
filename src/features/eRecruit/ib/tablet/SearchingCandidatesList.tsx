import { ScrollView, TouchableOpacity } from 'react-native';
import React, { useMemo, useState } from 'react';
import CustomSearchingBar from 'components/CustomSearchingBar';
import { useTheme } from '@emotion/react';
import {
  Column,
  Typography,
  Row,
  Box,
  Icon,
  Button,
  Chip,
} from 'cube-ui-components';
import {
  ApplicationStageKeys,
  CubeStatusKeys,
  SortDirectionKeys,
  cubeStatusList,
  filterStatusList,
  positionList,
} from 'types/eRecruit';
import styled from '@emotion/native';
import { useTranslation } from 'react-i18next';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';
import { useGetERApplicationList } from '../../hooks/useGetERApplicationList';
import { filterLabelMap } from './config';
import ERTable, {
  lastUpdateHandler,
} from 'features/eRecruit/ib/tablet/applicationStatus/ERInProgressTable';
import SlidingSideModal from 'components/SlidingSideModal';
import { default as ERApprovedTable } from 'features/eRecruit/ib/tablet/applicationStatus/ERApprovedTable';
import { default as ERRejectedTable } from 'features/eRecruit/ib/tablet/applicationStatus/ERRejectedTable';
import { country } from 'utils/context';
import { TFuncKey } from 'i18next';
import { RecruitKey } from 'utils/translation/i18next';

type ERTableContent = {
  displayName: string;
  position?: string;
  status: CubeStatusKeys;
  lastUpdated: string;
  recruitId?: number;
  stage: ApplicationStageKeys;
};

export default function SearchingCandidatesListSection({
  setIsSearching,
  isApproved,
  isRejected,
}: {
  setIsSearching: (value: boolean) => void;
  isApproved?: boolean;
  isRejected?: boolean;
}) {
  const { colors, space } = useTheme();
  const candidate = 'eRecruit.candidate';
  const { t } = useTranslation('eRecruit');

  const [searchKeyWordsQuery, setSearchKeyWordsQuery] = useState<string>('');
  const [order, setOrder] = useState<SortDirectionKeys>('newest');
  const [activeFilters, setActiveFilters] = useState<
    CubeStatusKeys | undefined
  >(isApproved ? 'APPROVED' : isRejected ? 'REJECTED' : undefined);
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [positionFilter, setPositionFilter] = useState<(string | undefined)[]>(
    [],
  );
  const [filterBy, setFilterBy] = useState<(CubeStatusKeys | undefined)[]>([]);

  const { data, isLoading, refetch, isRefetching } = useGetERApplicationList({
    cubeStatusList: activeFilters ? [activeFilters] : [],
    limit: 99999,
    direction: order === 'oldest' ? 'ASC' : 'DESC',
    query: searchKeyWordsQuery,
  });

  const candidatesList = data?.data ?? [];

  const candidateCount = candidatesList?.length ?? 0;

  const [isSortDate, setIsSortDate] = useState(false);

  const sortedAppStatusData: ERTableContent[] = useMemo(
    () =>
      !data
        ? []
        : data?.data
            ?.map(item => ({
              displayName: item.name,
              position: item.candidatePositionCode ?? '--',
              status: item.cubeStatus,
              lastUpdated: lastUpdateHandler(item),
              approvalDate: item.approvedDate ?? '--',
              rejectDate: item.rejectedDate ?? '--',
              recruitId: item.registrationId ?? item.registrationStagingId,
              stage: item.stage,
            }))
            .sort((a, b) =>
              isSortDate
                ? new Date(a?.lastUpdated).getTime() -
                  new Date(b?.lastUpdated).getTime()
                : new Date(b?.lastUpdated).getTime() -
                  new Date(a?.lastUpdated).getTime(),
            ),
    [data, isSortDate],
  );

  const dataOnPositionFilter = useMemo(
    () =>
      positionFilter.length > 0
        ? sortedAppStatusData.filter(item =>
            positionFilter.includes(item.position),
          )
        : sortedAppStatusData,
    [sortedAppStatusData, positionFilter],
  );

  const processedData = useMemo(
    () =>
      filterBy.length > 0
        ? dataOnPositionFilter.filter(item => filterBy.includes(item.status))
        : dataOnPositionFilter,
    [dataOnPositionFilter, filterBy],
  );

  const tagOnPressForPosition = (tabFilter: string) => {
    positionFilter.includes(tabFilter)
      ? setPositionFilter(positionFilter.filter(item => item !== tabFilter))
      : setPositionFilter([...positionFilter, tabFilter]);
  };

  const tagOnPress = (tabFilter: CubeStatusKeys) => {
    filterBy.includes(tabFilter)
      ? setFilterBy(filterBy.filter(item => item !== tabFilter))
      : setFilterBy([...filterBy, tabFilter]);
  };
  console.log('candidatesList', candidatesList);

  return (
    <Column paddingX={space[6]} paddingLeft={0} flex={1}>
      <SlidingSideModal
        hasHeaderBottomBar={false}
        title={'Filter'}
        visible={isOpenFilter}
        onClose={() => setIsOpenFilter(false)}>
        <Column justifyContent="space-between" flex={1}>
          <Box>
            <Box paddingX={space[4]} paddingY={0} gap={space[3]}>
              <Typography.H7 fontWeight="bold">
                {t('filterPanel.title')}
              </Typography.H7>
              <Row columnGap={space[1]} rowGap={space[3]} flexWrap="wrap">
                {filterStatusList.map(item => (
                  <Chip
                    key={item.status}
                    focus={filterBy.includes(item.status)}
                    label={t(`candidate.status.${item.label}` as RecruitKey)}
                    onPress={() => tagOnPress(item.status)}
                  />
                ))}
              </Row>
            </Box>
            <Box paddingY={space[6]} marginX={space[4]}>
              <Box height={1} backgroundColor={colors.palette.fwdGrey[100]} />
            </Box>
            <Box paddingX={space[4]} paddingY={0} gap={space[3]}>
              <Typography.H7 fontWeight="bold">Position</Typography.H7>
              <Row columnGap={space[1]} rowGap={space[3]} flexWrap="wrap">
                {positionList.map(position => (
                  <Chip
                    key={position}
                    focus={positionFilter.includes(position)}
                    label={position}
                    onPress={() => tagOnPressForPosition(position)}
                  />
                ))}
              </Row>
            </Box>
          </Box>
          <Box>
            <Box paddingY={space[6]}>
              <Box height={1} backgroundColor={colors.palette.fwdGrey[100]} />
            </Box>
            <Row
              paddingBottom={space[6]}
              gap={space[5]}
              paddingX={space[4]}
              justifyContent="space-between">
              <Button
                text={t('filterPanel.reset')}
                variant="secondary"
                style={{ flex: 1 }}
                onPress={() => {
                  setFilterBy([]);
                  setPositionFilter([]);
                }}
              />
              <Button
                text={t('filterPanel.apply')}
                style={{ flex: 1 }}
                onPress={() => {
                  setIsOpenFilter(false);
                }}
              />
            </Row>
          </Box>
        </Column>
      </SlidingSideModal>
      <CustomSearchingBar
        onExitSearch={() => setIsSearching(false)}
        setSearchKeyWords={setSearchKeyWordsQuery}
        placeholderText={' ' + t('eRecruit.searchCandidate')}
        BelowBarLabelComponent={
          <Typography.SmallLabel
            style={{
              alignSelf: 'flex-start',
              paddingTop: space[1],
            }}
            color={colors.palette.fwdGreyDarker}>
            {country === 'id'
              ? 'e.g. Candidate’s name, mobile'
              : 'e.g. Candidate’s name'}
          </Typography.SmallLabel>
        }
      />
      {searchKeyWordsQuery.trim().length > 0 && (
        <Typography.H7 fontWeight="bold" style={{ paddingTop: space[4] }}>
          Search result ({candidateCount})
        </Typography.H7>
      )}
      {searchKeyWordsQuery.trim().length > 0 && (
        <Box paddingTop={space[3]} flex={1}>
          <Box>
            <FilterSectionRow>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                contentContainerStyle={{
                  gap: space[2],
                }}>
                {isApproved || isRejected ? (
                  <>
                    {positionList.map(position => (
                      <Chip
                        key={position}
                        focus={positionFilter.includes(position)}
                        label={position}
                        onPress={() => tagOnPressForPosition(position)}
                      />
                    ))}
                  </>
                ) : (
                  cubeStatusList
                    .filter(
                      status => status !== 'APPROVED' && status !== 'REJECTED',
                    )
                    .map(status => {
                      return (
                        <Chip
                          key={status}
                          label={filterLabelMap?.[status]}
                          focus={filterBy.includes(status)}
                          onPress={() => tagOnPress(status)}
                        />
                      );
                    })
                )}
              </ScrollView>
              {isApproved || isRejected ? (
                <></>
              ) : (
                <TouchableOpacity
                  onPress={() => {
                    setIsOpenFilter(true);
                  }}>
                  {filterBy.length > 0 || positionFilter.length > 0 ? (
                    <Icon.Filtered2 />
                  ) : (
                    <Icon.Filter fill={colors.onBackground} />
                  )}
                </TouchableOpacity>
              )}
            </FilterSectionRow>
          </Box>
          <Box paddingTop={space[6]} />
          {isApproved ? (
            <ERApprovedTable
              data={processedData}
              isListProcessing={isLoading}
              isSortDate={isSortDate}
              setIsSortDate={setIsSortDate}
            />
          ) : isRejected ? (
            <ERRejectedTable
              data={processedData}
              isListProcessing={isLoading}
              isSortDate={isSortDate}
              setIsSortDate={setIsSortDate}
            />
          ) : (
            <ERTable
              data={processedData}
              isListProcessing={isLoading}
              isSortDate={isSortDate}
              setIsSortDate={setIsSortDate}
            />
          )}
        </Box>
      )}
    </Column>
  );
}

const FilterSectionRow = styled(Row)(({ theme }) => ({
  gap: theme.space[2],
  alignItems: 'center',
}));

const CandidateSectionSubLabel = styled(Typography.Label)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
}));
