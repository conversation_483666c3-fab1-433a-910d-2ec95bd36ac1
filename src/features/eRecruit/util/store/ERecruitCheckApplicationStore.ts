import { ObjectUtil } from 'utils';
import { create, StateCreator } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

export interface DoneStatus {
  done?: boolean;
}

export type Position = {
  jobType: string;
  agencyType: string;
  reportingBranch: string;
  position: string;
} & DoneStatus;

export type LeaderInformation = {
  agentCode: string;
  name: string;
  alcFwdName: string;
} & DoneStatus;

export interface ERecruitCheckApplicationState {
  registrationStagingId?: number;
  position: Position;
  leaderInformation: LeaderInformation;
}

export interface ERecruitCheckApplicationStore
  extends ERecruitCheckApplicationState {
  updateRegistrationStagingId: (registrationStagingId: number) => void;
  updatePosition: (
    data: Partial<ERecruitCheckApplicationState['position']>,
  ) => void;
  updateleaderInformation: (
    data: Partial<ERecruitCheckApplicationState['leaderInformation']>,
  ) => void;
  resetERecruitmentStoreState: () => void;
}

const createERecruitCheckApplicationStore: StateCreator<
  ERecruitCheckApplicationStore
> = (set, get) => ({
  ...ObjectUtil.cloneDeep(initialState),
  updateRegistrationStagingId: registrationStagingId => {
    set(() => ({
      ...get(),
      registrationStagingId,
    }));
  },
  updatePosition: data => {
    set(() => ({
      position: {
        ...get().position,
        ...data,
      },
    }));
  },
  updateleaderInformation: data => {
    set(() => ({
      leaderInformation: {
        ...get().leaderInformation,
        ...data,
      },
    }));
  },
  resetERecruitmentStoreState: () => {
    set({
      ...get(),
      ...ObjectUtil.cloneDeep(initialState),
      registrationStagingId: undefined,
    });
  },
});

const initialState: ERecruitCheckApplicationState = {
  registrationStagingId: undefined,
  position: {
    jobType: '',
    agencyType: '',
    reportingBranch: '',
    position: '',
    done: false,
  },
  leaderInformation: {
    agentCode: '',
    name: '',
    alcFwdName: '',
    done: false,
  },
};

export const useERecruitCheckApplicationStore = create(
  immer(devtools(createERecruitCheckApplicationStore)),
);
