import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Box, Column, H8, Icon, Row } from 'cube-ui-components';
import { RemoteCheckingLinkModal } from 'features/eRecruit/components/CandidateProfile/RecruitmentRootSection';
import ShareLinkButton from 'features/eRecruit/components/CandidateProfile/ShareLinkButton';
import { useGetERCandidateProfile } from 'features/eRecruit/hooks/useGetERCandidateProfile';
import RemoveCandidateButton from 'features/eRecruit/my/tablet/components/RemoveCandidateButton';
import TriggerApplicationButton from 'features/eRecruit/my/tablet/components/TriggerApplicationButton';
import useBoundStore from 'hooks/useBoundStore';
import React, { useCallback, useEffect, useMemo, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { LayoutChangeEvent, ScrollView } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ERAppStatusProps } from 'screens/ERecruitScreen/ERecruitCandidateProfileScreen/ERecruitCandidateProfileScreen';
import {
  ERecruitCandidateProfileParamList,
  RootStackParamListMap,
} from 'types';
import CandidateApplicationStatus from '../../CandidateProfile/CandidateApplicationStatus';
import CandidatePersonalInfo from 'features/eRecruit/ib/tablet/CandidateProfile/CandidatePersonalInfo';
import CandidateSideBarContent from './CandidateSideBarContent';
import useToggle from 'hooks/useToggle';
import TabletFooter from 'components/Footer/TabletFooter';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withDelay,
  withTiming,
} from 'react-native-reanimated';

export default function IDNCandidateProfileLayout(props: ERAppStatusProps) {
  const { top, bottom } = useSafeAreaInsets();
  const { t } = useTranslation('eRecruit');
  const { colors, space, getElevation, sizes } = useTheme();
  const { navigate } =
    useNavigation<NavigationProp<RootStackParamListMap['my']>>();
  const { registrationId, registrationStagingId } = props.route
    .params as ERecruitCandidateProfileParamList;
  const [isShowModal, onOpenModal, , setIsShowModal] = useToggle(false);
  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);

  const { isLoading: isCPDetailsLoading, data: candidateProfileDetails } =
    useGetERCandidateProfile({
      registrationId: registrationId,
      registrationStagingId: registrationStagingId,
    });

  const cubeStatus = candidateProfileDetails?.cubeStatus as
    | 'REMOTE_SIGNATURE'
    | 'PENDING_PAYMENT'
    | 'PENDING_LEADER_APPROVAL'
    | 'APPROVED'
    | 'REJECTED'
    | 'REMOTE_CHECKING'
    | 'RESUME_APPLICATION'
    | 'POTENTIAL_CANDIDATE'
    | undefined;

  useEffect(() => {
    if (isCPDetailsLoading) {
      setAppLoading();
    } else setAppIdle();
  }, [isCPDetailsLoading, setAppIdle, setAppLoading]);

  const scrollViewRef = useRef<ScrollView | null>(null);

  const onResumeApplication = useCallback(() => {
    if (!candidateProfileDetails?.stage) {
      console.log('No candidateProfileDetails?.stage found, Not navigation');
      return;
    }
    const currentApplicationId =
      typeof candidateProfileDetails?.registrationStagingId === 'number'
        ? String(candidateProfileDetails?.registrationStagingId)
        : '';

    const isNewApplication =
      candidateProfileDetails?.stage == 'NEW_APPLICATION' &&
      !currentApplicationId;

    navigate(
      'ERecruitApplication',
      isNewApplication
        ? undefined
        : {
            registrationStagingId: currentApplicationId,
          },
    );
  }, [candidateProfileDetails, navigate]);

  const triggerButtonLabel = useMemo(() => {
    if (!candidateProfileDetails?.stage) {
      return '--';
    }
    switch (candidateProfileDetails?.stage) {
      case 'NEW_APPLICATION':
        return t('eRecruit.candidateProfile.startApplication');
      default:
        return t('eRecruit.candidateProfile.resumeApplication');
    }
  }, [candidateProfileDetails?.stage, t]);

  const warningMessageBottom = useSharedValue(0);

  const onFooterLayout = useCallback(
    (e: LayoutChangeEvent) => {
      warningMessageBottom.value = withDelay(
        500,
        withTiming(e.nativeEvent.layout.height, {
          duration: 500,
        }),
      );
    },
    [warningMessageBottom],
  );

  const warningStyle = useAnimatedStyle(() => {
    return {
      position: 'absolute',
      bottom: warningMessageBottom.value,
      left: 0,
      right: 0,
    };
  });

  const warningMessage = useMemo(() => {
    switch (cubeStatus) {
      case 'REMOTE_SIGNATURE':
        return t('eRecruit.candidateProfile.remoteSignatureWarning');
      case 'RESUME_APPLICATION':
        return t('eRecruit.candidateProfile.resumeApplicationWarning');
      default:
        return undefined;
    }
  }, [cubeStatus, t]);

  const hasFooter =
    candidateProfileDetails?.submissionDate == null ||
    cubeStatus === 'REMOTE_SIGNATURE';

  return (
    <Row backgroundColor={colors.palette.fwdOrange[5]} flex={1}>
      <RemoteCheckingLinkModal
        isShowModal={isShowModal}
        setIsShowModal={setIsShowModal}
        shareLink={candidateProfileDetails?.link}
        shareMessage={
          cubeStatus == 'PENDING_PAYMENT'
            ? candidateProfileDetails?.sharePaymentUrl
            : candidateProfileDetails?.shareLink
        }
        mobilePhone={candidateProfileDetails?.mobilePhone}
        cubeStatus={cubeStatus}
      />
      <Column flex={268} paddingTop={top + 15}>
        <CandidateSideBarContent
          candidateProfileDetails={candidateProfileDetails}
          cubeStatus={cubeStatus}
          scrollViewRef={scrollViewRef}
        />
      </Column>
      <Column
        flex={844}
        backgroundColor={colors.background}
        borderTopLeftRadius={space[10]}
        borderBottomLeftRadius={space[10]}
        paddingRight={space[8]}
        paddingLeft={space[10]}
        style={{
          ...getElevation(5),
          shadowColor: 'rgba(184, 82, 6, 0.10)',
        }}>
        <ScrollView
          ref={scrollViewRef}
          contentContainerStyle={{ paddingBottom: space[25] }}>
          <CandidateApplicationStatus {...candidateProfileDetails} />
          <CandidatePersonalInfo
            fullName={candidateProfileDetails?.name}
            mobilePhone={candidateProfileDetails?.mobilePhone}
            email={candidateProfileDetails?.email}
            gender={candidateProfileDetails?.gender}
            source={candidateProfileDetails?.source}
            candidateNo={candidateProfileDetails?.candidateRefId}
          />
          {cubeStatus == 'REJECTED' ||
          (candidateProfileDetails?.submissionDate == null &&
            cubeStatus !== 'REMOTE_SIGNATURE') ? (
            <RemoveCandidateButton
              registrationId={registrationId}
              registrationStagingId={registrationStagingId}
            />
          ) : (
            <></>
          )}
        </ScrollView>
      </Column>

      {hasFooter && !!warningMessage && (
        <Animated.View style={warningStyle}>
          <Row
            backgroundColor={colors.palette.alertRedLight}
            gap={space[2]}
            alignItems="center"
            py={space[3]}
            px={space[5]}>
            <Icon.Warning size={sizes[5]} fill={colors.palette.alertRed} />
            <Box flex={1}>
              <H8 color={colors.palette.alertRed}>{warningMessage}</H8>
            </Box>
          </Row>
        </Animated.View>
      )}
      {hasFooter && (
        <TabletFooter
          style={{ position: 'absolute', bottom: 0, borderBottomWidth: 0 }}
          onLayout={onFooterLayout}>
          <FooterContainer bgColor={colors.background}>
            {cubeStatus === 'REMOTE_SIGNATURE' ? (
              <ShareLinkButton
                candidateProfileDetails={candidateProfileDetails}
                onOpenModal={onOpenModal}
              />
            ) : (
              <TriggerApplicationButton
                style={{
                  minHeight: space[13],
                }}
                label={triggerButtonLabel}
                onPress={onResumeApplication}
              />
            )}
          </FooterContainer>
        </TabletFooter>
      )}
    </Row>
  );
}

const FooterContainer = styled(Row)(({ theme: { space } }) => ({
  gap: space[4],
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'flex-end',
}));
