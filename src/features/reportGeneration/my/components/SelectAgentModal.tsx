import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Portal } from '@gorhom/portal';
import {
  Box,
  Button,
  H6,
  Icon,
  RadioButton,
  Row,
  TextField,
  Typography,
} from 'cube-ui-components';
import { MemberInfo } from 'features/reportGeneration/utils/reportUtils';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, ScrollView, TouchableOpacity, View } from 'react-native';

export default function SelectAgentModal({
  visible,
  onClose,
  data,
  handleSelectAgent,
}: {
  visible: boolean;
  onClose: () => void;
  data: MemberInfo[];
  handleSelectAgent?: (agent: MemberInfo) => void;
}) {
  const { colors, sizes, space } = useTheme();
  const { t } = useTranslation('reportGeneration');
  const [selectedValue, setSelectedValue] = useState(data[0]);
  const [searchText, setSearchText] = useState('');

  return (
    <Portal>
      <Modal visible={visible} animationType="fade" transparent={true}>
        <ModalContainer>
          <View
            style={{
              borderRadius: sizes[4],
              padding: space[6],
              paddingBottom: space[12],
              width: '80%',
              height: '80%',
              backgroundColor: colors.palette.white,
            }}>
            <Row justifyContent="flex-end">
              <TouchableOpacity onPress={onClose}>
                <Icon.Close size={sizes[6]} fill={colors.onBackground} />
              </TouchableOpacity>
            </Row>
            <View style={{ paddingHorizontal: space[6], gap: space[6] }}>
              <H6
                fontWeight="bold"
                color={colors.onBackground}
                style={{ paddingBottom: space[4] }}>
                {t('selectAgent')}
              </H6>
              <ScrollView
                contentContainerStyle={{ gap: space[6], height: '75%' }}>
                <TextField
                  left={<Icon.Search fill={colors.palette.fwdGreyDarker} />}
                  value={searchText}
                  placeholder={t('search')}
                  onChangeText={text => setSearchText(text)}
                />
                <View>
                  <Row>
                    <Box flex={1}>
                      <Typography.Body
                        color={colors.palette.fwdGreyDarker}
                        style={{ paddingLeft: space[11] }}>
                        {t('agentName')}
                      </Typography.Body>
                    </Box>
                    <Box flex={1}>
                      <Typography.Body color={colors.palette.fwdGreyDarker}>
                        {t('agentRoleAndCode')}
                      </Typography.Body>
                    </Box>
                  </Row>
                  {data.map((item, index) => {
                    return (
                      <Row
                        borderBottom={1}
                        borderBottomColor={colors.palette.fwdGrey[100]}
                        paddingY={space[3]}>
                        <Row flex={1}>
                          <RadioButton
                            value={item}
                            selected={
                              selectedValue?.agentCode === item.agentCode
                            }
                            onSelect={() => {
                              setSelectedValue(item);
                              handleSelectAgent?.(item);
                            }}
                            style={{ marginRight: space[5] }}
                          />
                          <Typography.LargeBody>
                            {item?.agentName}
                          </Typography.LargeBody>
                        </Row>
                        <Row flex={1}>
                          <Typography.LargeBody>
                            {item?.designation + ' - ' + item?.agentCode}
                          </Typography.LargeBody>
                        </Row>
                      </Row>
                    );
                  })}
                </View>
              </ScrollView>
              <Row gap={space[4]} justifyContent="center" alignItems="baseline">
                <Button
                  text={t('reset')}
                  variant="secondary"
                  style={{ width: sizes[50] }}
                  onPress={() => {
                    setSearchText('');
                    setSelectedValue(data[0]);
                    handleSelectAgent?.(data[0]);
                  }}
                />
                <Button
                  text={t('confirm')}
                  variant="primary"
                  style={{ width: sizes[50] }}
                  onPress={() => {
                    onClose();
                  }}
                />
              </Row>
            </View>
          </View>
        </ModalContainer>
      </Modal>
    </Portal>
  );
}

const ModalContainer = styled.View(({ theme: { colors, sizes, space } }) => ({
  alignItems: 'center',
  justifyContent: 'center',
  width: '100%',
  height: '100%',
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
}));
