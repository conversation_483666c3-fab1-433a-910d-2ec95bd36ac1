/**
 * This file contains the configuration for the data grid in report generation - Lapse report.
 */
import { t } from 'i18next';

export const FREEZE_HEADER = [
  {
    type: 'policyHolderName',
    title: t('reportGeneration:reportColumn.policyNumberAndpolicyHolderName'),
  },
];

export const HEADERS = [
  {
    type: 'agentCode',
    title: t('reportGeneration:reportColumn.agentCode'),
  },
  {
    type: 'agentName',
    title: t('reportGeneration:reportColumn.agentName'),
  },
  {
    type: 'product',
    title: t('reportGeneration:reportColumn.product'),
  },
  {
    type: 'policyHolderMobileNo',
    title: t('reportGeneration:reportColumn.policyHolderMobileNo'),
  },
  {
    type: 'registrationDate',
    title: t('reportGeneration:reportColumn.registrationDate'),
  },
  {
    type: 'submissionDate',
    title: t('reportGeneration:reportColumn.submissionDate'),
  },
  {
    type: 'issueDate',
    title: t('reportGeneration:reportColumn.issueDate'),
  },
  {
    type: 'premiumDueDate',
    title: t('reportGeneration:reportColumn.premiumDueDate'),
  },
  {
    type: 'frequency',
    title: t('reportGeneration:reportColumn.frequency'),
  },
  {
    type: 'paymentMethod',
    title: t('reportGeneration:reportColumn.paymentMethod'),
  },
  {
    type: 'status',
    title: t('reportGeneration:reportColumn.status'),
  },
  {
    type: 'policyTerm',
    title: t('reportGeneration:reportColumn.policyTerm'),
  },
  {
    type: 'premiumPaymentTerm',
    title: t('reportGeneration:reportColumn.premiumPaymentTerm'),
  },
  {
    type: 'modalPremium',
    title: t('reportGeneration:reportColumn.modalPremium'),
  },
  {
    type: 'duePremium',
    title: t('reportGeneration:reportColumn.duePremium'),
  },
  {
    type: 'ace',
    title: t('reportGeneration:reportColumn.ace'),
  },
  {
    type: 'address',
    title: t('reportGeneration:reportColumn.address'),
  },
  {
    type: 'policyHolderEmail',
    title: t('reportGeneration:reportColumn.policyHolderEmail'),
  },
];

export const CELL_RENDER_ORDER = HEADERS.map(header => header?.type);
export const DUE_DATE_REPORT_POST_BODY = HEADERS.map(header => ({
  label: header.title,
  path: header.type,
}));

DUE_DATE_REPORT_POST_BODY.unshift(
  {
    label: t('reportGeneration:reportColumn.policyNumber'),
    path: 'policyNumber',
  },
  {
    label: t('reportGeneration:reportColumn.policyHolderName'),
    path: 'policyHolderName',
  },
);
