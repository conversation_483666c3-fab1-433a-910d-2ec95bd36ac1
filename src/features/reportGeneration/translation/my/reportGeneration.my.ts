export default {
  reportGeneration: 'Report generation',
  lapsedPolicies: 'Lapsed policies',
  lapsedReport: 'Lapsed report',
  premiumReceived: 'Premium received',
  creditCardExpiration: 'Credit card expiration',
  unsuccessfulAdaAca: 'Unsuccessful ADA/ACA',
  policy: 'Policy anniversary',
  commission: 'Commission statement',
  tax: 'Tax statement',
  persistency: 'Persistency',
  reportSetting: 'Report setting',
  totalResult: 'Total results',
  information: 'Information',
  lapsed: 'Lapsed',
  premiumHoliday: 'Premium holiday',
  anticipatedLapse: 'Anticipated lapse',
  fullTable: 'Full table',
  applicationNotProceed: 'Application not proceed',
  dateType: 'Date type',
  datePeriod: 'Date period',
  dateRange: 'Date range',
  generate: 'Generate',
  dueDateReport: 'Due date Report',
  certificateTransactionReport: 'Certificate transaction report',
  certificateInquiriesReport: 'Certificate inquiries report',
  reportType: 'Report type',
  dueDate: 'Due date',
  registrationDate: 'Registration date',
  last2months: 'Last 2 months',
  last30days: 'Last 30 days',
  last180days: 'Last 180 days',
  Customise: 'Customise',
  certificateTransactionReportDetails:
    'Only one certificate number can be viewed in transaction history. Please enter the exact number.',
  certificateNumber: 'Certificate number',
  digitHint: 'Input 8 characters',
  general: 'General',
  BIRO: 'BIRO Angkasa',
  statusSelected: 'Status selected',
  allStatus: 'All status',
  displaying4500: 'Displaying 4500 results max',
  customise: 'Customise',
  reset: 'Reset',
  confirm: 'Confirm',
  export: 'Export',
  searchForStatus: 'Search for status',
  generalStatus: 'General status',
  pleaseSelect: 'Please select',
  selectAgent: 'Agent',
  agentName: 'Agent name',
  agentRoleAndCode: 'Agent role and agent code',
  search: 'Agent name/ agent code',

  // Report type
  'type.businessAndClienteleReport': 'Business & Clientele report',
  'type.myPersonalReport': 'My personal report',

  // Toolbar
  'toolbar.selectedAgent': 'Selected agent',
  'toolbar.team': 'Team',
  'toolbar.status': 'Status',
  'toolbar.policyHolder': 'Policy holder',
  'toolbar.policyInfo': 'Policy holder/number',
  'toolbar.transactionDate': 'Transaction date',
  'toolbar.issueDate': 'Issue date',
  'toolbar.policyAnniversaryPeriod': 'Policy anniversary period',
  'toolbar.certificateTransactionReport.error.hints':
    'Invalid number. Please try again.',

  // Action Panel
  'actionPanel.title.agent': 'Agent',
  'actionPanel.title.status': 'Status',
  'actionPanel.title.transactionDate': 'Transaction date',
  'actionPanel.title.datePeriod': 'Date period',
  'actionPanel.title.policyAnniversaryPeriod': 'Policy anniversary period',
  'actionPanel.title.searchForPolicyInfo':
    'Search for certificate holder/number',
  'actionPanel.subtitle.searchIndividual': 'Search individual',
  'actionPanel.subtitle.searchTip': 'Please fill in name or certificate number',
  'actionPanel.hint.policyNumber': 'Input full certificate number',
  'actionPanel.hint.dateRange':
    'Select date within 180 days before or after today',
  'actionPanel.placeholder.agentNameAgentCode': 'Agent name/ agent code',
  'actionPanel.placeholder.firstName': 'First name',
  'actionPanel.placeholder.lastName': 'Last name',
  'actionPanel.placeholder.policyHolderName': 'Name',
  'actionPanel.placeholder.policyNumber': 'Certificate number',
  'actionPanel.option.all': 'All',
  'actionPanel.option.individual': 'Individual',
  'actionPanel.option.thisMonth': 'This month',
  'actionPanel.option.lastThirtyDays': 'Last 30 days',
  'actionPanel.option.lastSixMonths': 'Last 180 days',
  'actionPanel.search': 'Search',
  'actionPanel.confirm': 'Confirm',
  'actionPanel.reset': 'Reset',
  'actionPanel.from': 'From',
  'actionPanel.to': 'To',
  'actionPanel.error.dateFrom':
    'Please select a start date earlier than the end date',
  'actionPanel.error.dateTo':
    'Please select a end date later than the start date',

  // Headers
  'header.month': 'Month',
  'header.downloadPDF': 'Download PDF',
  'header.policyNumber': 'Policy number',
  'header.policyHolderName': 'Policy holder name',
  'header.policyAnniversaryDate': 'Policy\nanniversary date',
  'header.paymentDate': 'Payment date',

  // Information modal
  'tips.title': 'Tips',
  'tips.message.1':
    'You may select your own name from the Agent list. Also if you are a leader, then the names from your team will show here and you can select them',
  'tips.message.premiumHoliday':
    'Policies which premium payment are covered by the balance in their account value',
  'tips.message.anticipatedLapse':
    'Policies which overdue premium payment but still within the 61 days grace period',
  'tips.message.Lapsed':
    'Policies which overdue premium payment beyond the 61 days grace period',
  'downloadPassword.title': 'Download Password',
  'downloadPassword.message.1':
    'Downloaded documents in this module are password encrypted.',
  'downloadPassword.message.2': `Enter the planner's birthday as the default password when opening the files using this format:`,
  'downloadPassword.message.3': 'Export can only support up to 4500 rows.',

  // Download Report
  'download.downloading': 'Downloading',
  'download.downloadSuccess': 'Downloaded successfully',

  // REPORT DATAGRIP COLUMN NAMES
  'reportColumn.policyNumberAndpolicyHolderName':
    'Certificate number/\nCertificate owner',
  'reportColumn.agentCode': 'Agent code',
  'reportColumn.agentName': 'Agent name',
  'reportColumn.product': 'Product',
  'reportColumn.registrationDate': 'Registration date',
  'reportColumn.decisionDate': 'Decision date',
  'reportColumn.status': 'Mandate status',
  'reportColumn.currency': 'Currency',
  'reportColumn.modalPremium': 'Modal contribution',
  'reportColumn.frequency': 'Frequency',
  'reportColumn.ace': 'ACE',
  'reportColumn.remark': 'Remark',
  'reportColumn.policyNumber': 'Certificate number',
  'reportColumn.policyHolderName': 'Certificate owner',
  'reportColumn.submissionDate': 'Submission date',
  'reportColumn.issueDate': 'Issue date',
  'reportColumn.premiumDueDate': 'Due date',
  'reportColumn.policyHolderMobileNo': 'Mobile phone',
  'reportColumn.paymentMethod': 'Payment method',
  'reportColumn.mandateStatus': 'Mandate status',
  'reportColumn.policyTerm': 'Certificate term',
  'reportColumn.premiumPaymentTerm': 'Contribution\npayment term',
  'reportColumn.rtu': 'RTU',
  'reportColumn.tranReference': 'Description',
  'reportColumn.transactionDate': 'Transaction date',
  'reportColumn.duePremium': 'Due contribution',
  'reportColumn.policyHolderEmail': 'Email',
  'reportColumn.address': 'Address',
};
