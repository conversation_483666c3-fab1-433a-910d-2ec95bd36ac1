export default {
  firstName: 'First Name',
  lastName: 'Last Name',
  interestedIn: 'Interested in',
  interestedTo: 'Interested to',
  leadSource: 'Lead source',
  leadScore: 'Lead score',
  leadOrigin: 'Lead origin',
  campaign: 'Campaign',
  from: 'From',
  'topTabBar.leads': 'Leads',
  'topTabBar.customers': 'Your customers',
  'topTabBar.leadConversion': 'Lead conversion',
  'search.headerSearchPlaceholder': 'Search your lead and customer',
  'search.headerSearchHeader': 'Search your lead or customer',
  'search.leadsHint': "e.g. Lead or customer's name, mobile",
  helpfulTips: 'Helpful tips!',
  noLeadToday:
    'No leads for today. Follow up closely to increase your chances of conversion.',
  noLeadOthers:
    'No leads in your contact list. Add new leads and start following up with them. Your lead’s data will not be shared.',
  addNewLead: 'Add new Lead',
  viewAllLeads: 'View all other leads',

  todayLeads: `Today's leads`,
  otherLeads: 'All other leads',

  'todayLeads.tooltip.title': 'Contact new lead within 24 hours. ',
  'todayLeads.tooltip.subTitle': 'It increases conversion rate by 50%! ',

  sortBy: 'Sort by ',
  expireSoon: 'Expiry soon',
  newestLead: 'Newest lead',
  createdDate: 'Created date',
  opportunityUpdateDate: 'Opportunity date',
  newest: 'Newest',
  oldest: 'Oldest',
  'status.not_contacted': 'Not contacted',
  'status.assigned': 'Assigned',
  'status.created': 'Not contacted',
  'status.consolidated': 'Consolidated',
  'status.updated': 'Updated',
  'status.recommended': 'Recommended',
  'status.contacted': 'Contacted',
  'status.appointment': 'Appointment',
  'status.illustration': 'Illustration',
  'status.declined': 'Declined',
  'status.deferred': 'Deferred',
  'status.submitted': 'Submitted',
  'status.issued': 'Application issued',
  'status.not_interested': 'Not interested',
  'status.deactivated': 'Deactivated',
  'status.inactive': 'Inactive',
  'status.cancelled': 'Cancelled',
  'source.individual': 'Individual',
  'source.entity': 'Organization',
  'source.SELF': 'Cube',
  'source.CUBE': 'Cube',
  'source.SMART': 'Smart',
  'source.IRIS': 'IRIS',
  'source.BLTS': 'eSource',
  'source.eSource': 'eSource',
  'source.ALTS': 'Affinity',
  'source.Affinity': 'Affinity',
  'source.Affiliate': 'Affiliate',
  'source.Marketing': 'Marketing',
  'source.SFMC': 'Marketing Campaign',
  'source.SFSC': 'Marketing',
  'source.ES': 'Elite Studio',
  'source.TM': 'Telemarketing',
  'source.YPC': 'Protection Score',
  'source.PLU': 'Protection Score',
  'source.PS': 'Protection Score - Existing Customer',
  'source.A-YPC': 'Protection Score (Affiliates)',
  'time.left': 'left',
  'activity.noAnswer':
    'It’s been 24 hours. Have you contacted the new lead, {{leadName}}?',
  'activity.interested':
    'Awesome!\nHave you contacted to the new lead {{leadName}} successfully?',
  'activity.notInterested':
    'No worries, keep trying! Were you able to contact your new lead, {{leadName}}?',
  'activity.deferred':
    'No worries. Don’t give up!\nHave you contacted to the new lead {{leadName}} successfully?',
  'activity.leadResponse': 'The lead’s response:',
  'activity.noAnswerLabel': 'No answer',
  'activity.interestedLabel': 'Interested',
  'activity.deferredLabel': 'Deferred',
  'activity.notInterestedLabel': 'Not interested',
  'activity.skipHint':
    'Please remember to log your lead\nactivities to track their statuses accurately',
  'activity.skipHint.tablet':
    'Please remember to log your lead activities to track their statuses accurately',
  'activity.skipAllButton': 'Skip all for now',
  'activity.haveNotContacted': 'Not contacted',
  'activity.callNow': 'Call now',
  'activity.skip': 'Skip',
  'activity.callFeedBack': "How was the lead's feedback?",
  'activity.loggedToast': 'Activity logged in profile',
  'activity.notInterestedPanelTitle': 'Reason of not being interested',
  'activity.notInterestedPanelTitle.tablet': "Why weren't they interested?",
  'activity.notInterestedExpensive': 'Premium too expensive',
  'activity.notInterestedUnsuitable': 'Unsuitable benefit',
  'activity.notInterestedWindow': 'Window shopping customer',
  'activity.notInterestedFeedback': 'Your feedback (optional)',
  'activity.notInterestedHint':
    'Thank you for meeting up with the prospect. You may use this form to report or share your feedback or any misuse by the prospect. We may also share your comments with another agent who may receive a request later on. ',
  'activity.toast': 'Well done! Updated {{count}} lead’s status.',
  'activity.leadSearchHint': 'e.g. Lead or customer’s name, mobile',
  'activity.callFailed.title': 'Unable to call',
  'activity.callFailed.message': 'Device cannot make and receive calls.',
  'search.noResults':
    'No results found.\n Adjust keyword to get better result or try another search.',
  'search.RecentSearchTitle': 'Recent search',
  'search.RecentSearchLead': 'Leads',
  'search.RecentSearchCustomer': 'Customer',
  'addLead.category.individual': 'Individual',
  'addLead.category.entity': 'Organization',
  'addLead.formFields.entityName': 'Organization name',
  'addLead.formFields.natureOfBusiness': 'Nature of business',
  'addLead.formFields.natureOfBusiness.optional':
    'Nature of business (optional)',
  'addLead.formFields.email': 'Email',
  'addLead.subTitle.primaryContactDetails': 'Contact details',
  'addLead.formFields.email.optional': 'Email (optional)',
  'addLead.subTitle.authorizedRepresentativeDetails':
    'Authorized representative’s details',
  'addLead.transferModal.title': 'Are you sure to change to {{targetPage}}?',
  'addLead.transferModal.content':
    'If you change to {{targetPage}}, the information you’ve filled in {{currentPage}} will be removed.',
  'addLead.button.label': 'Add new lead',
  'addLead.secureNote.title': 'We secure your own leads',
  'addLead.secureNote.body':
    'Your lead contacts will not be visible to other agents. It is only for you to centralise your leads and better manage your performance.',
  'addLead.secureNote.confirm': 'Got it',
  'addLead.uploadFromPhoneBook.buttonLabel': 'Upload from my phone book',
  'addLead.consent.back': 'Back',
  'addLead.consent.confirm': 'I confirm',
  'addLead.consent.statementPart1': `I, as FWD’s agent, certify and guarantee that I have received consent from the prospect to collect and use their personal information for the purpose of offering FWD’s insurance policy. I certify that`,
  'addLead.consent.statementFWDPrivacyPolicy': ` FWD’s Privacy Policy `,
  'addLead.consent.statementPart2': 'has been informed to that prospect.',
  'addLead.consent.statementPart0':
    'I, as FWD’s agent, certify and guarantee that I have received consent from',
  'addLead.consent.more': 'more...',
  'addLead.consent.close': 'Close',
  'addLead.formFields.addNewLead': 'Add a new lead',
  'addLead.formFields.isReady': 'is ready!',
  'addLead.formFields.code': 'Country code',
  'addLead.formFields.fullName': 'Full name',
  'addLead.formFields.phoneNumber': 'Phone number',
  'addLead.formFields.businessPhoneNumber': 'Business phone number',
  'addLead.formFields.countryCode': 'Country Code',
  'addLead.formFields.age': 'Age',
  'addLead.formFields.firstName': 'First name',
  'addLead.formFields.lastName': 'Last name',
  'addLead.formFields.mobileNumber': 'Mobile number',
  'addLead.formFields.CountryCode': 'Country Code',
  'addLead.formFields.subTitle': 'Optional information:',
  'addLead.formFields.gender.optional': 'Gender (optional)',
  'addLead.formFields.dateOfBirth': 'Date of birth',
  'addLead.formFields.dateOfBirth.optional': 'Date of birth (optional)',
  'addLead.formFields.interestedProductCategory':
    'Interested product (optional)',
  'addLead.formFields.interestedProductCategory.optional':
    'Interested product (optional)',
  'addLead.formFields.saveButton': 'Save',
  'addLead.formFields.cancel': 'Cancel',
  'addLead.existing.lead.found': 'Existing lead found',
  'addLead.existing.lead.tip':
    'We found someone having these information in your record.\nMobile: {{mobile}}\nEmail: {{email}}',
  'addLead.existing.lead.tip2':
    'We found someone having these information in your record.',
  'addLead.existing.lead.tip3': 'Mobile: {{mobile}}\nEmail: {{email}}',
  'addLead.existing.entity.found': 'Existing entity found',
  'addLead.existing.entity.tip':
    'We found someone having these information in your record.\nCompany name: {{companyName}}\nRegistration number (New): {{regNum}}',
  'addLead.formFields.newLeadIsAdd': 'A new lead is added.',
  'addLead.formFields.pleaseTryAgainLater': 'Please try again later.',
  'addLead.contactBook.noContact': `No Contact`,
  'addLead.contactBook.searchResult': `Search result ({{count}})`,
  'addLead.contactBook.searchResult.noResult': `No results found. \nAdjust keyword to get better result or try another search.`,
  'lead.proposal.noSavedProposal': 'No saved proposal',
  'lead.proposal.createNewProposal': 'Create new proposal',
  yourLeads: 'Your leads',
  conversionReport: 'Lead conversion Report',
  weekly: 'This week',
  monthly: 'This month',
  leadsConversionInThisWeek: 'Leads conversion in this week',
  leadsConversionInThisMonth: 'Leads conversion in this month',
  asOf: 'As of ',
  thisWeekTargetVSActualLeadNumber: 'This week target VS actual lead Number',
  thisMonthTargetVSActualLeadNumber: 'This month target VS actual lead Number',
  weeklyTarget: '— Weekly Target',
  monthlyTarget: '— Monthly Target',
  targetForYourNext7Days: 'Target of this week',
  targetOfThisMonth: 'Target of this month',
  targetOfThisWeek: 'Target of this week',
  emptyOperationData: 'Oops! Empty operation data.',
  setTarget: 'Set target',
  minimumRequirement: 'Target does not meet minimum requirement',
  igotIt: 'Ok, got it',
  okGotIt: 'Ok, got it',
  'lead.target.edit': 'Edit',
  'lead.target.week.number': 'Week {{number}}',
  thisMonthCycle: 'This month cycle',
  thisWeekCycle: 'This week cycle',
  notContact: 'Not Contact',
  contacted: 'Contacted',
  appointment: 'Appointment',
  illustration: 'Illustration',
  submitted: 'Submitted',
  'myTask.contactLeads': 'Contact leads',
  'myTask.emptyLeadsTasks':
    'You have no new leads today, but you can always check your other leads and keep other leads warm.',
  'myTask.contactLead': 'Contact lead',
  'myTask.newLead': 'New Lead',
  'myTask.newLead.interestedTo': 'Interested to ',
  'myTask.sendBirthdayCardTo': 'Send birthday card to',
  'myTask.birthday': 'Birthday',
  'myTask.emptyBirthdayTask':
    'There are no birthday stars today, but you can always check up-coming birthdays.',
  filterBy: 'Filter by',
  'filter.noResult':
    'No results found.\n Adjust filters to get better results.',
  'filter.panel.section.leadType': 'Lead type',
  'filter.panel.section.sourceOfLead': 'Source of lead',
  'filter.panel.section.leadOrigin': 'Lead origin',
  'filter.panel.section.campaignName': 'Campaign name',
  'filter.panel.section.statusOfLead': 'Status',
  'filter.panel.title': 'Filter by',
  'filter.panel.reset': 'Reset',
  'filter.panel.clearAll': 'Clear all',
  'filter.panel.apply': 'Apply',
  'filter.panel.showAll': 'Show all',
  'filter.panel.showLess': 'Show less',
  AFFILIATE_SOURCE: 'Affiliate',
  MYSELF_SOURCE: 'Myself',
  AYPC_SOURCE: 'Protection Score (Affiliates)',
  TM_SOURCE: 'Telemarketing',
  MARKETING_SOURCE: 'Marketing',
  YPC_SOURCE: 'Protection Score',
  PS_SOURCE: 'Protection Score - Existing Customer',
  PLU_SOURCE: 'People Like You',
  ELITE_STUDIO: 'Elite Studio',
  SFMC_SOURCE: 'Marketing Campaign',
  BANK_SOURCE: 'Bank',
  C2B_SOURCE: 'Click to buy',
  'lead.leadsName': 'Leads name',
  'lead.leadSource': 'From',
  'lead.status': 'Status',
  'lead.mobileNumber': 'Mobile number',
  'lead.customersName': 'Customers name',
  'lead.yourCustomers': 'Your customers',
  'lead.total': 'Total',
  'lead.gender': 'Gender',
  'lead.age': 'Age',
  'lead.leads.search.placeholder': 'Search lead or customer',
  'lead.searchResult': 'Search result',
  'lead.thisWeek': 'This week',
  'lead.thisMonth': 'This month',
  orSelect: 'Or select',
};
