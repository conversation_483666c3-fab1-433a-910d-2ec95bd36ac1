export default {
  firstName: 'First Name',
  lastName: 'Last Name',
  interestedIn: 'Interested in',
  interestedTo: 'Interested to',
  leadSource: 'From',
  from: 'From',
  leadOrigin: 'Lead origin',
  leadScore: 'Lead score',
  campaign: 'Campaign',
  'topTabBar.leads': 'Leads',
  'topTabBar.customers': 'Your customers',
  'topTabBar.leadConversion': 'Lead conversion',
  'search.headerSearchPlaceholder': 'Search your lead and customer',
  'search.headerSearchHeader': 'Search your lead or customer',
  'search.leadsHint':
    'e.g. Lead’s name, mobile, email, BLTS/ALTS reference number',
  helpfulTips: 'Helpful tips!',
  noLeadToday:
    'No leads for today.\nAdd new leads to increase your chance of conversion',
  noLeadOthers:
    'No leads in your contact list. Add new leads and start following up with them. Your lead’s data will not be shared.',
  addNewLead: 'Add new Lead',
  viewAllLeads: 'View all other leads',
  todayLeads: 'Leads in 24 hours',
  otherLeads: 'All other leads',
  'todayLeads.tooltip.title': 'Contact new lead within 24 hours. ',
  'todayLeads.tooltip.subTitle': 'It increases conversion rate by 50%! ',
  filterBy: 'Lead type',
  sortBy: 'Sort by ',
  expireSoon: 'Expiry soon',
  newestLead: 'Newest lead',
  createdDate: 'Created date',
  createInsured: 'Create insured',
  insuredDetails: 'Insured details',
  opportunityUpdateDate: 'Opportunity date',
  newest: 'Newest',
  oldest: 'Oldest',
  'status.not_contacted': 'Not contacted',
  'status.assigned': 'Assigned',
  'status.updated': 'Updated',
  'status.created': 'Not contacted',
  'status.contacted': 'Contacted',
  'status.appointment': 'Appointment',
  'status.illustration': 'Illustration',
  'status.consolidated': 'Consolidated',
  'status.recommended': 'Recommended',
  'status.declined': 'Declined',
  'status.submitted': 'Submitted',
  'status.deferred': 'Deferred',
  'status.cancelled': 'Cancelled',
  'status.deactivated': 'Deactivated',
  'status.not_interested': 'Not interested',
  'status.issued': 'Application issued',
  'source.individual': 'Individual',
  'source.entity': 'Entity',
  'source.SELF': 'Cube',
  'source.CUBE': 'Cube',
  'source.SMART': 'Smart',
  'source.IRIS': 'IRIS',
  'source.BLTS': 'BLTS',
  'source.eSource': 'eSource',
  'source.ALTS': 'Affinity',
  'source.Affinity': 'Affinity',
  'source.Affiliate': 'Affiliate',
  'source.Marketing': 'Marketing',
  'source.SFMC': 'Marketing',
  'source.SFSC': 'Marketing',
  'time.left': 'left',
  'activity.noAnswer':
    'It’s been 24 hours. Have you contacted the new lead, {{leadName}}?',
  'activity.interested':
    'Awesome!\nHave you contacted to the new lead {{leadName}} successfully?',
  'activity.notInterested':
    'No worries. Don’t give up!\nHave you contacted to the new lead {{leadName}} successfully?',
  'activity.deferred':
    'No worries. Don’t give up!\nHave you contacted to the new lead {{leadName}} successfully?',
  'activity.leadResponse': 'The lead’s response:',
  'activity.noAnswerLabel': 'No answer',
  'activity.interestedLabel': 'Interested',
  'activity.notInterestedLabel': 'Not interested',
  'activity.deferredLabel': 'Deferred',
  'activity.skipHint':
    'Please remember to log your lead\nactivities to track their statuses accurately',
  'activity.skipHint.tablet':
    'Please remember to log your lead activities to track their statuses accurately',
  'activity.skip': 'Skip',
  'activity.skipButton': 'Skip',
  'activity.skipAllButton': 'Skip logging for now',
  'activity.callNow': 'Call now',
  'activity.haveNotContacted': 'Not contacted',
  'activity.callFeedBack': "How was the lead's feedback?",
  'activity.loggedToast': 'Activity logged in profile',
  'activity.notInterestedPanelTitle': 'Reason of not being interested',
  'activity.notInterestedPanelTitle.tablet': 'Reason of not being interested',
  'activity.notInterestedExpensive': 'Premium too expensive',
  'activity.notInterestedUnsuitable': 'Unsuitable benefit',
  'activity.notInterestedWindow': 'Window shopping customer',
  'activity.notInterestedFeedback': 'Your feedback (optional)',
  'activity.notInterestedHint':
    'Thank you for meeting up with the prospect. You may use this form to report or share your feedback or any misuse by the prospect. We may also share your comments with another agent who may receive a request later on. ',
  'activity.toast': 'Well done! Updated {{count}} lead’s status.',
  'activity.leadSearchHint':
    'e.g. Lead or customer’s name, mobile, email, BLTS/ALTS reference number',
  'activity.callFailed.title': 'Unable to call',
  'activity.callFailed.message': 'Device cannot make and receive calls.',
  'search.noResults':
    'No results found.\n Adjust keyword to get better result or try another search.',
  'search.RecentSearchTitle': 'Recent search',
  'search.RecentSearchLead': 'Leads',
  'search.RecentSearchCustomer': 'Customer',
  'addLead.secureNote.title': 'We secure your own leads',
  'addLead.secureNote.body':
    'Your lead contacts will not be visible to other agents. It is only for you to centralise your leads and better manage your performance.',
  'addLead.secureNote.confirm': 'Got it',
  'addLead.uploadFromPhoneBook.buttonLabel': 'Upload from my phone book',
  'addLead.consent.back': 'Back',
  'addLead.consent.confirm': 'I confirm',
  'addLead.consent.statementPart1': `I, as FWD’s agent, certify and guarantee that I have received consent from the prospect to collect and use their personal information for the purpose of offering FWD’s insurance policy. I certify that`,
  'addLead.consent.statementFWDPrivacyPolicy': ` FWD’s Privacy Policy `,
  'addLead.consent.statementPart2': 'has been informed to that prospect.',
  'addLead.consent.statementPart0':
    'I, as FWD’s agent, certify and guarantee that I have received consent from',
  'addLead.consent.more': 'more...',
  'addLead.consent.close': 'Close',
  'addLead.category.individual': 'Individual',
  'addLead.category.lead': 'Lead',
  'addLead.category.entity': 'Entity',
  'addLead.button.label': 'Add new lead / entity',
  'addLead.subTitle.primaryContactDetails': 'Primary contact details',
  'addLead.subTitle.secondaryContactDetails': 'Secondary contact details',
  'addLead.subTitle.authorizedRepresentativeDetails':
    'Authorized representative’s details',
  'addLead.subTitle.referralDetails': 'Referral details',
  'addLead.formFields.addNewLead': 'Add a new lead',
  'addLead.formFields.isReady': 'is ready!',
  'addLead.formFields.code': 'Code',
  'addLead.formFields.countryCode': 'Country Code',
  'addLead.formFields.title': 'Salutation / Title',
  'addLead.formFields.firstName': 'First name',
  'addLead.formFields.middleName.optional': 'Middle name (optional)',
  'addLead.formFields.lastName': 'Last name',
  'addLead.formFields.extensionName.optional': 'Extension name (optional)',
  'addLead.formFields.mobileNumber': 'Mobile number',
  'addLead.formFields.CountryCode': 'Country Code',
  'addLead.formFields.subTitle': 'Optional information',
  'addLead.formFields.gender.optional': 'Gender (optional)',
  'addLead.formFields.dateOfBirth': 'Date of birth',
  'addLead.formFields.dateOfBirth.optional': 'Date of birth (optional)',
  'addLead.formFields.age': 'Age',
  'addLead.formFields.email': 'Email',
  'addLead.formFields.email.optional': 'Email (optional)',
  'addLead.formFields.interestedProductCategory': 'Interested product category',
  'addLead.formFields.interestedProductCategory.optional':
    'Interested product category (optional)',
  'addLead.formFields.entityName': 'Entity name',
  'addLead.formFields.businessPhoneNumber': 'Business phone number',
  'addLead.formFields.natureOfBusiness': 'Industry / Nature of business',
  'addLead.formFields.position': 'Position / Job title',
  'addLead.formFields.altsBltsRefNum': 'BLTS / ALTS reference number',
  'addLead.formFields.serviceBranch': 'Service branch',
  'addLead.formFields.referrerCode': 'Referrer code',
  'addLead.formFields.bankCustomerId': 'Bank customer ID',
  'addLead.formFields.saveButton': 'Save',
  'addLead.formFields.cancel': 'Cancel',
  'addLead.invalid.email': 'The email doesn’t exist. Please try again.',
  'addLead.existing.lead.found': 'Existing lead found',
  'addLead.existing.lead.tip':
    'We found someone having these information in your record.\nMobile: {{mobile}}\nEmail: {{email}}',
  'addLead.existing.lead.tip2':
    'We found someone having these information in your record.',
  'addLead.existing.lead.tip3': 'Mobile: {{mobile}}\nEmail: {{email}}',
  'addLead.existing.entity.found': 'Existing entity found',
  'addLead.existing.entity.tip':
    'We found someone having these information in your record.\nCompany name: {{companyName}}\nRegistration number (New): {{regNum}}',
  'addLead.contactBook.noContact': `No Contact`,
  'addLead.contactBook.searchResult': `Search result ({{count}})`,
  'addLead.contactBook.searchResult.noResult': `No results found. \nAdjust keyword to get better result or try another search.`,
  'addLead.transferModal.title': 'Are you sure to change to {{targetPage}}?',
  'addLead.transferModal.content':
    'If you change to {{targetPage}}, the information you’ve filled in {{currentPage}} will be removed.',
  'lead.proposal.noSavedProposal': 'No saved proposal',
  'lead.proposal.noSavedInsured': 'No saved insured',
  'lead.proposal.createNewProposal': 'Create new proposal',
  'lead.insured.noSavedInsured': 'No saved insured',
  'lead.insured.createInsured': 'Create insured',
  'lead.insured.loadingInsuredsMessage':
    'Please wait we are retrieving insured data',
  'lead.yourCustomers.customersName': 'Customers name',
  'lead.yourCustomers.gender': 'Gender',
  'lead.yourCustomers.age': 'Age',
  'lead.yourCustomers.mobileNumber': 'Mobile number',
  'lead.leads.search.placeholder': 'Search your lead',
  'lead.yourCustomers.search.placeholder': 'Search your customer',
  'insuredDetails.title': 'Insured details',
  'insuredDetails.personalInfo': 'Personal info',
  'insuredDetails.salutationTitle': 'Salutation/Title',
  'insuredDetails.firstName': 'First name',
  'insuredDetails.middleName': 'Middle name',
  'insuredDetails.lastName': 'Last name',
  'insuredDetails.extensionName': 'Extension name',
  'insuredDetails.gender': 'Gender',
  'insuredDetails.dateOfBirth': 'Date of birth',
  'insuredDetails.relationshipToOwner': 'Relationship to owner',
  'insuredDetails.contactDetails': 'Contact details',
  'insuredDetails.email': 'Email',
  'insuredDetails.mobileNumber': 'Mobile number',
  'createInsured.aboutPolicyInsured': 'About policy insured',
  'createInsured.personalInfo': 'Personal info',
  'createInsured.salutationTitle': 'Salutation/Title',
  'createInsured.firstName': 'First name',
  'createInsured.middleName': 'Middle name (optional)',
  'createInsured.lastName': 'Last name',
  'createInsured.extensionName': 'Extension name (optional)',
  'createInsured.dateOfBirth': 'Date of birth',
  'createInsured.age': 'Age',
  'createInsured.contactDetails': 'Contact details',
  'createInsured.relationshipToOwner': 'Relationship to owner',
  'createInsured.code': 'Code',
  'createInsured.mobile': 'Mobile',
  'createInsured.email': 'Email',
  'createInsured.save': 'Save',
  'createInsured.aboutInsured': 'About policy insured',
  'createInsured.insuredAdded': 'A new insured is added.',
  'createInsured.gender': 'Gender',
  'searchExistingLead.save': 'Save',
  'searchExistingLead.leadSearchHint':
    'e.g. Lead’s name, mobile, email, BLTS/ALTS',
  'searchExistingLead.headerSearchHeader': 'Search your lead',
  'lead.proposal.createNewInsured': 'Create insured',
  'lead.leadManagement': 'Leads',
  'lead.salesActivity': 'Sales activity',
  conversionReport: 'Conversion Report',
  weekly: 'Weekly',
  monthly: 'Monthly',
  leadsConversionInThisWeek: 'Leads conversion in this week',
  leadsConversionInThisMonth: 'Leads conversion in last 30 days',
  asOf: 'As of ',
  thisWeekTargetVSActualLeadNumber: 'This Week target VS Actual Lead Number',
  thisMonthTargetVSActualLeadNumber: 'This month target VS Actual Lead Number',
  weeklyTarget: '- Weekly Target',
  monthlyTarget: '- Monthly Target',
  targetForYourNext7Days: 'Target for your next 7 Days',
  targetOfThisMonth: 'Target of this month',
  targetOfThisWeek: 'Target of this week',
  emptyOperationData: 'Oops! Empty operation data.',
  setTarget: 'Set target',
  minimumRequirement: 'Target does not meet minimum requirement',
  igotIt: 'ok,I got it',
  'lead.target.edit': 'Edit',
  'lead.target.week.number': 'Week {{number}}',
  thisMonthCycle: 'This month cycle',
  thisWeekCycle: 'This week cycle',
  notContact: 'Not Contact',
  contacted: 'Contacted',
  appointment: 'Appointment',
  illustration: 'Illustration',
  submitted: 'Submitted',
  'myTask.contactLeads': 'Contact leads',
  'myTask.emptyLeadsTasks':
    'You have no new leads today, but you can always check your other leads and keep other leads warm.',
  'myTask.contactLead': 'Contact lead',
  'myTask.newLead': 'New Lead',
  'myTask.sendBirthdayCardTo': 'Send birthday card to',
  'myTask.birthday': 'Birthday',
  'myTask.emptyBirthdayTask':
    'There are no birthday stars today, but you can always check up-coming birthdays.',
  'filter.noResult':
    'No results found.\n Adjust filters to get better results.',
  'filter.panel.section.leadType': 'Lead type',
  'filter.panel.section.sourceOfLead': 'Source of lead',
  'filter.panel.section.leadOrigin': 'Lead origin',
  'filter.panel.section.campaignName': 'Campaign name',
  'filter.panel.section.statusOfLead': 'Status',
  'filter.panel.title': 'Filter by',
  'filter.panel.reset': 'Reset',
  'filter.panel.clearAll': 'Clear all',
  'filter.panel.apply': 'Apply',
  'filter.panel.showAll': 'Show all',
  'filter.panel.showLess': 'Show less',
  'validation.requiredInput': 'Required field',
  'validation.invalidFormat': 'Invalid format',
  'validation.invalidDate': 'Invalid date',
  'validation.insuredMinimumAge': 'Age cannot be less than 18 years old.',
  okGotIt: 'Ok, got it',
  orSelect: 'Or select',
};
