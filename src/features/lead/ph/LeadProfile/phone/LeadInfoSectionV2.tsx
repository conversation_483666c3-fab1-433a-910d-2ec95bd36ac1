import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { Box, Column, Icon, Row, Typography } from 'cube-ui-components';
import { LinearGradient } from 'expo-linear-gradient';
import EntityAvatarSVG from 'features/lead/components/LeadProfile/assets/icons/EntityAvatarSVG';
import FemaleAvatarSVG from 'features/lead/components/LeadProfile/assets/icons/FemaleAvatarSVG';
import MaleAvatarSVG from 'features/lead/components/LeadProfile/assets/icons/MaleAvatarSVG';
import NewFemaleAvatarSVG from 'features/lead/components/LeadProfile/assets/icons/NewFemaleAvatarSVG';
import LeadScore from 'features/lead/components/LeadProfile/LeadScore';
import ScoreInfoModal from 'features/lead/components/LeadProfile/ScoreInfoModal';
import { getSourceLabels } from 'features/lead/utils';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCustomerProfileByCustomerId } from 'hooks/useGetCustomerProfile';
import { useGetLeadByLeadId } from 'hooks/useGetLeads';
import { useGetOptionList } from 'hooks/useGetOptionList';
import i18n from 'i18next';
import _ from 'lodash';
import React, { useEffect, useImperativeHandle, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { LayoutChangeEvent, Pressable, TouchableOpacity } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { RootStackParamList } from 'types';
import { country } from 'utils/context';
import { calculateAge } from 'utils/helper/calculateAge';
import getLabelFromValue from 'utils/helper/getLabelFromValue';

export interface ShowLeadInfoHandle {
  hideComponent: () => void;
  showComponent: () => void;
}

interface LeadProps {
  isIndividualLead?: boolean;
  isCustomer?: boolean;
  customerId?: string;
  isMarketingLead?: boolean;
}

const LeadInfoSectionV2 = React.forwardRef<ShowLeadInfoHandle, LeadProps>(
  ({ isIndividualLead, isCustomer, customerId, isMarketingLead }, ref) => {
    const { t } = useTranslation(['lead', 'leadProfile']);
    const { colors, space, animation, sizes, borderRadius } = useTheme();
    const navigation = useNavigation<NavigationProp<RootStackParamList>>();

    const leadId = useBoundStore().lead.inquiringLeadId;
    const { setAppLoading, setAppIdle } = useBoundStore(
      store => store.appActions,
    );

    const [isModalVisible, setIsModalVisible] = useState(false);

    const { data: optionList } = useGetOptionList();
    const { isLoading: isGetCustomerLoading, data: customer } =
      useGetCustomerProfileByCustomerId(customerId);
    const { isLoading: isGetLeadLoading, data: lead } =
      useGetLeadByLeadId(leadId);
    const isLoading = isCustomer ? isGetCustomerLoading : isGetLeadLoading;

    /**
     * Info layout dimensions
     */
    const [infoLayout, setInfoLayout] = useState({ width: 0, height: 0 });
    const onLayout = (e: LayoutChangeEvent) => {
      const layoutHeight = e.nativeEvent.layout.height;
      const layoutWidth = e.nativeEvent.layout.width;
      if (infoLayout.width == 0 || layoutWidth != infoLayout.width) {
        setInfoLayout({ width: layoutWidth, height: layoutHeight });
      }
    };

    useEffect(() => {
      return () => {
        // Reset the Layout height when unmounting
        setInfoLayout({ width: 0, height: 0 });
      };
    }, []);

    /**
     * Animated component (hide/ show)
     */
    useImperativeHandle(ref, () => ({
      hideComponent: () => hideComponent(),
      showComponent: () => showComponent(),
    }));

    const opacity = useSharedValue(1);
    const height = useSharedValue(true);
    const ANIMATION_DURATION = animation.duration;

    const hideComponent = () => {
      opacity.value = withTiming(0, { duration: ANIMATION_DURATION * 0.75 });
      height.value = false;
    };

    const showComponent = () => {
      opacity.value = withTiming(1, { duration: ANIMATION_DURATION * 1.75 });
      height.value = true;
    };

    const animatedStyle = useAnimatedStyle(() => {
      if (infoLayout.width === 0) return {};
      return {
        opacity: opacity.value,
        height:
          height.value === true
            ? withTiming(infoLayout.height, { duration: ANIMATION_DURATION })
            : withTiming(0, { duration: ANIMATION_DURATION }),
      };
    });

    /**
     * Personal info
     * - If customer, use customer data
     * - If lead, use lead data
     */
    const personalInfo = {
      firstName: isCustomer ? customer?.firstName : lead?.firstName,
      middleName: isCustomer ? customer?.middleName : lead?.middleName,
      lastName: isCustomer ? customer?.lastName : lead?.lastName,
      genderCode: isCustomer ? customer?.gender : lead?.genderCode,
      birthDate: isCustomer ? customer?.birthDate : lead?.birthDate,
      maritalStatusCode: isCustomer
        ? customer?.maritalStatus
        : lead?.maritalStatusCode,
      source: isCustomer ? [] : lead?.sourceIds, // origin(?)
    };

    const firstName = personalInfo?.firstName
      ? personalInfo?.firstName + ' '
      : '';
    const middleName = personalInfo?.middleName
      ? personalInfo?.middleName + ' '
      : '';
    const lastName = personalInfo?.lastName ? personalInfo?.lastName : '';
    const fullName = firstName + middleName + lastName;
    const gender =
      getLabelFromValue(
        optionList?.GENDER?.options,
        personalInfo?.genderCode,
      ) ?? undefined;
    const age = personalInfo?.birthDate
      ? calculateAge(new Date(personalInfo?.birthDate))
      : undefined;
    const genderAndAge = concatGenderAndAge(gender, age);
    const maritalStatus =
      getLabelFromValue(
        optionList?.MARITAL_STATUS?.options,
        personalInfo?.maritalStatusCode,
      ) ?? undefined;
    const source = personalInfo?.source ?? [];
    const mappedSource = getSourceLabels(source)
      .map(label => t(`lead:source.${label}`))
      .join(', ');

    /**
     * Marketing lead data
     */
    const leadScore = lead?.extra?.engagement_score;

    /**
     * Customer data
     */
    const CUSTOMER_DATA = [
      {
        type: 'customerOrigin',
        label: 'Customer origin',
        value: mappedSource,
      },
    ];

    /**
     * Individual lead data
     */
    const INDIVIDUAL_LEAD_DATA = [
      {
        type: 'leadOrigin',
        label: t('leadProfile:leadProfile.profileDetails.leadOrigin'),
        value: mappedSource,
      },
      {
        type: 'leadSource',
        label: t('leadProfile:leadProfile.profileDetails.leadSource'),
        value: lead?.extra?.contact_history,
      },
      {
        type: 'interestProduct',
        label: 'Interest product',
        value: !_.isEmpty(lead?.interestedProducts)
          ? lead?.interestedProducts?.join(',\n')
          : !_.isEmpty(lead?.interestedCategories)
          ? lead?.interestedCategories?.join(', ')
          : undefined,
      },
      {
        type: 'campaignName',
        label: 'Campaign name',
        value: lead?.campaignName,
      },
    ];

    /**
     * Avatar
     */
    const isAvatarVisible =
      !isIndividualLead ||
      (isIndividualLead && lead?.genderCode) ||
      (isCustomer && customer?.gender);

    const avatarHandler = () => {
      if (isIndividualLead || isCustomer) {
        if (personalInfo?.genderCode === 'M') return <MaleAvatarSVG />;
        if (personalInfo?.genderCode === 'F') {
          if (country === 'id') {
            return <NewFemaleAvatarSVG />;
          }
          return <FemaleAvatarSVG />;
        }
      }
      if (!isIndividualLead) return <EntityAvatarSVG />;
      return null;
    };

    /**
     * View profile navigation
     */
    const onViewProfilePress = () => {
      isCustomer
        ? navigation.navigate('CustomerProfileDetails', {
            customerId: String(customerId),
          })
        : navigation.navigate('ProfileDetails');
    };

    /**
     * Loading state
     */
    useEffect(() => {
      isLoading ? setAppLoading() : setAppIdle();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isLoading]);

    if (!lead && !customer) return <></>;

    return (
      <>
        <Animated.View
          onLayout={onLayout}
          style={[infoLayout.width > 0 && animatedStyle]}>
          <LinearGradient
            colors={[
              colors.background,
              colors.palette.fwdOrange[5],
              colors.palette.fwdOrange[5],
              colors.palette.fwdOrange[20],
              colors.palette.fwdOrange[20],
            ]}
            style={{ position: 'absolute', height: '100%', width: '100%' }}
            start={{ x: 0, y: 0 }}
            end={{ x: 0, y: 1 }}
          />

          <Column px={space[4]} pt={space[3]}>
            <Row>
              <Column width={isAvatarVisible ? '65%' : '100%'}>
                {isIndividualLead || isCustomer ? (
                  /**
                   * Individual lead, Customer
                   */
                  <Column gap={space[2]}>
                    <Typography.H5
                      numberOfLines={5}
                      fontWeight="bold"
                      children={fullName}
                    />

                    {genderAndAge && (
                      <Typography.LargeLabel
                        children={genderAndAge}
                        color={colors.palette.fwdGreyDarker}
                      />
                    )}

                    {maritalStatus && (
                      <Typography.LargeLabel
                        children={maritalStatus}
                        color={colors.palette.fwdGreyDarker}
                      />
                    )}
                  </Column>
                ) : (
                  /**
                   * Entity lead
                   */
                  <Column gap={space[2]}>
                    <Typography.H5
                      numberOfLines={5}
                      fontWeight="bold"
                      children={lead?.companyName}
                    />
                    <Typography.LargeLabel
                      children={lead?.occupationIndustryCode ?? '--'}
                      color={colors.palette.fwdGreyDarker}
                    />
                  </Column>
                )}

                <TouchableOpacity
                  onPress={() => onViewProfilePress()}
                  style={{
                    flexDirection: 'row',
                    alignSelf: 'flex-start',
                    alignItems: 'center',
                    gap: space[1],
                    //
                    marginVertical: space[2],
                    paddingVertical: space[2],
                  }}>
                  <Icon.ArrowRight
                    size={sizes[5]}
                    fill={colors.palette.fwdAlternativeOrange[100]}
                  />
                  <Typography.H7
                    fontWeight="bold"
                    children={t(
                      'leadProfile:leadProfile.leadInfoSection.viewProfile',
                    )}
                    color={colors.palette.fwdAlternativeOrange[100]}
                  />
                </TouchableOpacity>
              </Column>

              {isAvatarVisible && (
                <Box position="absolute" right={5} top={0}>
                  {avatarHandler()}
                </Box>
              )}
            </Row>

            <Box
              p={space[4]}
              bgColor={colors.background}
              borderRadius={borderRadius.large}
              style={{
                shadowColor: colors.primary,
                shadowOffset: { width: 0, height: 0 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 7,
              }}>
              {isIndividualLead || isCustomer ? (
                /**
                 * Individual lead, Customer
                 */
                <Column gap={space[2]}>
                  {isMarketingLead && leadScore && (
                    <Row alignItems="center">
                      <Row flex={5} alignItems={'center'} gap={space[1]}>
                        <Typography.H7
                          children={t(
                            'leadProfile:leadProfile.leadInfoSection.leadScore',
                          )}
                          color={colors.palette.fwdGreyDarker}
                        />
                        <Pressable onPress={() => setIsModalVisible(true)}>
                          <Icon.InfoCircle
                            size={sizes[4]}
                            fill={colors.palette.fwdAlternativeOrange[100]}
                          />
                        </Pressable>
                      </Row>

                      <Row flex={7}>{LeadScore({ leadScore: leadScore })}</Row>
                    </Row>
                  )}

                  {isCustomer ? (
                    <>
                      {CUSTOMER_DATA?.map(({ type, label, value }) => {
                        return (
                          <Row key={type} gap={space[1]}>
                            <Typography.H7
                              children={label}
                              color={colors.palette.fwdGreyDarker}
                              style={{ flex: 5, paddingRight: space[2] }}
                            />
                            <Typography.H7
                              children={value ? value : '--'}
                              style={{ flex: 7, paddingRight: space[0] }}
                            />
                          </Row>
                        );
                      })}
                    </>
                  ) : (
                    <>
                      {INDIVIDUAL_LEAD_DATA?.map(({ type, label, value }) => {
                        if (!value) return null;
                        if (!isMarketingLead && type === 'leadSource')
                          return null;
                        if (!isMarketingLead && type === 'campaignName')
                          return null;
                        return (
                          <Row key={type} gap={space[1]}>
                            <Typography.H7
                              children={label}
                              color={colors.palette.fwdGreyDarker}
                              style={{ flex: 5, paddingRight: space[2] }}
                            />
                            <Typography.H7
                              children={value}
                              style={{ flex: 7, paddingRight: space[0] }}
                            />
                          </Row>
                        );
                      })}
                    </>
                  )}
                </Column>
              ) : (
                /**
                 * Entity lead
                 */
                <Row gap={space[1]}>
                  <Typography.H7
                    children={t(
                      'leadProfile:leadProfile.profileDetails.leadOrigin',
                    )}
                    color={colors.palette.fwdGreyDarker}
                    style={{ flex: 5, paddingRight: space[2] }}
                  />
                  <Typography.H7
                    children={mappedSource}
                    style={{ flex: 7, paddingRight: space[0] }}
                  />
                </Row>
              )}
            </Box>
          </Column>

          <Box h={space[5]} bgColor={colors.palette.fwdOrange[20]} />
        </Animated.View>

        <ScoreInfoModal
          version="v2"
          isVisible={isModalVisible}
          onClose={() => setIsModalVisible(false)}
        />
      </>
    );
  },
);

export default LeadInfoSectionV2;

const concatGenderAndAge = (gender?: string, age?: number | '') => {
  const yearsOld = i18n.t('leadProfile:leadProfile.leadInfoSection.yearsOld');
  if (!gender && !age) return null;
  if (gender && age) return `${gender}・${age + ' ' + yearsOld}`;
  if (gender) return gender;
  if (age) return age + ' ' + yearsOld;
  return null;
};
