import styled from '@emotion/native';
import { Theme, useTheme } from '@emotion/react';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { FlashList } from '@shopify/flash-list';
import ResponsiveView from 'components/ResponsiveView';
import {
  Body,
  H7,
  H8,
  Icon,
  Row,
  SearchBar,
  SearchBarRef,
  SmallBody,
  Toggle,
  ToggleGroup,
  Typography,
} from 'cube-ui-components';
import { LeadItem } from 'features/lead/components/LeadItem';
import { NoResults } from 'features/lead/components/NoResult';
import useBoundStore from 'hooks/useBoundStore';
import { useGetNewCustomerProfile } from 'hooks/useGetCustomerProfile';
import { useSearchCustomers } from 'hooks/useGetCustomers';
import { useSearchLead } from 'hooks/useGetLeads';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import CustomHeaderBackButton from 'navigation/components/HeaderBackButton';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Platform, ScrollView, TouchableOpacity, View } from 'react-native';
import { EdgeInsets, useSafeAreaInsets } from 'react-native-safe-area-context';
import { RootStackParamList } from 'types';
import { NewCustomerProfile } from 'types/customerProfile';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';
import Fuse from 'fuse.js';
import CustomerItem from 'features/lead/phone/ib/CustomerItem';
import { country } from 'utils/context';

type searchTypeProps = 'leads' | 'customers';
export default function LeadAndCustomerSearch() {
  const theme = useTheme();
  const { sizes, colors, space } = theme;
  const navigation =
    useNavigation<NativeStackNavigationProp<RootStackParamList>>();
  const insets = useSafeAreaInsets();

  const [value, setValue] = useState<searchTypeProps>('leads');

  const [query, setQuery] = useState('');
  const {
    data: leadResult,
    isFetching: isFetchingLead,
    remove: removeLeadQuery,
  } = useSearchLead({
    queryText: query,
    enabled: Boolean(query),
  });

  // Customer Part
  const {
    data: customerData,
    isFetching: isFetchingCustomer,
    remove: removeCustomerQuery,
  } = useGetNewCustomerProfile(country !== 'id');

  const dataFuse = useMemo<Fuse<NewCustomerProfile> | null>(() => {
    if (!customerData || customerData.length === 0) return null;

    return new Fuse(customerData, {
      keys: ['displayNameEN', 'phoneMobile'],
      threshold: 0.01,
      ignoreLocation: true,
      useExtendedSearch: true,
    });
  }, [customerData]);

  const customerResult = useMemo(() => {
    if (!dataFuse) return [];

    return query ? dataFuse.search(query).map(customer => customer.item) : [];
  }, [customerData, dataFuse, query]);

  const resetSearch = () => {
    setQuery('');
    removeCustomerQuery();
    removeLeadQuery();
  };

  const onPressBack = () => {
    resetSearch();
    navigation.goBack();
  };

  useEffect(() => {
    return () => {
      resetSearch();
    };
  }, []);

  const isFetching = isFetchingLead || isFetchingCustomer;

  const leadsData = leadResult?.data || [];

  const { customersData, numberOfCustomers } = useMemo(() => {
    if (!customerResult) {
      return { customersData: [], numberOfCustomers: 0 };
    }
    return {
      customersData: customerResult || [],
      numberOfCustomers: customerResult.length,
    };
  }, [customerResult]);

  const numberOfLeads = leadResult?.data.length || 0;

  // From Ticket CUBEFIB-4606: By default, select a filter that actually have search results
  // If both filters have results, or if both filters have no results, then by default select the “Leads” filter
  useEffect(() => {
    // ID doesn't have customer search
    if (country === 'id') {
      return;
    }
    // Only set the value if both leads and customers have finished fetching
    if (!isFetchingLead && !isFetchingCustomer) {
      if (numberOfLeads && numberOfCustomers) {
        setValue('leads');
      } else if (!numberOfLeads && numberOfCustomers) {
        setValue('customers');
      } else {
        setValue('leads');
      }
    }
  }, [query, isFetchingLead, isFetchingCustomer]);

  const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();
  const { t } = useTranslation(['lead']);
  const searchRef = useRef<SearchBarRef>(null);
  const [sortBy, setSortBy] = useState<'newest' | 'oldest'>('newest');

  const {
    leadSearch: { recentSearchLeadItem, recentSearchCustomerItem },
    leadSearchActions: {
      updateRecentLeadSearch,
      updateRecentCustomerSearch,
      trimRecentSearch,
    },
  } = useBoundStore();

  const handleSearch = (text: string) => {
    setQuery(text);
  };
  const handleSelect = (value: searchTypeProps) => {
    setValue(value);
  };
  const handleClearBtn = () => {
    searchRef.current?.onClear();
    searchRef.current?.input.focus();
    setQuery('');
  };
  useEffect(() => {
    if (
      recentSearchLeadItem.length > 3 ||
      recentSearchCustomerItem.length > 3
    ) {
      trimRecentSearch();
    }
  }, [recentSearchLeadItem, recentSearchCustomerItem]);

  const SearchResultComponent = () => {
    if (value === 'leads') {
      return (
        <>
          {country !== 'id' && (
            <SearchResultTextContainer>
              <Typography.Body color={colors.palette.fwdGreyDarkest}>
                Search results ({numberToThousandsFormat(leadsData.length)})
              </Typography.Body>
              <SortDataByArrow
                sortBy={sortBy}
                onPressDate={() =>
                  setSortBy(sortBy === 'newest' ? 'oldest' : 'newest')
                }
              />
            </SearchResultTextContainer>
          )}
          <FlashList
            contentContainerStyle={{
              paddingHorizontal: isNarrowScreen ? sizes[3] : sizes[4],
              backgroundColor: colors.surface,
              paddingVertical: isNarrowScreen ? sizes[3] : sizes[4],
            }}
            renderItem={({ item }) => (
              <LeadItem
                {...item}
                isToday={
                  item.createdAt
                    ? Date.parse(item.createdAt) - new Date().getTime() <= 1
                    : false
                }
                onPress={() => {
                  updateRecentLeadSearch(item);
                  navigation.navigate('LeadProfile', {
                    id: String(item.id),
                    isIndividualLead: item.isIndividual,
                  });
                }}
              />
            )}
            data={sortBy === 'newest' ? leadsData.slice().reverse() : leadsData}
            keyExtractor={({ id, firstName }) => `lead_${id}_${firstName}`}
            estimatedItemSize={sizes[28]}
            ListEmptyComponent={!isFetching ? <NoResults /> : <></>}
          />
        </>
      );
    } else if (value === 'customers') {
      return (
        <>
          <SearchResultTextContainer>
            <Typography.Body color={colors.palette.fwdGreyDarkest}>
              Search results ({numberToThousandsFormat(customersData.length)})
            </Typography.Body>
          </SearchResultTextContainer>
          <FlashList
            contentContainerStyle={{
              paddingHorizontal: isNarrowScreen ? sizes[3] : sizes[4],
              backgroundColor: colors.surface,
              paddingVertical: isNarrowScreen ? sizes[3] : sizes[4],
            }}
            renderItem={({ item }) => (
              <CustomerItem
                {...item}
                onPress={() => {
                  updateRecentCustomerSearch(item);
                  navigation.navigate('LeadProfile', {
                    id: '',
                    customerId: item.customerID,
                  });
                }}
              />
            )}
            data={customersData}
            keyExtractor={({ customerID, displayNameEN }) =>
              `customer_${customerID}_${displayNameEN}`
            }
            estimatedItemSize={sizes[16]}
            ListEmptyComponent={!isFetching ? <NoResults /> : <></>}
          />
        </>
      );
    }
    return null;
  };

  return (
    <>
      <SearchContainer insets={insets}>
        <HeaderContainer
          narrowStyle={{ height: sizes[11], paddingTop: sizes[6] }}>
          <ResponsiveView
            narrowStyle={{ alignItems: 'flex-end', paddingLeft: sizes[1] }}
            style={{ flex: 1, alignItems: 'flex-start' }}>
            <CustomHeaderBackButton
              fill={theme.colors.background}
              size={25}
              onPressBack={onPressBack}
            />
          </ResponsiveView>
          <H7
            fontWeight="bold"
            color={theme.colors.background}
            style={{ flex: 4, textAlign: 'center' }}>
            {t('lead:search.headerSearchHeader')}
          </H7>
          <View style={{ flex: 1 }}></View>
        </HeaderContainer>
        <SearchBarContainer narrowStyle={{ paddingHorizontal: space[3] }}>
          <SearchBar
            onChangeQuery={() => null}
            ref={searchRef}
            variant={'round'}
            placeholder=""
            collapseOnBlur={false}
            showClearBtnAfterQuery
            inputProps={{
              enablesReturnKeyAutomatically: true,
              returnKeyType: 'search',
              autoComplete: 'off',
              autoCorrect: false,
              spellCheck: false,
              onSubmitEditing(e) {
                handleSearch(e.nativeEvent.text);
              },
              autoFocus: true,
              style: { fontSize: sizes[4] },
            }}
            // iconLeftProps={{ fill: colors.palette.fwdGreyDark }}
            iconLeft={
              <ResponsiveView style={{ paddingRight: sizes[1] }}>
                <Icon.Search fill={colors.palette.fwdGreyDark} />
              </ResponsiveView>
            }
            isLoading={isFetching}
            activeBorderColor={colors.primary}
            inactiveBorderColor={colors.primary}
            style={{
              height: isWideScreen ? sizes[13] : sizes[12],
              paddingVertical: sizes[1],
            }}
            focusAfterClear
            debouncedDelay={0}
            onPressIconRight={handleClearBtn}
          />
        </SearchBarContainer>
        <SearchHintContainer narrowStyle={{ paddingHorizontal: space[3] }}>
          <SearchHint>
            {country === 'id'
              ? t('lead:search.leadsHint')
              : t('lead:activity.leadSearchHint')}
          </SearchHint>
        </SearchHintContainer>

        {country !== 'id' && (
          <ToggleGroup
            value={value}
            onChange={selected => handleSelect(selected as searchTypeProps)}>
            <ToggleGroupContainer narrowStyle={{ paddingHorizontal: space[3] }}>
              <Toggle value="leads" mode={'flat'}>
                {({ color, selected }) => (
                  <ToggleChildren selected={selected}>
                    <ToggleBody
                      color={colors.background}
                      fontWeight={selected ? 'bold' : 'normal'}>
                      {`Leads`}
                    </ToggleBody>
                  </ToggleChildren>
                )}
              </Toggle>
              <Toggle value="customers" mode={'flat'}>
                {({ color, selected }) => (
                  <ToggleChildren selected={selected}>
                    <ToggleBody
                      color={colors.background}
                      fontWeight={selected ? 'bold' : 'normal'}>
                      {`Customers`}
                    </ToggleBody>
                  </ToggleChildren>
                )}
              </Toggle>
            </ToggleGroupContainer>
          </ToggleGroup>
        )}
      </SearchContainer>
      <SearchResultContainer>
        {query ? (
          <SearchResultComponent />
        ) : (
          <>
            {(recentSearchLeadItem.length > 0 ||
              recentSearchCustomerItem.length > 0) && (
              <ScrollView
                keyboardDismissMode="on-drag"
                keyboardShouldPersistTaps="never">
                <RecentSearchContainer>
                  <H7
                    fontWeight="bold"
                    color={colors.secondaryVariant}
                    style={{
                      paddingHorizontal: sizes[4],
                      paddingTop: sizes[3],
                    }}>
                    {t('lead:search.RecentSearchTitle')}
                  </H7>

                  {!!recentSearchLeadItem.length && (
                    <>
                      <RecentSearchCategoryTitle>
                        {`${t('lead:search.RecentSearchLead')} (${
                          recentSearchLeadItem?.length
                        })`}
                      </RecentSearchCategoryTitle>
                      <FlashList
                        contentContainerStyle={{
                          paddingHorizontal: isNarrowScreen
                            ? sizes[3]
                            : sizes[4],
                          backgroundColor: colors.surface,
                          // paddingVertical: sizes[4],
                        }}
                        renderItem={({ item }) => {
                          return (
                            <LeadItem
                              {...item}
                              isToday={
                                item.createdAt
                                  ? Date.parse(item.createdAt) -
                                      new Date().getTime() <=
                                    1
                                  : false
                              }
                              onPress={() => {
                                updateRecentLeadSearch(item);
                                navigation.navigate('LeadProfile', {
                                  id: String(item.id),
                                  isIndividualLead: item.isIndividual,
                                });
                              }}
                            />
                          );
                        }}
                        data={recentSearchLeadItem}
                        keyExtractor={({ id, firstName }, i) =>
                          `lead_${id}_${firstName}_${i}`
                        }
                        estimatedItemSize={3}
                      />
                    </>
                  )}

                  {!!recentSearchCustomerItem.length && (
                    <>
                      <RecentSearchCategoryTitle>
                        {`${t('lead:search.RecentSearchCustomer')} (${
                          recentSearchCustomerItem?.length
                        })`}
                      </RecentSearchCategoryTitle>
                      <FlashList
                        contentContainerStyle={{
                          paddingHorizontal: isNarrowScreen
                            ? sizes[3]
                            : sizes[4],
                          backgroundColor: colors.surface,
                          paddingBottom: sizes[2],
                        }}
                        renderItem={({ item }) => {
                          return (
                            <CustomerItem
                              {...item}
                              onPress={() => {
                                updateRecentCustomerSearch(item);
                                navigation.navigate('LeadProfile', {
                                  id: '',
                                  customerId: item.customerId,
                                });
                              }}
                            />
                          );
                        }}
                        data={recentSearchCustomerItem}
                        keyExtractor={({ customerId, firstName }, i) =>
                          `lead_${customerId}_${firstName}_${i}`
                        }
                        estimatedItemSize={3}
                      />
                    </>
                  )}
                </RecentSearchContainer>
              </ScrollView>
            )}
          </>
        )}
      </SearchResultContainer>
    </>
  );
}
const SearchContainer = styled.View(
  ({ theme, insets }: { insets: EdgeInsets; theme?: Theme }) => ({
    paddingTop: insets.top,
    backgroundColor: theme?.colors.primary,
    paddingBottom: theme?.space[5],
  }),
);
const HeaderContainer = styled(ResponsiveView)(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  paddingHorizontal: Platform.OS === 'ios' ? theme.space[3] : 0,
}));
const SearchBarContainer = styled(ResponsiveView)(({ theme }) => ({
  // flexDirection: 'row',
  // alignItems: 'center',
  paddingTop: theme.space[3],
  paddingBottom: theme.space[2],
  paddingHorizontal: theme?.space[4],
  // justifyContent: 'center',
}));

const SearchResultContainer = styled.View(({ theme }) => ({
  flex: 1,
  // paddingTop: insets.top,
  backgroundColor: theme?.colors.surface,
}));
const SearchHint = styled(SmallBody)(({ theme }) => ({
  color: theme.colors.background,
  paddingHorizontal: theme?.space[4],
}));
const SearchHintContainer = styled(ResponsiveView)(({ theme }) => ({
  color: theme.colors.primary,
  paddingHorizontal: theme?.space[4],
  // paddingBottom: theme.space[5],
}));
const ToggleGroupContainer = styled(ResponsiveView)(({ theme }) => {
  return {
    flexDirection: 'row',
    paddingHorizontal: theme?.space[4],
    // paddingBottom: theme?.space[5],
    gap: theme.space[1],
    paddingTop: theme.space[2],
    // justifyContent: 'space-evenly',
  };
});
const ToggleChildren = styled.View<{ selected: boolean }>(
  ({ theme, selected }) => {
    return {
      // alignItems: 'flex-end',
      // justifyContent:'flex-end',
      paddingHorizontal: theme.space[3],
      paddingVertical: theme.space[2],
      margin: 0,
      borderRadius: theme.sizes[8],
      borderWidth: selected ? 2 : 1,
      borderColor: theme.colors.background,
      backgroundColor: selected
        ? theme.colors.palette.fwdAlternativeOrange[100]
        : theme.colors.primary,
    };
  },
);
const ToggleBody = styled(Body)(({ theme }) => {
  return {
    // textAlign: 'left',
    // flexWrap: 'wrap',
    // marginTop: theme.space[3],
    // height: theme.sizes[12],
    // width: '100%',
  };
});

const RecentSearchContainer = styled.View(({ theme }) => ({
  // paddingTop: insets.top,
  backgroundColor: theme?.colors.surface,
  // borderWidth: 5,
  flex: 1,
}));

const RecentSearchCategoryTitle = styled(H8)(({ theme }) => {
  return {
    paddingHorizontal: theme.sizes[4],
    paddingVertical: theme.sizes[2],
    color: theme.colors.palette.fwdGreyDarkest,
  };
});

const SearchResultTextContainer = styled.View(({ theme }) => ({
  flexDirection: 'row',
  paddingTop: theme.space[4],
  paddingLeft: theme.space[4],
  gap: theme.space[2],
}));

const SortDataByArrow = ({
  sortBy,
  onPressDate,
}: {
  sortBy: 'newest' | 'oldest';
  onPressDate: () => void;
}) => {
  const { colors, sizes } = useTheme();
  return (
    <TouchableOpacity onPress={onPressDate}>
      {sortBy === 'newest' ? (
        <Row alignItems="center">
          <Typography.Body
            color={colors.palette.fwdAlternativeOrange[100]}
            fontWeight="bold">
            Newest
          </Typography.Body>
          <Icon.ArrowDown
            fill={colors.palette.fwdAlternativeOrange[100]}
            size={sizes[4]}
          />
        </Row>
      ) : (
        <Row alignItems="center">
          <Typography.Body
            color={colors.palette.fwdAlternativeOrange[100]}
            fontWeight="bold">
            Oldest
          </Typography.Body>
          <Icon.ArrowUp
            fill={colors.palette.fwdAlternativeOrange[100]}
            size={sizes[4]}
          />
        </Row>
      )}
    </TouchableOpacity>
  );
};
