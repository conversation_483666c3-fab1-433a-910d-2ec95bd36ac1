import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  Lead,
  LeadListToolBarProps,
  LeadsFilters,
  MyLeadParamList,
  MyLeadScreenProps,
  RootStackParamList,
} from 'types';
import { LeadItem } from 'features/lead/components/LeadItem';
import {
  RefreshControl,
  TouchableOpacity,
  useWindowDimensions,
  View,
} from 'react-native';
import { createMaterialTopTabNavigator } from '@react-navigation/material-top-tabs';
import Animated, {
  FadeIn,
  LinearTransition,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';
import {
  Chip,
  LoadingIndicator,
  Row,
  Typography,
  XView,
} from 'cube-ui-components';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { FlashList } from '@shopify/flash-list';
import { useBottomBar } from 'hooks/useBottomBar';
import useBoundStore from 'hooks/useBoundStore';
import { Icon } from 'cube-ui-components';
import {
  useGetExpiredLeads,
  useGetLeadsOther,
  useGetLeadsToday,
} from 'hooks/useGetLeads';
import {
  EventArg,
  NavigationProp,
  ParamListBase,
  TabNavigationState,
  useNavigation,
} from '@react-navigation/native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import LeadToolTips from 'features/lead/components/LeadToolTips';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import useLeadScrollContext from 'features/lead/hooks/useLeadScrollContext';

import ResponsiveText from 'components/ResponsiveTypography';
import { FilterDot } from 'components/FilterDot';
import LeadListEmptyComponent from 'features/lead/components/LeadListEmptyComponent';
import {
  handleScrollEndByOffset,
  memorizeStartOffset,
} from 'features/lead/utils';
import ResponsiveView from 'components/ResponsiveView';
import LeadChipsFilterPanel from 'features/lead/components/LeadFilterPanel/LeadChipsFilterPanel';
import { country } from 'utils/context';
import { countryModuleSellerConfig } from 'utils/config/module';
import LeadActivityController from 'features/lead/components/LeadActivityLogModal/phone/LeadActivityController';
import { MaterialTopTabNavigationProp } from '@react-navigation/material-top-tabs';

const MyLeadTab = createMaterialTopTabNavigator<MyLeadParamList>();

export default function MyLeads() {
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const tabNavigation =
    useNavigation<MaterialTopTabNavigationProp<MyLeadParamList>>();
  const { colors, sizes, space } = useTheme();
  const { isWideScreen } = useWindowAdaptationHelpers();
  const { isShowBottomBar } = useBottomBar();
  const { t } = useTranslation(['lead']);

  const [currentTab, setCurrentTab] = useState<'todayLeads' | 'otherLeads'>(
    'todayLeads',
  );

  const addLeadBtnOnPress = useCallback(() => {
    navigation.navigate('AddNewLeadOrEntity');
  }, [navigation]);

  const addButtonAnimatedStyle = useAnimatedStyle(() => {
    return {
      transform: [
        {
          translateY: -space[isWideScreen ? 5 : 4],
        },
      ],
      bottom: isShowBottomBar?.value ? 150 : 50,
    };
  }, [isShowBottomBar?.value]);

  const screenListeners = useMemo(() => {
    return {
      state: (
        state: EventArg<
          'state',
          boolean | undefined,
          { state: TabNavigationState<ParamListBase> }
        >,
      ) => {
        if (state.data?.state?.routes) {
          const currentRoute = state.data.state.routes[state.data.state.index];
          setCurrentTab(
            currentRoute.name === 'TodayLeads' ? 'todayLeads' : 'otherLeads',
          );
        }
      },
    };
  }, []);

  const showToolTip = useBoundStore(state => state.lead.today.showToolTip);

  const resetFilters = useBoundStore(store => store.leadActions.resetFilters);
  useEffect(() => {
    return () => {
      if (country === 'id') {
        resetFilters();
      }
    };
  }, []);

  return (
    <View style={{ flex: 1 }}>
      <Row
        gap={space[1]}
        px={space[4]}
        pt={space[3]}
        pb={
          currentTab === 'todayLeads'
            ? showToolTip
              ? space[1]
              : space[3]
            : space[3]
        }>
        <Chip
          size="large"
          style={{ flex: 1 }}
          label={t('lead:todayLeads')}
          focus={currentTab === 'todayLeads'}
          onPress={() => {
            tabNavigation.navigate('TodayLeads');
          }}
        />
        <Chip
          size="large"
          style={{ flex: 1 }}
          focus={currentTab === 'otherLeads'}
          label={t('lead:otherLeads')}
          onPress={() => {
            tabNavigation.navigate('OtherLeads');
          }}
        />
      </Row>
      <MyLeadTab.Navigator
        screenOptions={{
          swipeEnabled: false,
        }}
        screenListeners={screenListeners}
        tabBar={() => null}>
        <MyLeadTab.Screen
          name="TodayLeads"
          component={TodayLeads}
          options={{ tabBarLabel: t('lead:todayLeads') }}
        />
        <MyLeadTab.Screen
          name="OtherLeads"
          component={OtherLeads}
          options={{ tabBarLabel: t('lead:otherLeads') }}
        />
      </MyLeadTab.Navigator>

      <Animated.View
        entering={FadeIn.delay(500)}
        layout={LinearTransition}
        style={[addButtonAnimatedStyle]}>
        <AddLeadBtn
          style={{
            right: space[isWideScreen ? 6 : 4],
            width: space[isWideScreen ? 18 : 14],
            height: space[isWideScreen ? 18 : 14],
          }}
          onPress={addLeadBtnOnPress}>
          <Icon.Plus
            size={sizes[isWideScreen ? 8 : 6]}
            fill={colors.background}
          />
        </AddLeadBtn>
      </Animated.View>
    </View>
  );
}

const AddLeadBtn = styled.TouchableOpacity(({ theme }) => ({
  height: theme.sizes[14],
  width: theme.sizes[14],
  justifyContent: 'center',
  alignItems: 'center',
  borderRadius: theme.borderRadius.full,
  backgroundColor: theme.colors.primary,
  position: 'absolute',
  right: theme.sizes[4],
  ...theme.elevation[5],
}));

const LeadToolBarContainer = styled(ResponsiveView)(({ theme }) => ({
  paddingBottom: theme.space[2],
  paddingHorizontal: theme.space[4],
  backgroundColor: theme.colors.surface,
  zIndex: 99,
}));

const FilterButton = styled.TouchableOpacity(() => ({
  position: 'absolute',
  right: 0,
  top: 8,
  alignItems: 'flex-end',
}));

const TodayLeads = ({ navigation, route }: MyLeadScreenProps<'TodayLeads'>) => {
  const { colors, sizes, space } = useTheme();
  const animatedProps = useLeadScrollContext();
  const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();
  const { height } = useWindowDimensions();
  const { bottom } = useSafeAreaInsets();

  const { sortByNewest: sortByNewestToday, filters: todayFilters } =
    useBoundStore(store => store.lead.today);

  const { toggleSort } = useBoundStore(store => store.leadActions.today);

  const {
    data,
    fetchNextPage,
    isFetchingNextPage,
    hasNextPage,
    refetch,
    isFetching,
  } = useGetLeadsToday();

  const todayLeads = useMemo(() => {
    if (!data) return [];

    return data?.pages
      .map(page => page?.data)
      .flat()
      .filter(lead => lead?.status !== 'inactive'); // CUBEPH-3404 Hiding inactive leads after transferred
  }, [data]);

  useEffect(() => {
    refetch();
    ref.current?.scrollToOffset({ offset: 0 });
  }, [refetch, sortByNewestToday, todayFilters]);

  const { showBottomBar, hideBottomBar, safeBottomPadding } = useBottomBar();

  const ref = useRef<FlashList<Lead>>(null);

  /**
   * CUBEPH-339 24 hrs lead activity log
   */

  const {
    lead: { isExpiredLeadChecked },
  } = useBoundStore();

  const { data: expiredLeadsData } = useGetExpiredLeads();

  const [expiredLeads, setExpiredLeads] = useState([]);
  useEffect(() => {
    if (expiredLeadsData?.data?.length > 0 && expiredLeads?.length === 0) {
      setExpiredLeads(expiredLeadsData?.data);
    }
  }, [expiredLeadsData]);
  const { t } = useTranslation(['lead']);

  /**
   *  LeadActivityResponsePanel
   */

  const hasFilter = checkHasFilter(todayFilters);
  const showToolTip = useBoundStore(state => state.lead.today.showToolTip);

  const defaultSortingText = sortByNewestToday
    ? t('lead:expireSoon')
    : t('lead:expireSoon');
  const idSortingText = sortByNewestToday ? t('lead:newest') : t('lead:oldest');

  return (
    <>
      <View style={{ backgroundColor: colors.surface }}>
        {showToolTip && <LeadToolTips />}
      </View>
      <LeadListToolBar
        curTab={'TodayLeads'}
        toggleSort={toggleSort}
        hasFilter={hasFilter}
        sortingText={country === 'id' ? idSortingText : defaultSortingText}
        filterText={`Total (${todayLeads.length || 0})`}
        numberOfLeads={todayLeads.length}
        sortByNewest={sortByNewestToday}
      />
      {countryModuleSellerConfig?.lead?.leadActivityModal &&
        expiredLeads &&
        expiredLeads?.length > 0 &&
        !isExpiredLeadChecked && (
          <LeadActivityController expiredLeadData={expiredLeads} />
        )}

      <FlashList
        refreshControl={
          <RefreshControl
            refreshing={isFetching ?? false}
            onRefresh={refetch}
          />
        }
        ref={ref}
        onScrollBeginDrag={e => {
          const startOffset = e.nativeEvent.contentOffset.y;
          memorizeStartOffset(startOffset, animatedProps);
        }}
        onScrollEndDrag={e => {
          const endOffset = e.nativeEvent.contentOffset.y;
          handleScrollEndByOffset(
            endOffset,
            animatedProps,
            showBottomBar,
            hideBottomBar,
          );
        }}
        contentContainerStyle={{
          paddingHorizontal: sizes[4],
          backgroundColor: colors.surface,
          paddingBottom: Math.max(
            height - 120 * todayLeads.length,
            safeBottomPadding - bottom + space[51],
          ),
          ...(isWideScreen && {
            paddingHorizontal: sizes[6],
          }),
          ...(isNarrowScreen && {
            paddingHorizontal: sizes[3],
          }),
        }}
        renderItem={({ item }) => {
          return (
            <LeadItem
              {...item}
              isToday={true}
              onPress={() => {
                navigation.navigate('LeadProfile', {
                  id: String(item.id),
                  isIndividualLead: item.isIndividual,
                });
                showBottomBar();
              }}
              isLoading={isFetching && !isFetchingNextPage}
            />
          );
        }}
        keyExtractor={({ id, firstName }) => `lead_${id}_${firstName}`}
        data={todayLeads}
        estimatedItemSize={sizes[30]}
        bounces={true}
        ListEmptyComponent={
          !isFetching ? (
            <LeadListEmptyComponent
              title={t('lead:helpfulTips')}
              text={t('lead:noLeadToday')}
              buttonText={t('lead:viewAllLeads')}
              onPressButton={() => navigation.navigate('OtherLeads')}
              tab={route.name}
            />
          ) : (
            <LeadLoadingPlaceholder />
          )
        }
        onEndReached={() => {
          hasNextPage && fetchNextPage();
        }}
        ListFooterComponent={
          isFetchingNextPage ? (
            <LoadingContainer>
              <LoadingIndicator />
            </LoadingContainer>
          ) : (
            <></>
          )
        }
        extraData={[data]}
        scrollEnabled={todayLeads.length > 0}
      />
    </>
  );
};

const OtherLeads = ({ navigation, route }: MyLeadScreenProps<'OtherLeads'>) => {
  const { colors, sizes, space } = useTheme();
  const { height } = useWindowDimensions();
  const animatedProps = useLeadScrollContext();

  const {
    others: { sortByNewest, filters: othersFilters },
  } = useBoundStore(store => store.lead);
  const { toggleSort } = useBoundStore(store => store.leadActions.others);
  const { t } = useTranslation(['lead']);
  const {
    data,
    fetchNextPage,
    isFetchingNextPage,
    hasNextPage,
    refetch,
    isFetching,
  } = useGetLeadsOther();

  useEffect(() => {
    refetch();
    ref.current?.scrollToOffset({ offset: 0 });
  }, [sortByNewest, othersFilters]);

  const otherLeads = useMemo(() => {
    if (!data) return [];

    return data?.pages
      .map(page => page?.data)
      .flat()
      .filter(lead => lead?.status !== 'inactive'); // CUBEPH-3404 Hiding inactive leads after transferred
  }, [data]);

  const { showBottomBar, hideBottomBar, safeBottomPadding } = useBottomBar();

  const { bottom } = useSafeAreaInsets();

  const ref = useRef<FlashList<Lead>>(null);

  useEffect(() => {
    return () => {
      handleScrollEndByOffset(0, animatedProps, showBottomBar, hideBottomBar);
    };
  }, []);

  const hasFilter = checkHasFilter(othersFilters);

  return (
    <>
      <LeadListToolBar
        curTab="OtherLeads"
        toggleSort={toggleSort}
        hasFilter={hasFilter}
        sortingText={sortByNewest ? t('lead:newest') : t('lead:oldest')}
        filterText={`Total (${otherLeads.length || 0})`}
        numberOfLeads={otherLeads.length}
        sortByNewest={sortByNewest}
      />
      <FlashList
        refreshControl={
          <RefreshControl
            refreshing={isFetching ?? false}
            onRefresh={refetch}
          />
        }
        ref={ref}
        onScrollBeginDrag={e => {
          const startOffset = e.nativeEvent.contentOffset.y;
          memorizeStartOffset(startOffset, animatedProps);
        }}
        onScrollEndDrag={e => {
          const endOffset = e.nativeEvent.contentOffset.y;
          handleScrollEndByOffset(
            endOffset,
            animatedProps,
            showBottomBar,
            hideBottomBar,
          );
        }}
        contentContainerStyle={{
          paddingHorizontal: sizes[4],
          backgroundColor: colors.palette.surface,
          paddingBottom: Math.max(
            height - 120 * otherLeads.length,
            safeBottomPadding - bottom + space[51],
          ),
        }}
        renderItem={({ item }) => {
          return (
            <LeadItem
              {...item}
              onPress={() => {
                navigation.navigate('LeadProfile', {
                  id: String(item.id),
                  isIndividualLead: item.isIndividual,
                });
                showBottomBar();
              }}
              isLoading={isFetching && !isFetchingNextPage}
            />
          );
        }}
        estimatedItemSize={sizes[30]}
        keyExtractor={({ id, firstName }) => `lead_${id}_${firstName}`}
        data={otherLeads}
        bounces={true}
        ListEmptyComponent={
          !isFetching ? (
            <LeadListEmptyComponent
              title={t('lead:helpfulTips')}
              text={t('lead:noLeadOthers')}
              onPressButton={() => navigation.navigate('AddNewLeadOrEntity')}
              buttonText={t('lead:addNewLead')}
              tab={route.name}
            />
          ) : (
            <LeadLoadingPlaceholder />
          )
        }
        onEndReached={() => {
          hasNextPage && fetchNextPage();
        }}
        ListFooterComponent={
          isFetchingNextPage ? (
            <LoadingContainer>
              <LoadingIndicator />
            </LoadingContainer>
          ) : (
            <>
              {/* {Number(data?.pages[data?.pages?.length - 1].totalCount) > 0 && (
                <Typography.Body
                  color={colors.secondaryVariant}
                  style={{ width: '100%', textAlign: 'center' }}>
                  {'No more leads...'}
                </Typography.Body>
              )} */}
            </>
          )
        }
        scrollEnabled={otherLeads.length > 0}
        extraData={[data, isFetching]}
      />
    </>
  );
};

const LoadingContainer = styled.View(() => ({
  justifyContent: 'center',
  alignItems: 'center',
}));

const checkHasFilter = (filters: LeadsFilters) => {
  const filterValArr = Object.values(filters)
    .map(filterCat => Object.values(filterCat))
    .flat();

  return !filterValArr.every(val => !val);
};

const LeadLoadingPlaceholder = () => {
  const arr = Array(10).fill(null);

  return (
    <>
      {arr.map((_, i) => (
        <LeadItem
          id={''}
          caseIds={[]}
          createdAt={''}
          agentId={null}
          firstName={''}
          nameSuffix={''}
          localNameLang={''}
          recommendedProducts={[]}
          isLoading={true}
          key={'LeadItem_LeadLoadingPlaceholder_' + i}
        />
      ))}
    </>
  );
};

const LeadListToolBar = ({
  toggleSort,
  hasFilter,
  sortingText,
  filterText,
  numberOfLeads,
  curTab,
  sortByNewest,
}: LeadListToolBarProps) => {
  const {
    colors,
    sizes,
    space,
    animation: { duration },
  } = useTheme();
  const { t } = useTranslation(['lead']);

  const animatedProps = useLeadScrollContext();

  const [showFilterPanel, setShowFilterPanel] = useState(false);
  const { isWideScreen } = useWindowAdaptationHelpers();

  const onPressFilter = () => {
    setShowFilterPanel(true);
  };

  const animTabBarStyle = useAnimatedStyle(() => {
    return {
      height: withTiming(animatedProps.show.value ? sizes[16] : 0, {
        duration,
      }),
      opacity: withTiming(animatedProps.show.value ? 1 : 0, { duration }),
    };
  }, [animatedProps.show.value, sizes]);

  if (!hasFilter && !numberOfLeads) {
    return null;
  }

  return (
    <Animated.View style={[animatedProps && animTabBarStyle]}>
      <LeadToolBarContainer narrowStyle={{ paddingHorizontal: sizes[3] }}>
        <XView>
          <XView
            style={{
              flex: 1,
              alignItems: 'flex-end',
              justifyContent: 'space-between',
            }}>
            <TouchableOpacity
              style={{ alignSelf: 'flex-start' }}
              onPress={toggleSort}>
              <XView style={{ marginTop: space[2], alignItems: 'center' }}>
                <ResponsiveText TypographyDefault={Typography.Label}>
                  {t('lead:sortBy')}
                </ResponsiveText>
                <ResponsiveText
                  TypographyDefault={Typography.Label}
                  color={colors.palette.fwdAlternativeOrange[100]}
                  fontWeight="bold">
                  {sortingText}
                </ResponsiveText>
                {sortByNewest ? (
                  <Icon.ArrowDown
                    size={sizes[4]}
                    fill={colors.palette.fwdAlternativeOrange[100]}
                  />
                ) : (
                  <Icon.ArrowUp
                    size={sizes[4]}
                    fill={colors.palette.fwdAlternativeOrange[100]}
                  />
                )}
              </XView>
            </TouchableOpacity>
            <FilterButton onPress={onPressFilter}>
              {hasFilter ? (
                <Icon.Filtered2 size={sizes[isWideScreen ? 7 : 6]} />
              ) : (
                <Icon.Filter
                  size={sizes[isWideScreen ? 7 : 6]}
                  fill={colors.secondary}
                />
              )}
            </FilterButton>
          </XView>
        </XView>
        <ResponsiveText
          TypographyDefault={Typography.Body}
          style={{
            marginTop: space[2],
            color: colors.palette.fwdGreyDarkest,
          }}>
          {filterText}
        </ResponsiveText>

        <LeadChipsFilterPanel
          visible={showFilterPanel}
          handleClose={() => setShowFilterPanel(false)}
          curTab={curTab as keyof MyLeadParamList}
        />
      </LeadToolBarContainer>
    </Animated.View>
  );
};
