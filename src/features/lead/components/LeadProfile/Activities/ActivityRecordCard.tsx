import React, { Fragment } from 'react';
import { View } from 'react-native';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Card, H6, H7, H8, Icon, XView } from 'cube-ui-components';
import { useStylesFromTheme } from '../utils/leadProfileCommonStyle';
import FunnelChart from 'components/Chart/FunnelChart';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import useBoundStore from 'hooks/useBoundStore';
import { useGetLeadByLeadId } from 'hooks/useGetLeads';
import { useTranslation } from 'react-i18next';
import { TransactionsAction, TransactionsInLead } from 'types';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { ActionsForActivities } from './types';
import { country } from 'utils/context';

// Funnel chart current status
const statusLabelLevel = {
  created: 1,
  consolidated: 1,
  contacted: 2,
  appointment: 3,
  illustration: 4,
  submitted: 5,
};
Object.freeze(statusLabelLevel);

export default function ActivityRecordCard({
  leadId: leadIdProp,
}: {
  leadId?: string;
}) {
  const { t } = useTranslation('leadProfile');
  const { space } = useTheme();
  const { cardStyle } = useStylesFromTheme();
  const { isNarrowScreen, shouldAdaptWideLayout } =
    useWindowAdaptationHelpers();

  // storeLeadId maybe empty string in some cases
  const storeLeadId = useBoundStore.getState().lead.inquiringLeadId;
  const leadId = leadIdProp || storeLeadId;
  const { isLoading, isError, data: lead } = useGetLeadByLeadId(leadId);
  const isIB = country === 'ib';

  const activityRecord = lead?.transactions || [];
  const latestFiveRecords = [...activityRecord]
    .filter(
      ({ action }) =>
        action === 'contact' ||
        action === 'appointment' ||
        action === 'illustrate' ||
        action === 'defer' ||
        action === 'not_interested' ||
        action === 'submit',
    )
    .slice(-5)
    .reverse();

  // * map 'not_interested' action  to 'contact' if country is IB (display lead status)
  const filterFiveRecordsByCountry = isIB
    ? latestFiveRecords.map(record => {
        if (record.action === 'not_interested') {
          return {
            ...record,
            action: 'contact',
          };
        }
        return record;
      })
    : latestFiveRecords;

  // * Funnel chart current status
  // ** ib special handling: 'not_interested' -> 'created'; Reason: 'not_interested' is not a funnel chart status
  const leadCurrentStatus = isIB
    ? lead?.status === 'not_interested'
      ? 'created'
      : lead?.status
    : lead?.status || '';

  // * Funnel chart data
  const funnelChartValuesObj = countActions(activityRecord);
  const VALUES_ARR = [
    lead?.status === 'created' || lead?.status === 'consolidated' ? 1 : 0, // Value of not contacted
    funnelChartValuesObj?.['contact'] ?? 0,
    funnelChartValuesObj?.['appointment'] ?? 0,
    funnelChartValuesObj?.['illustrate'] ?? 0,
    funnelChartValuesObj?.['submit'] ?? 0,
  ];
  const mappedStatusLabelLevel = statusLabelLevel?.[leadCurrentStatus];

  // * Funnel chart titles
  const TITLES_ARR = [
    t('leadProfile.activityRecord.chart.not_contacted'),
    t('leadProfile.activityRecord.chart.contacted'),
    t('leadProfile.activityRecord.chart.appointment'),
    t('leadProfile.activityRecord.chart.illustration'),
    t('leadProfile.activityRecord.chart.submitted'),
  ];

  return (
    <Card style={[cardStyle, { gap: space[4] }]}>
      <H6 fontWeight="bold">{t('leadProfile.activityRecord.title')}</H6>

      <View
        style={{
          width: shouldAdaptWideLayout ? '65%' : '100%',
          alignSelf: 'center',
        }}>
        <FunnelChart
          scaleX={isNarrowScreen ? 0.85 : 1}
          scaleY={1}
          titlesArray={TITLES_ARR}
          valuesArray={VALUES_ARR}
          showStatusLabel={true}
          statusLabelLevel={mappedStatusLabelLevel}
        />
      </View>

      <View>
        {filterFiveRecordsByCountry.map(
          ({ action, actionAt, extra, reason }, index) => {
            const date = dateFormatUtil(actionAt);
            // const date = formatDate(parseISO(actionAt));
            return (
              <Fragment key={'details_' + index}>
                <StatusDetails
                  action={action as ActionsForActivities}
                  date={date}
                  category={
                    action === 'contact' || action === 'appointment'
                      ? extra?.feedback
                      : action
                  }
                  content={extra?.feedbackDetails}
                  reason={reason}
                />
                {filterFiveRecordsByCountry.length - 1 > index && (
                  <RowSeparator />
                )}
              </Fragment>
            );
          },
        )}
      </View>
    </Card>
  );
}

interface StatusDetailsProps {
  action: Extract<
    TransactionsAction,
    | 'contact'
    | 'appointment'
    | 'illustrate'
    | 'submit'
    | 'not_interested'
    | 'defer'
  >;
  date: string;
  category?: string | null;
  content?: string | null;
  reason?: string | null;
}

function StatusDetails({
  action,
  date,
  category,
  content,
  reason,
}: StatusDetailsProps) {
  const { t } = useTranslation('leadProfile');
  const { space } = useTheme();

  const ICON_SIZE = 24;

  return (
    <View style={{ gap: space[1] }}>
      <XView style={{ alignItems: 'center', justifyContent: 'space-between' }}>
        <XView style={{ alignItems: 'center', gap: space[3] }}>
          {keyMappedIcon(category)}
          <H7 fontWeight="bold">
            {t(`leadProfile.activityRecord.action.${action}`)}
          </H7>
        </XView>
        <Date>{date}</Date>
      </XView>

      <XView style={{ gap: space[3] }}>
        <View style={{ width: ICON_SIZE }} />
        <View style={{ flex: 1, gap: space[1] }}>
          {(action === 'contact' || action === 'appointment') && (
            <H7>
              {t(
                `leadProfile.activityRecord.feedback.${
                  (category as 'deferred') || 'interested' || 'notInterested'
                }`,
              )}
            </H7>
          )}
          {reason && <Content>{reason}</Content>}
          {content && <Content>{content}</Content>}
        </View>
      </XView>
    </View>
  );
}

const keyMappedIcon = (key?: string | null) => {
  switch (key) {
    case 'interested':
      return <Icon.VeryInterested />;
    case 'notInterested':
    case 'not_interested':
      return <Icon.NotInterested />;
    case 'illustrate':
      return <Icon.Document />;
    case 'submit':
      return <Icon.TickCircle />;
    case 'deferred':
    case 'defer':
      return <Icon.Thinking />;
    default:
      return <Icon.CallText />;
  }
};

const countActions = (arr: TransactionsInLead[]) => {
  if (arr.length === 0) return;

  const count: Record<string, number> = {};

  for (const obj of [...arr]) {
    if (obj && typeof obj === 'object') {
      const action = obj.action;
      if (action && typeof action === 'string') {
        count[action] = (count[action] || 0) + 1;
      }
    }
  }
  return count;
};

const Date = styled(H8)(({ theme }) => ({
  color: theme.colors.placeholder,
}));

const RowSeparator = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.palette.fwdGrey[100],
  width: '100%',
  height: 1,
  marginVertical: theme.space[2],
}));

const Content = styled(H8)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
}));
