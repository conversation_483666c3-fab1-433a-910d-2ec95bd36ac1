import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Body, Checkbox, HighlightShadowV2 } from 'cube-ui-components';
import useToggle from 'hooks/useToggle';
import React, { useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Linking } from 'react-native';
import { country } from 'utils/context';

const PolicyConfirm = React.memo(
  ({
    checked,
    onChange,
  }: {
    checked: boolean;
    onChange: (checked: boolean) => void;
  }) => {
    const { t } = useTranslation(['lead']);
    const { colors, space, borderRadius } = useTheme();
    const [expanded, expand, collapse] = useToggle(false);

    const onPressPrivacy = useCallback(() => {
      switch (country) {
        case 'id':
          Linking.openURL('https://www.fwd.co.id/id/kebijakan-data-pribadi/');
          break;
        case 'ph':
        case 'my':
        case 'ib':
        //TODO: add url for my, ph, ib
      }
    }, []);

    return (
      <HighlightShadowV2
        borderRadius={borderRadius.medium}
        disabled={checked}
        style={{
          marginBottom: space[5],
        }}>
        <Container checked={checked}>
          <Checkbox checked={checked} onChange={onChange} />
          {!expanded && (
            <Body style={{ flex: 1 }}>
              {t('lead:addLead.consent.statementPart0')}{' '}
              <Body
                fontWeight={'bold'}
                onPress={expand}
                color={colors.palette.fwdAlternativeOrange[100]}>
                {t('lead:addLead.consent.more')}
              </Body>
            </Body>
          )}
          {expanded && (
            <Body style={{ flex: 1 }}>
              {t('lead:addLead.consent.statementPart1')}{' '}
              <Body
                fontWeight={'bold'}
                onPress={onPressPrivacy}
                color={colors.palette.fwdAlternativeOrange[100]}>
                {t('lead:addLead.consent.statementFWDPrivacyPolicy')}
              </Body>{' '}
              {t('lead:addLead.consent.statementPart2')}{' '}
              <Body
                fontWeight={'bold'}
                onPress={collapse}
                color={colors.palette.fwdAlternativeOrange[100]}>
                {t('lead:addLead.consent.close')}
              </Body>
            </Body>
          )}
        </Container>
      </HighlightShadowV2>
    );
  },
);

export default PolicyConfirm;

const Container = styled.View<{ checked: boolean }>(
  ({ theme: { space, colors, borderRadius }, checked }) => ({
    flexDirection: 'row',
    gap: space[2],
    alignItems: 'flex-start',
    backgroundColor: checked ? colors.primaryVariant3 : colors.background,
    padding: space[3],
    // marginBottom: space[5],
    borderRadius: borderRadius.medium,
    borderWidth: 1,
    borderColor: colors.primary,
  }),
);
