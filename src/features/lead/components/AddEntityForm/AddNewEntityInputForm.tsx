import styled from '@emotion/native';
import { Theme, useTheme } from '@emotion/react';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import ButtonSeparator from 'components/ButtonSeparator';
import Input from 'components/Input';
import Phone<PERSON>ield from 'components/PhoneField';
import ResponsiveText from 'components/ResponsiveTypography';
import SearchableDropdown from 'components/SearchableDropdown';
import { MY_MOBILE_CODE } from 'constants/optionList';
import {
  addToast,
  Button,
  Column,
  Dropdown,
  Icon,
  LoadingIndicator,
  Row,
  TextField,
  Typography,
} from 'cube-ui-components';
import * as Contacts from 'expo-contacts';
import EntityIcon from 'features/lead/components/AddLeadForm/EntityIcon';
import LeadIcon from 'features/lead/components/AddLeadForm/LeadIcon';
import NameTextField from 'features/lead/components/AddLeadForm/NameTextField';
import { useCreateLead } from 'features/lead/hooks/useCreateLead';
import useGetNatureOfBusinessOption from 'features/lead/hooks/useGetNatureOfBusinessOption';
import { useLookupLead } from 'features/lead/hooks/useLookupLead';
import useValidateEmail from 'features/lead/hooks/useValidateEmail';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { useGetOptionList } from 'hooks/useGetOptionList';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { useEffect, useMemo, useState } from 'react';
import {
  SubmitErrorHandler,
  SubmitHandler,
  useForm,
  UseFormHandleSubmit,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Platform, TouchableOpacity, View } from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Animated, { LinearTransition } from 'react-native-reanimated';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  AddEntityFormData,
  CreateEntityRequest,
  EntityLeadFormValues,
  Lead,
  LookupEntityRequest,
  MainTabParamList,
  RootStackParamList,
} from 'types';
import {
  BusinessNatureEntity,
  CountryCode,
  Extension,
  Title,
} from 'types/optionList';
import { country } from 'utils/context';
import { useGenerateBltsRefInfoStored } from 'utils/helper/bltsUtils';
import { useValidationYupResolver } from 'utils/validation';
import {
  addNewEntityLeadSchema,
  initialLeadData,
} from 'utils/validation/my/addNewEntityLeadSchema';
import {
  addEntityValidationSchema,
  initialEntityFormData,
  INVALID_EMAIL_INPUT,
  KeyOfRequiredAddEntityFieldsObj,
  requiredAddEntityFieldsObj,
} from 'utils/validation/ph/addEntityFormSchema';
import AddLeadOrEntityTransferModal from '../AddLeadOrEntityTransferModal';
import PolicyConfirm from '../PolicyConfirm';

export default function AddNewEntityForm() {
  const isiOS = Platform.OS === 'ios';
  const { t } = useTranslation('lead');
  const channel = useGetCubeChannel();
  const { colors, sizes } = useTheme();
  const { bottom } = useSafeAreaInsets();
  const navigation =
    useNavigation<NavigationProp<MainTabParamList & RootStackParamList>>();
  const { params, name } =
    useRoute<RouteProp<RootStackParamList, 'AddNewLead' | 'AddNewEntity'>>();

  const [modalVisible, setModalVisible] = useState(false);

  // UI constants
  const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();
  const ROW_GAP = isWideScreen ? sizes[7] : sizes[5];
  const COUNTRY_CODE_MR = isWideScreen ? sizes[5] : sizes[4];

  // Option lists
  const { data: optionList } = useGetOptionList();
  const natureOfBusinessData = useGetNatureOfBusinessOption();
  const {
    titleOptions,
    extensionOptions,
    countryCodeOptions,
    natureOfBusinessOptions,
  } = useMemo(() => {
    return {
      titleOptions: optionList?.TITLE?.options || [],
      extensionOptions: optionList?.EXTENSION?.options || [],
      countryCodeOptions: optionList?.COUNTRY_CODE?.options || [],
      natureOfBusinessOptions:
        country == 'ph'
          ? optionList?.BUSINESS_NATURE_ENTITY?.options || []
          : natureOfBusinessData || [],
    };
  }, [optionList]);

  const { mutate, isLoading } = useCreateLead();
  const { mutate: mutateToValidateEmail, isLoading: isLoadingEmailValidation } =
    useValidateEmail();
  const { mutateAsync: mutateLookupLead, isLoading: isLoadingLookupLead } =
    useLookupLead();

  const resolver =
    country === 'ph'
      ? useValidationYupResolver(addEntityValidationSchema)
      : useValidationYupResolver(addNewEntityLeadSchema);
  type FormData = AddEntityFormData | EntityLeadFormValues;

  type FormDataType<T extends string> = T extends 'ph'
    ? AddEntityFormData
    : EntityLeadFormValues;

  type SelectedForDataType = FormDataType<typeof country>;

  const {
    watch,
    setValue,
    handleSubmit,
    control,
    formState: { errors },
    setError,
    clearErrors,
  } = useForm<SelectedForDataType>({
    defaultValues:
      country == 'ph'
        ? initialEntityFormData
        : {
            ...initialLeadData,
            mobilePhoneCountryCode: (country === 'ib' && MY_MOBILE_CODE) || '',
          },
    resolver,
    mode: 'onBlur',
  });

  type HandleSubmitSchema = typeof handleSubmit extends UseFormHandleSubmit<
    infer U
  >
    ? U
    : never;

  const watchedEmail = watch('email');
  const requiredFields = Object.keys(
    requiredAddEntityFieldsObj,
  ) as Array<KeyOfRequiredAddEntityFieldsObj>;
  const isSubmitDisabled = watch(requiredFields).some(field => field === '');

  const validateEmail = (email: string, actionAfterSuccess?: () => void) => {
    if (email.trim() === '') {
      actionAfterSuccess && actionAfterSuccess();
      return;
    }
    mutateToValidateEmail(email, {
      onSuccess: data => {
        if (data.valid === false) {
          setError('email', {
            message: INVALID_EMAIL_INPUT,
          });
          return;
        }
        actionAfterSuccess && actionAfterSuccess();
      },
    });
  };

  const onValidSubmit: SubmitHandler<HandleSubmitSchema> = async data => {
    // Data for create entity
    if (country === 'ph') {
      const newData: CreateEntityRequest = {
        isIndividual: false,
        companyName: data.companyName,
        occupationIndustryCode: data.occupationIndustryCode,
        email: data.email,
        mobilePhoneCountryCode: getCountryCodeValueOnSubmit(
          data.mobilePhoneCountryCode,
        ),
        mobilePhoneNumber: data.mobilePhoneNumber,
        homePhoneCountryCode: getCountryCodeValueOnSubmit(
          data.homePhoneCountryCode,
        ),
        homePhoneNumber: data.homePhoneNumber,
        salutation: data.salutation,
        firstName: data.firstName,
        lastName: data.lastName,
        jobTitle: data.jobTitle,
      };
      if (data.middleName) {
        newData.middleName = data.middleName;
      }
      if (data.nameExtension) {
        newData.nameExtension = data.nameExtension;
      }
      if (channel === 'BANCA' && data.extra) {
        newData.extra = {
          alts_blts_ref_num: bancaReferralDetails?.alts_blts_ref_num ?? '',
          service_branch: bancaReferralDetails?.service_branch ?? '',
          referrer_code: bancaReferralDetails?.referrer_code ?? '',
          bank_customer_id: '',
        };
      }

      // Data for lookup
      const lookupEntityReq: LookupEntityRequest = {
        ...newData,
        sourceId: 'CUBE',
        acceptPhoneCall: true,
        acceptEmail: true,
        isSmoker: false,
      };

      try {
        await mutateLookupLead(lookupEntityReq, {
          onSuccess: data => {
            const res: Lead = data as Lead;
            goBackPreviousStack();
            navigation.navigate('LeadProfile', {
              id: String(res.id),
            });
            return;
          },
          onError: error => {
            const errorObj: any = error as any;
            if (errorObj.response.status == '404') {
              const submitLeadForm = () =>
                mutate(newData, {
                  onSuccess: () => {
                    goBackPreviousStack();
                    addToast([
                      {
                        IconLeft: Icon.Tick,
                        message: 'A new entity is added.',
                      },
                    ]);
                  },
                });

              validateEmail(watchedEmail, submitLeadForm);
            }
          },
        });
      } catch (error) {
        console.log(error);
      }
    }
    if (country === 'ib') {
      const newData: CreateEntityRequest = {
        // ...data,
        //
        companyName: data.companyName,
        occupationIndustryCode: data.occupationIndustryCode,
        firstName: data.firstName,
        mobilePhoneCountryCode: data.mobilePhoneCountryCode,
        mobilePhoneNumber: data.mobilePhoneNumber,
        email: data.email,
        isIndividual: false,
        lastName: ' ',
      };
      mutate(newData, {
        onSuccess: () => {
          addToast([
            {
              IconLeft: Icon.Tick,
              message: 'A new lead is added.',
            },
          ]);
          goBackPreviousStack();
        },
        onError: e => {
          addToast([
            {
              message: 'Please try again later.',
            },
          ]);
          console.log('onError: ', e);
        },
      });
    }
  };

  const onInvalidSubmit: SubmitErrorHandler<HandleSubmitSchema> = error => {
    console.log(
      '🚀 ~ file: AddNewEntityInputForm.tsx: ~ AddNewEntity ~ error:',
      error,
    );
  };

  useEffect(() => {
    if (errors?.email) clearErrors('email');
  }, [watchedEmail]);

  // Contact book handler
  const uploadFromContactBookOnPress = async () => {
    const { status } = await Contacts.requestPermissionsAsync();
    if (status === Contacts.PermissionStatus.GRANTED) {
      navigation.navigate('ContactBook', { routeName: 'AddNewEntity' });
    }
  };

  const checkValuesFromParams = () => {
    if (params?.contactInfo?.firstName) {
      setValue('firstName', params.contactInfo.firstName);
    } else {
      setValue('firstName', '');
    }
    if (params?.contactInfo?.lastName) {
      setValue('lastName', params.contactInfo.lastName);
    } else {
      setValue('lastName', '');
    }
    if (params?.contactInfo?.phoneNumbers) {
      const firstSetTel: Contacts.PhoneNumber | undefined =
        params.contactInfo.phoneNumbers[0];
      const preFormatedNumber = firstSetTel?.number ?? '';
      const phoneNumberArray = preFormatedNumber.split(' ');
      const countryCodeWithPlus = phoneNumberArray[0];
      if (
        preFormatedNumber &&
        preFormatedNumber[0] == '+' &&
        phoneNumberArray.length > 1
      ) {
        // **Jessica: CountryCode can be extracted, extracted as +xx yyyyyyy
        setValue('mobilePhoneCountryCode', phoneNumberArray[0]);
        const mobilePhoneCountryCodeValue = countryCodeOptions.filter(
          item =>
            getCountryCodeValue(item) ===
            countryCodeWithPlus.replace(/\D/g, ''),
        );
        if (country === 'ib') {
          setValue('mobilePhoneCountryCode', MY_MOBILE_CODE);
        } else {
          setValue(
            'mobilePhoneCountryCode',
            mobilePhoneCountryCodeValue[0]?.value || '',
          );
        }
        setValue(
          'mobilePhoneNumber',
          preFormatedNumber.replace(countryCodeWithPlus, '').replace(/\D/g, ''),
        );
      } else {
        // **Jessica: CountryCode cannot be extracted
        if (country === 'ib') {
          setValue('mobilePhoneCountryCode', MY_MOBILE_CODE);
        } else {
          setValue('mobilePhoneCountryCode', '');
        }
        setValue('mobilePhoneNumber', preFormatedNumber.replace(/\D/g, ''));
      }
    } else {
      if (country === 'ib') {
        setValue('mobilePhoneCountryCode', MY_MOBILE_CODE);
      } else {
        setValue('mobilePhoneCountryCode', '');
      }
      setValue('mobilePhoneNumber', '');
    }
    if (params?.contactInfo?.emails) {
      const firstSetEmail: Contacts.Email | undefined =
        params.contactInfo.emails[0];
      setValue('email', firstSetEmail.email ?? '');
    } else {
      setValue('email', '');
    }
  };

  useEffect(() => {
    checkValuesFromParams();
  }, [params]);

  // Pre-fill country code
  useEffect(() => {
    if (countryCodeOptions?.length > 0 && country === 'ph') {
      const countryName = t(country, { ns: 'locale' } as never);
      const countryCodePresetItem = countryCodeOptions.find(item =>
        item.label.includes(countryName as never),
      );
      if (countryCodePresetItem) {
        setValue('mobilePhoneCountryCode', countryCodePresetItem?.value);
        setValue('homePhoneCountryCode', countryCodePresetItem?.value);
      }
    }
    if (country === 'ib') {
      setValue('mobilePhoneCountryCode', MY_MOBILE_CODE);
    }
  }, [countryCodeOptions]);

  // Get referral details
  const bancaReferralDetails = useGenerateBltsRefInfoStored(channel);

  const goBackPreviousStack = () => {
    navigation.canGoBack() && navigation.goBack(); // back to add-new-lead screen
    navigation.canGoBack() && navigation.goBack(); // back to the screen before add-new-lead screen
  };

  const signatoryName = watch('firstName');
  const fullName = watch('companyName');
  const mobilePhoneNumber = watch('mobilePhoneNumber');
  const isMandoryFieldsFilled =
    fullName !== '' && mobilePhoneNumber !== '' && signatoryName !== '';

  const disableButton =
    country === 'ph'
      ? isSubmitDisabled ||
        isLoading ||
        isLoadingEmailValidation ||
        isLoadingLookupLead
      : !isMandoryFieldsFilled || isLoading;

  const [policyConfirmed, setPolicyConfirmed] = useState(false);

  return (
    <>
      <Animated.View layout={LinearTransition} style={{ flex: 1 }}>
        <KeyboardAwareScrollView
          contentContainerStyle={[
            {
              paddingTop: sizes[4],
              paddingBottom: sizes[12],
              paddingHorizontal: sizes[4],
            },
            isWideScreen && {
              paddingTop: sizes[5],
              paddingBottom: sizes[15],
              paddingHorizontal: sizes[4],
            },
            isNarrowScreen && {
              paddingTop: sizes[4],
              paddingBottom: sizes[8],
              paddingHorizontal: sizes[3],
            },
          ]}>
          <Button
            onPress={uploadFromContactBookOnPress}
            text={t('addLead.uploadFromPhoneBook.buttonLabel')}
            variant={'secondary'}
            style={{ maxWidth: 300, alignSelf: 'center' }}
            contentStyle={isWideScreen ? { height: 50 } : undefined}
          />

          <ButtonSeparator text={t('orSelect')} />
          <PolicyConfirm
            checked={policyConfirmed}
            onChange={setPolicyConfirmed}
          />

          <LeadIconContainer>
            <TouchableOpacity
              style={{ alignItems: 'center' }}
              onPress={() => setModalVisible(true)}>
              <LeadIcon isActive={false} />
              <Typography.LargeLabel
                children={t('addLead.category.individual')}
                fontWeight="bold"
                style={{
                  color: colors.palette.fwdGreyDark,
                  marginTop: sizes[3],
                }}
              />
            </TouchableOpacity>
            <View style={{ alignItems: 'center' }}>
              <EntityIcon isActive />
              <Typography.LargeLabel
                children={t('addLead.category.entity')}
                fontWeight="bold"
                style={{ marginTop: sizes[3] }}
              />
            </View>
          </LeadIconContainer>

          <Row>
            <Column style={{ flex: 1, gap: ROW_GAP }}>
              {country === 'ib' && (
                <Input
                  control={control}
                  as={NameTextField}
                  name="firstName"
                  style={{ flex: 1 }}
                  label={'Authorised signatory name'}
                  error={errors.firstName?.message}
                />
              )}
              <Input
                control={control}
                as={TextField}
                name="companyName"
                label={
                  country === 'ib'
                    ? 'Company name'
                    : t('addLead.formFields.entityName')
                }
                error={errors.companyName?.message}
              />
              {country === 'ib' && (
                <Row gap={12} flex={1}>
                  <Input
                    control={control}
                    as={Dropdown<CountryCode, string>}
                    name="mobilePhoneCountryCode"
                    label={'Code'}
                    modalTitle={'Country code'}
                    data={countryCodeOptions}
                    style={{ flex: 1.5 }}
                    getItemValue={item => item.value}
                    getItemLabel={item => item.label}
                    getDisplayedLabel={item => '+' + getCountryCodeValue(item)}
                    keyExtractor={item => item.value + item.label}
                  />
                  <Input
                    control={control}
                    as={PhoneField}
                    name="mobilePhoneNumber"
                    label={t('addLead.formFields.businessPhoneNumber')}
                    style={{ flex: 3 }}
                    keyboardType="numeric"
                    error={errors.mobilePhoneNumber?.message}
                    size={'large'}
                  />
                </Row>
              )}
              <Input
                control={control}
                as={SearchableDropdown<BusinessNatureEntity, string>}
                name="occupationIndustryCode"
                label={
                  country === 'ph'
                    ? t('addLead.formFields.natureOfBusiness')
                    : t('addLead.formFields.natureOfBusiness.optional')
                }
                modalTitle={
                  country === 'ph'
                    ? t('addLead.formFields.natureOfBusiness')
                    : t('addLead.formFields.natureOfBusiness.optional')
                }
                data={natureOfBusinessOptions}
                getItemValue={item => item.value}
                getItemLabel={item => item.label}
                searchable
                style={{ flex: 1 }}
              />
            </Column>
          </Row>

          {country === 'ph' && (
            <ResponsiveText
              children={t('addLead.subTitle.primaryContactDetails')}
              TypographyDefault={Typography.Body}
              TypographyWide={Typography.LargeBody}
              fontWeight="bold"
              style={{ marginVertical: ROW_GAP, color: colors.primary }}
            />
          )}
          {country === 'ib' && (
            <Column style={{ gap: ROW_GAP, paddingTop: ROW_GAP }}>
              <Input
                autoCapitalize="none"
                control={control}
                as={TextField}
                name="email"
                label={t('addLead.formFields.email.optional')}
                style={{ flex: 2 }}
                error={errors.email?.message}
              />
            </Column>
          )}
          {country === 'ph' && (
            <Column style={{ gap: ROW_GAP }}>
              <Input
                autoCapitalize="none"
                control={control}
                as={TextField}
                name="email"
                label={
                  country === 'ph'
                    ? t('addLead.formFields.email')
                    : t('addLead.formFields.email.optional')
                }
                style={{ flex: 2 }}
                error={errors.email?.message}
                onBlur={() => validateEmail(watchedEmail)}
                right={isLoadingEmailValidation ? <LoadingIndicator /> : <></>}
              />
              <Row>
                <Column
                  style={{ marginRight: COUNTRY_CODE_MR, minWidth: sizes[26] }}>
                  <Input
                    control={control}
                    as={Dropdown<CountryCode, string>}
                    name="mobilePhoneCountryCode"
                    label={t('addLead.formFields.code')}
                    modalTitle={t('addLead.formFields.code')}
                    data={countryCodeOptions}
                    getItemValue={item => item.value}
                    getItemLabel={item => item.label}
                    getDisplayedLabel={item => '+' + getCountryCodeValue(item)}
                    keyExtractor={item => item.value + item.label}
                  />
                </Column>
                <Input
                  control={control}
                  as={PhoneField}
                  name="mobilePhoneNumber"
                  label={t('addLead.formFields.mobileNumber')}
                  style={{ flex: 2 }}
                  error={errors.mobilePhoneNumber?.message}
                  keyboardType="phone-pad"
                />
              </Row>
            </Column>
          )}

          {country == 'ph' && (
            <>
              <ResponsiveText
                children={t('addLead.subTitle.secondaryContactDetails')}
                TypographyDefault={Typography.Body}
                TypographyWide={Typography.LargeBody}
                fontWeight="bold"
                style={{ marginVertical: ROW_GAP, color: colors.primary }}
              />
              <Row>
                <Column
                  style={{ marginRight: COUNTRY_CODE_MR, minWidth: sizes[26] }}>
                  <Input
                    control={control}
                    as={Dropdown<CountryCode, string>}
                    name="homePhoneCountryCode"
                    label={t('addLead.formFields.code')}
                    modalTitle={t('addLead.formFields.code')}
                    data={countryCodeOptions}
                    getItemValue={item => item.value}
                    getItemLabel={item => item.label}
                    getDisplayedLabel={item => '+' + getCountryCodeValue(item)}
                    keyExtractor={item => item.value + item.label}
                  />
                </Column>
                <Input
                  control={control}
                  as={PhoneField}
                  name="homePhoneNumber"
                  label={t('addLead.formFields.mobileNumber')}
                  style={{ flex: 2 }}
                  error={errors.homePhoneNumber?.message}
                  keyboardType="phone-pad"
                />
              </Row>
            </>
          )}

          {country == 'ph' && (
            <>
              <ResponsiveText
                children={t('addLead.subTitle.authorizedRepresentativeDetails')}
                TypographyDefault={Typography.Body}
                TypographyWide={Typography.LargeBody}
                fontWeight="bold"
                style={{ marginVertical: ROW_GAP, color: colors.primary }}
              />
              <Row>
                <Column style={{ flex: 1, gap: ROW_GAP }}>
                  <Input
                    control={control}
                    as={SearchableDropdown<Title, string>}
                    name="salutation"
                    label={t('addLead.formFields.title')}
                    modalTitle={t('addLead.formFields.title')}
                    data={titleOptions}
                    getItemValue={item => item.value}
                    getItemLabel={item => item.label}
                    searchable
                    style={{ flex: 1 }}
                  />
                  <Input
                    control={control}
                    as={TextField}
                    name="firstName"
                    label={t('addLead.formFields.firstName')}
                    error={errors.firstName?.message}
                  />
                  <Input
                    control={control}
                    as={TextField}
                    name="middleName"
                    label={t('addLead.formFields.middleName.optional')}
                    error={errors.middleName?.message}
                  />
                  <Input
                    control={control}
                    as={TextField}
                    name="lastName"
                    label={t('addLead.formFields.lastName')}
                    error={errors.lastName?.message}
                  />
                  <Input
                    control={control}
                    as={SearchableDropdown<Extension, string>}
                    name="nameExtension"
                    label={t('addLead.formFields.extensionName.optional')}
                    modalTitle={t('addLead.formFields.extensionName.optional')}
                    data={extensionOptions}
                    getItemValue={item => item.value}
                    getItemLabel={item => item.label}
                    searchable
                    style={{ flex: 1 }}
                  />
                  <Input
                    control={control}
                    as={TextField}
                    name="jobTitle"
                    label={t('addLead.formFields.position')}
                    error={errors.jobTitle?.message}
                  />
                </Column>
              </Row>
            </>
          )}
        </KeyboardAwareScrollView>
      </Animated.View>

      <SubmitButtonContainer isWideScreen={isWideScreen} isiOS={isiOS}>
        <Button
          variant={'primary'}
          loading={isLoading || isLoadingEmailValidation || isLoadingLookupLead}
          text={t('addLead.formFields.saveButton')}
          onPress={handleSubmit(onValidSubmit, onInvalidSubmit)}
          disabled={disableButton || !policyConfirmed}
          style={[{ maxWidth: 400, width: '100%', marginBottom: bottom }]}
          gaParams={{
            eventType: 'lead_created',
            formSource: name.includes('Lead')
              ? 'menu_leads_and_customers'
              : 'homepage_cta',
          }}
        />
      </SubmitButtonContainer>

      <AddLeadOrEntityTransferModal
        type={'EntityToIndividual'}
        visible={modalVisible}
        closeHandler={() => setModalVisible(false)}
      />
    </>
  );
}

type SubmitButtonContainerProps = {
  theme?: Theme;
  isWideScreen: boolean;
  isiOS: boolean;
};

const SubmitButtonContainer = styled(View)(
  ({ theme, isWideScreen }: SubmitButtonContainerProps) => {
    if (isWideScreen) {
      return {
        backgroundColor: theme?.colors.background,
        padding: theme?.sizes[5],
        borderTopWidth: 1,
        borderTopColor: theme?.colors.palette.fwdGrey[50],
        alignItems: 'center',
      };
    }
    return {
      backgroundColor: theme?.colors.background,
      padding: theme?.sizes[4],
      // paddingBottom: isiOS ? theme?.sizes[0] : theme?.sizes[4],
      borderTopWidth: 1,
      borderTopColor: theme?.colors.palette.fwdGrey[50],
      alignItems: 'center',
    };
  },
);

const LeadIconContainer = styled(Row)(({ theme }) => ({
  justifyContent: 'center',
  marginBottom: theme.space[6],
  gap: theme.space[11],
}));

const getCountryCodeValueOnSubmit = (item: string) => item.split(' - ')[0];
const getCountryCodeValue = (item: CountryCode) => item.value.split(' - ')[0];
