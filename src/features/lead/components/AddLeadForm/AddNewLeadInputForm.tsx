import styled from '@emotion/native';
import { Theme, useTheme } from '@emotion/react';
import { yupResolver } from '@hookform/resolvers/yup';
import {
  NavigationProp,
  RouteProp,
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import ButtonSeparator from 'components/ButtonSeparator';
import Input from 'components/Input';
import PhoneField from 'components/PhoneField';
import ResponsiveText from 'components/ResponsiveTypography';
import {
  Box,
  Button,
  Column,
  DatePicker,
  Dropdown,
  Icon,
  LoadingIndicator,
  Picker,
  Row,
  TextField,
  Typography,
  addToast,
} from 'cube-ui-components';
import { format, sub } from 'date-fns';
import * as Contacts from 'expo-contacts';
import LeadIcon from 'features/lead/components/AddLeadForm/LeadIcon';
import { useCreateLead } from 'features/lead/hooks/useCreateLead';
import { useLookupLead } from 'features/lead/hooks/useLookupLead';
import useValidateEmail from 'features/lead/hooks/useValidateEmail';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useRootStackNavigation } from 'hooks/useRootStack';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import {
  SubmitErrorHandler,
  SubmitHandler,
  UseFormHandleSubmit,
  get,
  useForm,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  Platform,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Animated, { LinearTransition } from 'react-native-reanimated';
import RootSiblings from 'react-native-root-siblings';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  AddLeadFormData,
  CreateLeadRequest,
  LookupLeadRequest,
  RootStackParamList,
} from 'types';
import { CountryCode } from 'types/optionList';
import { country } from 'utils/context';
import { calculateAge as calculateAgeAsANB } from 'utils/helper/calculateAge';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { strictEmailRegex } from 'utils/validation/customValidation';
import {
  KeyOfRequiredAddLeadFieldsObj,
  addLeadValidationSchema,
  initialLeadFormData,
  requiredAddLeadFieldsObj,
} from 'utils/validation/ph/addLeadFormSchema';
import { object, string } from 'yup';
import EntityIcon from '../../components/AddLeadForm/EntityIcon';
import AddLeadOrEntityTransferModal from '../../components/AddLeadOrEntityTransferModal';
import LeadIconV2 from './LeadIconV2';
import { useValidationYupResolver } from 'utils/validation';
import useBoundStore from 'hooks/useBoundStore';
import { ICON_HIT_SLOP } from 'constants/hitSlop';
import PolicyConfirm from '../PolicyConfirm';

const INVALID_EMAIL_INPUT = 'The email doesn’t exist. Please try again.';

const requiredFields = Object.keys(
  requiredAddLeadFieldsObj,
) as Array<KeyOfRequiredAddLeadFieldsObj>;

export default function AddNewLeadForm() {
  const { t } = useTranslation('lead');
  const { t: ct } = useTranslation('common');

  const { isWideScreen, isNarrowScreen } = useWindowAdaptationHelpers();
  const { colors, sizes, space, borderRadius } = useTheme();
  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { bottom } = useSafeAreaInsets();
  const { params, name } =
    useRoute<RouteProp<RootStackParamList, 'AddNewLead'>>();
  const isiOS = Platform.OS === 'ios';

  const [modalVisible, setModalVisible] = useState(false);
  const { navigate } = useRootStackNavigation();

  const { mutate, isLoading } = useCreateLead();
  const { mutate: mutateToValidateEmail, isLoading: isLoadingEmailValidation } =
    useValidateEmail();
  const { mutate: mutateToCreateLead } = useCreateLead();
  const { mutateAsync: mutateLookupLead, isLoading: isLoadingLookupLead } =
    useLookupLead();

  const resetTodayFilters = useBoundStore(
    state => state.leadActions.resetTodayFilters,
  );
  const { data: optionList } = useGetOptionList();

  const resolver = useValidationYupResolver(addLeadValidationSchema);

  const {
    watch,
    setValue,
    handleSubmit,
    reset,
    control,
    formState: { errors },
    setError,
    clearErrors,
  } = useForm<AddLeadFormData>({
    defaultValues: initialLeadFormData,
    resolver,
    mode: 'onBlur',
  });

  const { genderOptions, countryCodeOptions } = useMemo(() => {
    return {
      genderOptions:
        // Translation from backend?
        optionList?.GENDER.options.map(item => ({
          text: item.label,
          value: item.value,
        })) || [],
      countryCodeOptions: optionList?.COUNTRY_CODE.options || [],
    };
  }, [optionList]);

  const isSubmitDisabled = watch(requiredFields).some(field => field === '');

  const currentEmail = watch('email') ?? '';
  const birthDate = watch('birthDate');

  const age = birthDate ? calculateAgeAsANB(new Date(birthDate)) : '';

  const isErrorsActive = Object.keys(errors).length > 0;

  const validateEmail = (email: string, actionAfterSuccess?: () => void) => {
    if (email.trim() === '') {
      actionAfterSuccess && actionAfterSuccess();
      return;
    }

    //  email format can have or not have a number, eg. xyz@gmail
    if (country === 'ib' || country === 'id') {
      object({
        email: string().test('email', 'Invalid email format', value =>
          !value ? true : strictEmailRegex.test(value),
        ),
      });
      return;
    }

    //  email format must have number, eg. <EMAIL>
    if (country === 'ph' || country === 'my') {
      mutateToValidateEmail(email, {
        onSuccess: data => {
          if (data.valid === false) {
            setError('email', {
              message: INVALID_EMAIL_INPUT,
            });
            return;
          }
          actionAfterSuccess && actionAfterSuccess();
        },
      });
    }
  };

  // TODO_Alex: Look for a way to get the type of the schema better
  type HandleSubmitSchema = typeof handleSubmit extends UseFormHandleSubmit<
    infer U
  >
    ? U
    : never;
  const closeModalHandler = () => {
    // onClose();
    reset();
  };
  const onValidSubmit: SubmitHandler<HandleSubmitSchema> = async data => {
    const ibNewData = {
      firstName: data.fullName,
      lastName: ' ',
      mobilePhoneCountryCode: getCountryCodeValueOnSubmit(
        data.mobilePhoneCountryCode,
      ),
      mobilePhoneNumber: data.mobilePhoneNumber,
    };

    const phNewData = {
      firstName: data.firstName,
      lastName: data.lastName,
      mobilePhoneCountryCode: getCountryCodeValueOnSubmit(
        data.mobilePhoneCountryCode,
      ),
      mobilePhoneNumber: data.mobilePhoneNumber,
    };

    const newData: CreateLeadRequest =
      country === 'ib' || country === 'id' ? ibNewData : phNewData;

    const ibLookupLeadReq: LookupLeadRequest = {
      // sourceId: 'CUBE',
      isIndividual: true,
      firstName: data.fullName,
      lastName: ' ',
      mobilePhoneCountryCode: getCountryCodeValueOnSubmit(
        data.mobilePhoneCountryCode,
      ),
      mobilePhoneNumber: data.mobilePhoneNumber,
      // acceptPhoneCall: true,
      // acceptEmail: true,
      // isSmoker: false,
      // extra: {},
    };

    const phLookupLeadReq: LookupLeadRequest = {
      sourceId: 'CUBE',
      isIndividual: true,
      firstName: data.firstName,
      lastName: data.lastName,
      mobilePhoneCountryCode: getCountryCodeValueOnSubmit(
        data.mobilePhoneCountryCode,
      ),
      mobilePhoneNumber: data.mobilePhoneNumber,
      acceptPhoneCall: true,
      acceptEmail: true,
      isSmoker: false,
      extra: {},
    };

    const lookupLeadReq: LookupLeadRequest =
      country === 'ib' || country === 'id' ? ibLookupLeadReq : phLookupLeadReq;

    if (data.birthDate) {
      newData.birthDate = format(new Date(data.birthDate), 'yyyy-MM-dd');
    }
    if (data.genderCode) {
      newData.genderCode = data.genderCode;
    }
    if (data.interestedCategories) {
      newData.interestedCategories = [data.interestedCategories];
      lookupLeadReq.interestedCategories = [data.interestedCategories];
    } else {
      lookupLeadReq.interestedCategories = [];
    }
    if (data.email) {
      newData.email = data.email;
    }

    try {
      await mutateLookupLead(lookupLeadReq, {
        onSuccess: res => {
          const countryCode = res?.mobilePhoneCountryCode || '';
          const phoneNumber = res?.mobilePhoneNumber || '';
          const mobile = `${countryCode} ${phoneNumber.replace(
            /^\d{4}(\d.)/g,
            '****$1',
          )}`.trim();
          const parsedEmail = (res?.email || '').replace(
            /^\w{4}(\w.)/g,
            '****$1',
          );
          const email = parsedEmail ? parsedEmail : '--';
          const existingLeadFound = new RootSiblings(
            (
              <BackgroundContainer>
                <Background />
                <Box
                  backgroundColor={colors.background}
                  borderRadius={borderRadius.large}
                  width={country === 'id' ? 344 : undefined}
                  maxWidth={country === 'id' ? undefined : 380}
                  minHeight={country === 'id' ? undefined : 300}
                  p={country === 'id' ? space[6] : space[12]}>
                  <TouchableOpacity
                    style={{
                      alignSelf: 'flex-end',
                    }}
                    hitSlop={ICON_HIT_SLOP}
                    onPress={() => existingLeadFound.destroy()}>
                    <Icon.Close size={sizes[6]} fill={colors.secondary} />
                  </TouchableOpacity>
                  <Typography.H6 fontWeight="bold">
                    {t('addLead.existing.lead.found')}
                  </Typography.H6>
                  <Typography.LargeBody style={{ marginTop: space[4] }}>
                    {t('addLead.existing.lead.tip2', { mobile, email })}
                  </Typography.LargeBody>
                  <Typography.LargeBody
                    style={{ marginTop: space[2], marginBottom: space[6] }}>
                    {t('addLead.existing.lead.tip3', { mobile, email })}
                  </Typography.LargeBody>
                  <Box alignItems="center">
                    <Button
                      text={ct('viewProfile')}
                      variant="primary"
                      onPress={() => {
                        navigate('LeadProfile', {
                          id: res.id ?? '',
                          // If the lead is an entity, isIndividual should be false
                          isIndividualLead: true,
                        });
                        existingLeadFound.destroy();
                      }}
                      style={{ width: country === 'id' ? '100%' : 156 }}
                    />
                  </Box>
                </Box>
              </BackgroundContainer>
            ),
          );
          return;
        },
        onError: error => {
          const errorObj: any = error as any;
          if (errorObj.response.status == '404') {
            // As discussed with Alex, 'submitLeadForm' & 'validateEmail' should be kept for ph mobile only
            if (country === 'ph') {
              const submitLeadForm = () =>
                mutate(newData, {
                  onSuccess: () => {
                    navigation.canGoBack() && navigation.goBack();
                    addToast([
                      {
                        IconLeft: Icon.Tick,
                        message: 'A new lead is added.',
                      },
                    ]);
                  },
                });

              validateEmail(currentEmail, submitLeadForm);
            }

            if (country === 'ib' || country === 'id') {
              mutateToCreateLead(newData, {
                onSuccess: () => {
                  //Only apply for IDN first
                  if (country === 'id') {
                    resetTodayFilters();
                  }
                  navigation.canGoBack() && navigation.goBack();
                  addToast([
                    {
                      IconLeft: Icon.Tick,
                      message: t('addLead.formFields.newLeadIsAdd'),
                    },
                  ]);
                  reset();
                },
                onError: e => {
                  addToast([
                    {
                      message: t('addLead.formFields.pleaseTryAgainLater'),
                    },
                  ]);
                  console.log('onError: ', e);
                },
              });
            }
          }
        },
      });
    } catch (error) {
      console.log(error);
    }
  };

  const onInvalidSubmit: SubmitErrorHandler<HandleSubmitSchema> = error => {
    console.log(
      '🚀 ~ file: AddNewLeadInputForm.tsx:163 ~ AddNewLeadForm ~ error:',
      error,
    );
  };
  const checkValuesFromParams = () => {
    if (params?.contactInfo?.firstName) {
      setValue('firstName', params.contactInfo.firstName);
    } else {
      setValue('firstName', '');
    }
    if (params?.contactInfo?.lastName) {
      setValue('lastName', params.contactInfo.lastName);
    } else {
      setValue('lastName', '');
    }

    // FullName is only available in ib and id mobile but not in ph mobile
    if (country === 'ib' || country === 'id') {
      const { firstName, lastName } = params?.contactInfo || {};
      const fullName =
        firstName && lastName
          ? `${firstName} ${lastName}`
          : firstName || lastName || '';
      setValue('fullName', fullName);
    }

    if (params?.contactInfo?.phoneNumbers) {
      const firstSetTel: Contacts.PhoneNumber | undefined =
        params.contactInfo.phoneNumbers[0];
      const preFormatedNumber = firstSetTel?.number ?? '';
      const phoneNumberArray = preFormatedNumber.split(' ');
      const countryCodeWithPlus = phoneNumberArray[0];
      if (
        preFormatedNumber &&
        preFormatedNumber[0] == '+' &&
        phoneNumberArray.length > 1
      ) {
        // **Jessica: CountryCode can be extracted, extracted as +xx yyyyyyy
        setValue('mobilePhoneCountryCode', phoneNumberArray[0]);
        const mobilePhoneCountryCodeValue = countryCodeOptions.filter(
          item =>
            getCountryCodeValue(item) ===
            countryCodeWithPlus.replace(/\D/g, ''),
        );
        setValue(
          'mobilePhoneCountryCode',
          mobilePhoneCountryCodeValue[0]?.value || '',
        );
        setValue(
          'mobilePhoneNumber',
          preFormatedNumber.replace(countryCodeWithPlus, '').replace(/\D/g, ''),
        );
      } else {
        // **Jessica: CountryCode cannot be extracted
        setValue('mobilePhoneCountryCode', '');
        setValue('mobilePhoneNumber', preFormatedNumber.replace(/\D/g, ''));
      }
    } else {
      setValue('mobilePhoneCountryCode', '');
      setValue('mobilePhoneNumber', '');
    }
    if (params?.contactInfo?.emails) {
      const firstSetEmail: Contacts.Email | undefined =
        params.contactInfo.emails[0];
      setValue('email', firstSetEmail.email ?? '');
    } else {
      setValue('email', '');
    }
  };

  useEffect(() => {
    checkValuesFromParams();
  }, [params]);

  useFocusEffect(
    React.useCallback(() => {
      if (!params?.contactInfo && countryCodeOptions?.length > 0) {
        const countryName = t(country, { ns: 'locale' } as never);
        const countryCodePresetItem = countryCodeOptions.find(item => {
          // - Malaysia should use '+60' as its country code mentioned in Figma, but its country name 'IB' cannot be used to find the corresponding country code.
          if (typeof countryName === 'string' && countryName === 'ib') {
            return item.label.includes('Malaysia');
          }
          return item.label.includes(countryName as never);
        });
        if (countryCodePresetItem) {
          setValue('mobilePhoneCountryCode', countryCodePresetItem?.value);
        }
      }
    }, [params, countryCodeOptions]),
  );

  const uploadFromContactBookOnPress = async () => {
    const { status } = await Contacts.requestPermissionsAsync();
    if (status === Contacts.PermissionStatus.GRANTED) {
      navigation.navigate('ContactBook', { routeName: 'AddNewLead' });
    }
  };

  const leadIconMarginRight = isWideScreen ? sizes[5] : sizes[4];

  useEffect(() => {
    if (errors.email) {
      clearErrors('email');
    }
  }, [currentEmail]);

  const DatePickerMaxDate = useMemo(() => {
    return country === 'id' ? sub(new Date(), { months: 1 }) : new Date();
  }, []);

  const defaultDate = useMemo(
    () => new Date(new Date().getFullYear() - 18, 0, 1),
    [],
  );

  const focusOnField = useCallback(
    (name: keyof AddLeadFormData) => {
      const field = get(control._fields, name);
      field?._f?.ref?.focus();
    },
    [control._fields],
  );

  const [policyConfirmed, setPolicyConfirmed] = useState(false);

  return (
    <>
      <Animated.View layout={LinearTransition} style={{ flex: 1 }}>
        <KeyboardAwareScrollView
          contentContainerStyle={[
            {
              paddingTop: sizes[4],
              paddingBottom: sizes[12],
              paddingHorizontal: sizes[4],
            },
            isWideScreen && {
              paddingTop: sizes[5],
              paddingBottom: sizes[15],
              paddingHorizontal: sizes[4],
            },
            isNarrowScreen && {
              paddingTop: sizes[4],
              paddingBottom: sizes[8],
              paddingHorizontal: sizes[3],
            },
          ]}>
          <Button
            onPress={uploadFromContactBookOnPress}
            text={t('addLead.uploadFromPhoneBook.buttonLabel')}
            variant={'secondary'}
            style={{ maxWidth: 300, alignSelf: 'center' }}
            contentStyle={isWideScreen ? { height: 50 } : undefined}
          />
          <ButtonSeparator text={t('orSelect')} />
          <PolicyConfirm
            checked={policyConfirmed}
            onChange={setPolicyConfirmed}
          />
          {country === 'ib' && (
            <>
              <IBLeadIconContainer>
                <Column style={{ alignItems: 'center' }}>
                  <LeadIconV2 gender={watch('genderCode')} />
                  <Typography.LargeLabel
                    children={'Individual'}
                    fontWeight="bold"
                    style={{
                      marginTop: sizes[3],
                    }}
                  />
                </Column>
                <TouchableOpacity
                  style={{ alignItems: 'center' }}
                  onPress={() => setModalVisible(true)}>
                  <EntityIcon />
                  <Typography.LargeLabel
                    children={t('addLead.category.entity')}
                    fontWeight="bold"
                    style={{
                      color: colors.palette.fwdGreyDark,
                      marginTop: sizes[3],
                    }}
                  />
                </TouchableOpacity>
              </IBLeadIconContainer>

              <Input
                control={control}
                as={TextField}
                name="fullName"
                label={t('addLead.formFields.fullName')}
                error={errors.fullName?.message}
                style={{ flex: 1, marginBottom: space[6] }}
                maxLength={50}
              />
            </>
          )}
          {country === 'id' && (
            <>
              <Row alignItems="center" pb={30}>
                <Column
                  style={{
                    alignItems: 'center',
                    minWidth: sizes[26],
                    marginRight: leadIconMarginRight,
                  }}>
                  <LeadIconV2 gender={watch('genderCode')} />
                </Column>
                <Input
                  control={control}
                  as={TextField}
                  name="fullName"
                  label={t('addLead.formFields.fullName')}
                  error={errors.fullName?.message}
                  returnKeyType="done"
                  style={{ flex: 1 }}
                  onSubmitEditing={() => {
                    focusOnField('mobilePhoneNumber');
                  }}
                  shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                  initialHighlight={true}
                />
              </Row>
            </>
          )}
          {country === 'ph' && (
            <>
              <PHLeadIconContainer>
                <View style={{ alignItems: 'center' }}>
                  <LeadIcon gender={watch('genderCode')} />
                  <Typography.LargeLabel
                    children={t('addLead.category.individual')}
                    fontWeight="bold"
                    style={{ marginTop: sizes[3] }}
                  />
                </View>
                <TouchableOpacity
                  style={{ alignItems: 'center' }}
                  onPress={() => setModalVisible(true)}>
                  <EntityIcon />
                  <Typography.LargeLabel
                    children={t('addLead.category.entity')}
                    fontWeight="bold"
                    style={{
                      color: colors.palette.fwdGreyDark,
                      marginTop: sizes[3],
                    }}
                  />
                </TouchableOpacity>
              </PHLeadIconContainer>
            </>
          )}

          <Row>
            <Column style={{ flex: 1, gap: sizes[5] }}>
              {country === 'ph' && (
                <>
                  <Input
                    control={control}
                    as={TextField}
                    name="firstName"
                    label={t('addLead.formFields.firstName')}
                    error={errors.firstName?.message}
                  />
                  <Input
                    control={control}
                    as={TextField}
                    name="lastName"
                    label={t('addLead.formFields.lastName')}
                    error={errors.lastName?.message}
                  />
                </>
              )}
              <Row>
                <Column
                  style={{
                    marginRight: leadIconMarginRight,
                    minWidth: sizes[26],
                  }}>
                  <Input
                    control={control}
                    as={Dropdown<CountryCode, string>}
                    name="mobilePhoneCountryCode"
                    label={t('addLead.formFields.code')}
                    modalTitle={t('addLead.formFields.code')}
                    data={countryCodeOptions}
                    getItemValue={item => item.value}
                    getItemLabel={item => item.label}
                    getDisplayedLabel={item => '+' + getCountryCodeValue(item)}
                    keyExtractor={item => item.value + item.label}
                  />
                </Column>
                <Input
                  control={control}
                  as={PhoneField}
                  name="mobilePhoneNumber"
                  label={t('addLead.formFields.mobileNumber')}
                  style={{ flex: 2 }}
                  error={errors.mobilePhoneNumber?.message}
                  keyboardType="phone-pad"
                  shouldHighlightOnUntouched={
                    country === 'id' ? Input.defaultHighlightCheck : undefined
                  }
                  initialHighlight={country === 'id' ? true : undefined}
                />
              </Row>
            </Column>
          </Row>
          <ResponsiveText
            TypographyDefault={Typography.Body}
            TypographyWide={Typography.LargeBody}
            fontWeight="bold"
            style={{
              marginTop: sizes[5],
              marginBottom: country === 'id' ? sizes[5] : sizes[7],
              color: colors.primary,
            }}>
            {t('addLead.formFields.subTitle')}
          </ResponsiveText>
          <Column style={{ gap: isWideScreen ? sizes[7] : sizes[5] }}>
            {country !== 'id' && (
              <Input
                control={control}
                as={Picker}
                name="genderCode"
                type="text"
                label={t('addLead.formFields.gender.optional')}
                error={errors.genderCode?.message}
                items={genderOptions}
              />
            )}
            <Column style={{ gap: sizes[1] }}>
              {/* Age field is only available in ib and id mobile but not in ph mobile */}
              {country === 'ib' || country === 'id' ? (
                <Row style={{ gap: space[3] }}>
                  <Input
                    control={control}
                    as={DatePicker}
                    name="birthDate"
                    label={t('addLead.formFields.dateOfBirth.optional')}
                    modalTitle={t('addLead.formFields.dateOfBirth')}
                    defaultValue={defaultDate}
                    maxDate={DatePickerMaxDate}
                    formatDate={val => (val ? dateFormatUtil(val) : '')}
                    style={{ flex: 1 }}
                    modeSwitchEnabled={country === 'id'}
                  />
                  <TextField
                    disabled={true}
                    label={t('addLead.formFields.age')}
                    value={age === 0 ? '0' : age}
                    style={{ flex: 0.3 }}
                  />
                </Row>
              ) : (
                <Input
                  control={control}
                  as={DatePicker}
                  name="birthDate"
                  label={t('addLead.formFields.dateOfBirth.optional')}
                  modalTitle={t('addLead.formFields.dateOfBirth')}
                  maxDate={new Date()}
                  formatDate={val => (val ? dateFormatUtil(val) : '')}
                />
              )}

              <Typography.ExtraSmallLabel
                style={{ marginLeft: sizes[4] }}
                color={colors.palette.fwdGreyDarker}>
                {/* ph is MM/DD/YYYY in shown screen */}
                {country === 'ph' ? 'MM/DD/YYYY' : 'DD/MM/YYYY'}
              </Typography.ExtraSmallLabel>
            </Column>
            {country === 'id' && (
              <Input
                control={control}
                as={Picker}
                name="genderCode"
                type="text"
                label={t('addLead.formFields.gender.optional')}
                error={errors.genderCode?.message}
                items={genderOptions}
              />
            )}
            <Input
              autoCapitalize="none"
              control={control}
              as={TextField}
              name="email"
              label={t('addLead.formFields.email.optional')}
              style={{ flex: 2 }}
              error={errors.email?.message}
              onBlur={() => validateEmail(currentEmail)}
              right={isLoadingEmailValidation ? <LoadingIndicator /> : <></>}
            />
            {country !== 'id' && (
              <Input
                control={control}
                as={Dropdown<string, string>}
                name="interestedCategories"
                label={t(
                  'addLead.formFields.interestedProductCategory.optional',
                )}
                modalTitle={t('addLead.formFields.interestedProductCategory')}
                // TODO_Alex: data list shall come from option list API
                // TODO(Harrison): CUBEPH-147
                data={['Education', 'Legacy', 'Protection', 'Wealth']}
                getItemValue={item => item}
                getItemLabel={item => item}
                style={{ flex: 1 }}
              />
            )}
          </Column>
        </KeyboardAwareScrollView>
      </Animated.View>
      <SubmitButtonContainer isWideScreen={isWideScreen} isiOS={isiOS}>
        <Button
          variant={'primary'}
          loading={isLoading || isLoadingEmailValidation || isLoadingLookupLead}
          text={t('addLead.formFields.saveButton')}
          onPress={handleSubmit(onValidSubmit, onInvalidSubmit)}
          disabled={
            isSubmitDisabled ||
            isErrorsActive ||
            isLoadingEmailValidation ||
            isLoading ||
            isLoadingLookupLead ||
            !policyConfirmed
          }
          style={[{ maxWidth: 400, width: '100%', marginBottom: bottom }]}
          gaParams={{
            eventType: 'lead_created',
            formSource: name.includes('Lead')
              ? 'menu_leads_and_customers'
              : 'homepage_cta',
          }}
        />
      </SubmitButtonContainer>

      <AddLeadOrEntityTransferModal
        type={'IndividualToEntity'}
        visible={modalVisible}
        closeHandler={() => setModalVisible(false)}
      />
    </>
  );
}

type SubmitButtonContainerProps = {
  theme?: Theme;
  isWideScreen: boolean;
  isiOS: boolean;
};

const SubmitButtonContainer = styled(View)(
  ({ theme, isWideScreen, isiOS }: SubmitButtonContainerProps) => {
    if (isWideScreen) {
      return {
        backgroundColor: theme?.colors.background,
        padding: theme?.sizes[5],
        borderTopWidth: 1,
        borderTopColor: theme?.colors.palette.fwdGrey[50],
        alignItems: 'center',
      };
    }
    return {
      backgroundColor: theme?.colors.background,
      padding: theme?.sizes[4],
      // paddingBottom: isiOS ? theme?.sizes[0] : theme?.sizes[4],
      borderTopWidth: 1,
      borderTopColor: theme?.colors.palette.fwdGrey[50],
      alignItems: 'center',
    };
  },
);

const IBLeadIconContainer = styled(Row)(({ theme }) => ({
  gap: theme.space[11],
  marginBottom: theme.space[4],
  alignItems: 'center',
  justifyContent: 'center',
}));

const PHLeadIconContainer = styled(Row)(({ theme }) => ({
  justifyContent: 'center',
  marginBottom: theme.space[6],
  gap: theme.space[11],
  alignItems: 'center',
}));

const BackgroundContainer = styled(Box)(({ theme }) => ({
  ...StyleSheet.absoluteFillObject,
  justifyContent: 'center',
  alignItems: 'center',
}));

const Background = styled.View(() => ({
  ...StyleSheet.absoluteFillObject,
  width: '100%',
  height: '100%',
  justifyContent: 'center',
  alignItems: 'center',
  backgroundColor: 'rgba(0, 0, 0, 0.5)',
}));

const getCountryCodeValueOnSubmit = (item: string) => item.split(' - ')[0];
const getCountryCodeValue = (item: CountryCode) => item.value.split(' - ')[0];
