import React from 'react';
import { TouchableOpacity, TouchableOpacityProps } from 'react-native';
import { Row, Typography } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import { country } from 'utils/context';

export default function TagButton(
  props: {
    isActive: boolean;
    label: string;
    Icon?: React.ReactNode;
    onChange?: (isActive: boolean) => void;
  } & TouchableOpacityProps,
) {
  const { colors, space, borderRadius } = useTheme();
  const { isActive, label, onChange, style, ...rest } = props;
  const borderWidth = isActive ? 2 : 1;
  return (
    <TouchableOpacity
      onPress={() => onChange?.(!isActive)}
      style={[
        {
          borderWidth: borderWidth,
          justifyContent: 'center',
          alignItems: 'center',
          borderRadius: borderRadius.full,
          paddingHorizontal: space[4],
          borderColor: isActive ? colors.primary : colors.palette.fwdGrey[100],
          backgroundColor: isActive
            ? colors.primaryVariant3
            : colors.background,
          height: space[10],
        },
        style,
      ]}
      {...rest}>
      <Row alignItems="center" gap={space[1]}>
        {props?.Icon}
        {country === 'id' ? (
          <Typography.H8
            fontWeight={isActive ? 'bold' : 'normal'}
            color={colors.secondary}>
            {label}
          </Typography.H8>
        ) : (
          <Typography.H7
            fontWeight={isActive ? 'medium' : 'normal'}
            color={isActive ? colors.primary : colors.secondary}>
            {label}
          </Typography.H7>
        )}
      </Row>
    </TouchableOpacity>
  );
}
