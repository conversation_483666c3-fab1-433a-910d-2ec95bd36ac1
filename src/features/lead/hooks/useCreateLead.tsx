import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createLead } from 'api/leadApi';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import { CreateEntityRequest, CreateLeadRequest } from 'types';
import { CHANNELS } from 'types/channel';
import { useGenerateBltsRefInfoStored } from 'utils/helper/bltsUtils';

export function useCreateLead() {
  const queryClient = useQueryClient();
  const channel = useGetCubeChannel();
  const generateBltsRefInfoStored = useGenerateBltsRefInfoStored(channel);
  return useMutation({
    mutationFn: (requestData: CreateLeadRequest | CreateEntityRequest) => {
      let extra = requestData.extra ?? {};
      if (
        (channel === CHANNELS.BANCA || channel === CHANNELS.AFFINITY) &&
        !requestData.extra
      ) {
        extra = generateBltsRefInfoStored;
      }

      let payload: CreateLeadRequest | CreateEntityRequest = {
        ...requestData,
        extra,
      };

      Object.keys(payload).forEach(key => {
        if (
          payload[key as keyof (CreateLeadRequest | CreateEntityRequest)] === ''
        ) {
          payload[key as keyof (CreateLeadRequest | CreateEntityRequest)] =
            undefined;
        }
      });

      return createLead(payload);
    },
    onSuccess: () => {
      setTimeout(() => {
        queryClient.invalidateQueries({
          queryKey: ['lead'],
        });
      }, 500);
    },
    onError(error, variables, context) {
      console.log(
        '🚀🔴🔴 ~ file: useCreateLead.tsx:18 ~ onError ~ error:',
        error,
        context,
      );
    },
  });
}
