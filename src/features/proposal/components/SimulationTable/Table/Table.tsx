import React, { useCallback, useMemo, useRef } from 'react';
import {
  Animated,
  ScrollViewProps,
  NativeSyntheticEvent,
  NativeScrollEvent,
  FlatList,
  Platform,
} from 'react-native';
import { Row } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import FreezeColumns from './FreezeColumns';
import { HeaderData } from './HeaderTable';
import styled from '@emotion/native';
import Columns from './Column';

export type OnScroll = Pick<
  ScrollViewProps,
  'onScroll' | 'onScrollBeginDrag' | 'onScrollAnimationEnd'
>;

export type RowData = { origin: string; data: string[] };

export interface Props {
  width: number;
  headerData: HeaderData[];
  data: RowData[];
  freezeColumnWidths?: number[];
  columnWidths: number[];
  columnWidthRows?: number[];
  renderHeaderCell: (
    index: number,
    data: HeaderData,
    freeze: boolean,
  ) => JSX.Element | null;
  renderCell: (
    rowIndex: number,
    columnIndex: number,
    origin: string,
    data: string,
    freeze: boolean,
  ) => JSX.Element | null;
  onEndReached?: () => void;
}

const Table = ({
  width,
  headerData,
  data,
  freezeColumnWidths = [],
  columnWidths,
  columnWidthRows,
  renderHeaderCell,
  renderCell,
  onEndReached,
}: Props) => {
  const { colors } = useTheme();
  const freezeIndex = freezeColumnWidths.length;
  const freezeWidth = useMemo(
    () => (freezeIndex > 0 ? freezeColumnWidths.reduce((w, i) => w + i, 0) : 0),
    [freezeIndex, freezeColumnWidths],
  );

  const unfreezeWidth = useMemo(
    () => columnWidths.reduce((w, i) => w + i, 0),
    [columnWidths],
  );

  const horizontalScrollEnabled = width < unfreezeWidth + freezeWidth;

  const scrollViewRef = useRef<FlatList<RowData>>(null);
  const freezeScrollViewRef = useRef<FlatList<RowData>>(null);

  const scrollY = useRef(new Animated.Value(0)).current;
  const freezeScrollY = useRef(new Animated.Value(0)).current;
  const activeViewRef = useRef<'freeze' | 'normal' | undefined>();
  const onScroll = useMemo(
    () =>
      Animated.event(
        [
          {
            nativeEvent: {
              contentOffset: {
                y: scrollY,
              },
            },
          },
        ],
        {
          useNativeDriver: true,
          listener: ({
            nativeEvent: {
              contentOffset: { y },
            },
          }: NativeSyntheticEvent<NativeScrollEvent>) => {
            if (activeViewRef.current === 'normal' || Platform.OS === 'web') {
              if (freezeScrollViewRef.current) {
                freezeScrollViewRef.current.scrollToOffset({
                  offset: y,
                  animated: false,
                });
              }
            }
          },
        },
      ),
    [scrollY],
  );

  const onFreezeScroll = useMemo(
    () =>
      Animated.event(
        [
          {
            nativeEvent: {
              contentOffset: {
                y: freezeScrollY,
              },
            },
          },
        ],
        {
          useNativeDriver: true,
          listener: ({
            nativeEvent: {
              contentOffset: { y },
            },
          }: NativeSyntheticEvent<NativeScrollEvent>) => {
            if (activeViewRef.current === 'freeze' || Platform.OS === 'web') {
              if (scrollViewRef.current) {
                scrollViewRef.current.scrollToOffset({
                  offset: y,
                  animated: false,
                });
              }
            }
          },
        },
      ),
    [freezeScrollY],
  );

  const onFreezeScrollBeginDrag = useCallback(
    () => (activeViewRef.current = 'freeze'),
    [],
  );
  const onScrollBeginDrag = useCallback(
    () => (activeViewRef.current = 'normal'),
    [],
  );
  const onScrollAnimationEnd = useCallback(
    () => (activeViewRef.current = undefined),
    [],
  );

  return (
    <Container bgColor={colors.background}>
      {freezeIndex > 0 && (
        <FreezeColumns
          ref={freezeScrollViewRef}
          freezeColumnWidths={freezeColumnWidths}
          headerData={headerData}
          freezeIndex={freezeIndex}
          scrollY={scrollY}
          onScroll={onFreezeScroll}
          onScrollBeginDrag={onFreezeScrollBeginDrag}
          onScrollAnimationEnd={onScrollAnimationEnd}
          data={data}
          renderHeaderCell={renderHeaderCell}
          renderCell={renderCell}
        />
      )}
      <Columns
        ref={scrollViewRef}
        horizontalScrollEnabled={horizontalScrollEnabled}
        headerData={headerData}
        freezeIndex={freezeIndex}
        columnWidths={columnWidths}
        columnWidthRows={columnWidthRows}
        scrollY={scrollY}
        onScroll={onScroll}
        onScrollBeginDrag={onScrollBeginDrag}
        onScrollAnimationEnd={onScrollAnimationEnd}
        data={data}
        renderHeaderCell={renderHeaderCell}
        renderCell={renderCell}
        onEndReached={onEndReached}
      />
    </Container>
  );
};
export default React.memo(Table);

const Container = styled(Row)(() => ({
  flex: 1,
  overflow: 'hidden',
}));
