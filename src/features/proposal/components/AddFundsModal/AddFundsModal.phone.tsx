import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Header from 'components/Header';
import { Button, Typography } from 'cube-ui-components';
import useFundFilter from 'features/proposal/hooks/fund/useFundFilter';
import useFundsDefinitions from 'features/proposal/hooks/useFundsDefinitions';
import { RiskLevel } from 'features/proposal/types';
import { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Modal, ModalProps, View } from 'react-native';
import { FundOption } from 'types/quotation';
import SelectFundsRow from '../RpqQuestionForm/SelectFundRow';
import RiskLineButtons from './RiskLineButtons';

const BodyContainer = styled.ScrollView(({ theme: { colors, space } }) => ({
  backgroundColor: colors.background,
  position: 'relative',
  paddingHorizontal: space[4],
  // Workaround for the issue with scroll view in modal. Refer to: https://github.com/facebook/react-native/issues/48822
  height: 100,
}));

const Footer = styled.View(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  padding: space[4],
  paddingBottom: space[12],
  borderTopColor: colors.palette.fwdGrey[100],
  borderTopWidth: 1,
}));

export type AddFundModalProps = {
  selectedFunds?: FundOption[];
  maxFund?: number | null;
  ownerRiskLevel?: RiskLevel;
  visible: boolean;
  onClose: () => void;
  onAdd: (funds: FundOption[]) => void;
} & ModalProps;

export default function AddFundsModal({
  selectedFunds,
  maxFund,
  ownerRiskLevel,
  visible,
  onClose,
  onAdd,
  ...props
}: AddFundModalProps) {
  const fundsDefinitions = useFundsDefinitions();
  const MAX_FUNDS = maxFund ?? fundsDefinitions.length;
  const { t } = useTranslation(['proposal', 'common']);
  const { space, colors, sizes } = useTheme();

  const {
    fundFilterType,
    currentFilterType,
    setCurrentFilterType,
    localSelectedFunds,
    setLocalSelectedFunds,
    sortedFunds,
    isNumOfFundsExceedLimit,
    ALL_FILTER,
    fundFilterList,
  } = useFundFilter(ownerRiskLevel, selectedFunds, MAX_FUNDS);

  const isIncludeFund = (targetFund: FundOption) =>
    localSelectedFunds.some(fund => fund.fid === targetFund.fid);

  const handleCheckBoxOnPress = (fund: FundOption) => {
    if (!isNumOfFundsExceedLimit && !isIncludeFund(fund)) {
      setLocalSelectedFunds(prev => [...prev, fund]);
      return;
    }
    const filtered = localSelectedFunds.reduce((sum, current) => {
      if (current.fid !== fund.fid) {
        sum.push(current);
      }
      return sum;
    }, [] as FundOption[]);
    setLocalSelectedFunds(filtered);
  };

  const onPress = () => {
    onAdd(localSelectedFunds);
    onClose();
  };

  useEffect(() => {
    setLocalSelectedFunds(selectedFunds ?? []);
  }, [selectedFunds, visible]);

  return (
    <Modal visible={visible} {...props}>
      <Header
        modal={true}
        variant="white"
        title={'Add Funds'}
        onPressBackButton={onClose}
      />

      <RiskLineButtons
        riskLevel={ownerRiskLevel}
        currentFilterType={currentFilterType}
        onPress={setCurrentFilterType}
        fundFilterList={fundFilterList}
      />
      <BodyContainer>
        <Typography.H7
          style={{
            fontSize: sizes[4],
            fontWeight: '400',
          }}>
          {t('proposal:funds.selectFunds')}
        </Typography.H7>
        <View style={{ marginTop: space[5] }} />
        {sortedFunds.map((r, index) => {
          if (
            currentFilterType !== ALL_FILTER &&
            r[fundFilterType] !== currentFilterType
          ) {
            return;
          }

          const rowStyle: {
            borderBottomColor?: string;
            borderBottomWidth?: number;
          } = {
            borderBottomColor: colors.palette.fwdGrey[100],
          };
          if (index !== fundsDefinitions.length - 1) {
            rowStyle.borderBottomWidth = 1;
          }

          return (
            <SelectFundsRow
              key={`selectFundsRow.${r.fid}`}
              fundsDefinition={r}
              isSelected={isIncludeFund(r)}
              isDisabled={!isIncludeFund(r) && isNumOfFundsExceedLimit}
              style={rowStyle}
              onPress={handleCheckBoxOnPress}
            />
          );
        })}
      </BodyContainer>
      <Footer>
        <Button
          disabled={!localSelectedFunds.length}
          text={`${t('common:add')} (${
            localSelectedFunds.length
          }/${MAX_FUNDS})`}
          onPress={onPress}
        />
      </Footer>
    </Modal>
  );
}
