import { useTheme } from '@emotion/react';
import { DropdownPanel, Icon, Row, Typography } from 'cube-ui-components';
import { useBasePlanCurrency } from 'features/proposal/hooks/useBasePlanInfo';
import useFields, { FieldsType } from 'features/proposal/hooks/useFields';
import { OptionConfig } from 'features/proposal/types';
import getSelectedItem from 'features/proposal/untils/getSelectedItem';
import uniqBy from 'lodash/uniqBy';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StyleSheet, ViewProps, ViewStyle } from 'react-native';
import { formatCurrencyWithMask } from 'utils';
import { renderLabelByLanguage } from 'utils/helper/translation';
import { CurrencyText } from '../BasePlanCalculationBox/CalculationUnderlineInput';
import RoundOutlineTextField from '../RoundOutlineText';
import BasePlanTextFieldContainer from './BasePlanTextFieldContainer';

export type BasePlanDropdownProps = {
  suffix?: string;
  value?: string | number;
  disabled?: boolean;
  error?: string;
  onChange?: (value?: string) => void;
  onBlur?: () => void;
  fieldName: keyof FieldsType;
  title?: string;
  hasCurrency?: boolean;
  isLoading?: boolean;
  options?: OptionConfig<string>[] | undefined;
  titleDirection?: 'top' | 'left';
  size?: 'default' | 'small';
  style?: ViewStyle;
} & ViewProps;

export default function BasePlanDropdown({
  suffix,
  disabled,
  error,
  value,
  onChange,
  onBlur,
  fieldName,
  title,
  hasCurrency,
  isLoading,
  style,
  options: optionsProps,
  size = 'default',
  titleDirection = 'left',
  ...props
}: BasePlanDropdownProps) {
  const { colors, space } = useTheme();
  const { t } = useTranslation(['proposal', 'common']);
  const _options = useFields([fieldName]).get(fieldName)?.options as
    | OptionConfig<string>[]
    | undefined;

  const options = uniqBy(optionsProps || _options, 'value');
  const dataType = useFields([fieldName]).get(fieldName)?.dataType;
  const configLabel = useFields([fieldName]).get(fieldName)?.label;
  const inputLabel = title ?? renderLabelByLanguage(configLabel);
  const templateOptions = useFields([fieldName]).get(fieldName)?.options;
  const [modalVisible, setModalVisible] = useState(false);
  const selectedItem = getSelectedItem(options, value);

  const showModal = () => {
    if (isLoading || disabled) return;
    setModalVisible(true);
  };

  const currency = useBasePlanCurrency();

  if (disabled || (templateOptions?.length === 1 && !!selectedItem?.label)) {
    let displayValue;
    if (dataType === 'options') {
      const fallback = value ? `${value} ${suffix ?? ''}` : '--';
      displayValue = selectedItem
        ? renderLabelByLanguage(selectedItem?.label)
        : fallback;
    } else {
      displayValue = value
        ? `${
            hasCurrency ? formatCurrencyWithMask(value as number, 2) : value
          } ${value && suffix ? suffix : ''}`
        : '--';
    }

    return (
      <BasePlanTextFieldContainer title={inputLabel}>
        <Row marginLeft={space[4]}>
          {hasCurrency && value && <CurrencyText>{currency}</CurrencyText>}
          <Typography.LargeBody fontWeight="bold">
            {displayValue}
          </Typography.LargeBody>
        </Row>
      </BasePlanTextFieldContainer>
    );
  }

  return (
    <>
      <BasePlanTextFieldContainer
        titleDirection={titleDirection}
        title={title}
        style={style}>
        <RoundOutlineTextField
          size={size}
          isActive={!disabled}
          error={error}
          value={renderLabelByLanguage(selectedItem?.label)}
          onPress={showModal}
          inputProps={{
            multiline: true,
            placeholder: t('proposal:basicPlan.dropdown.placeholder'),
          }}
          right={
            <Icon.Dropdown
              size={20}
              fill={disabled ? colors.palette.fwdDarkGreen[20] : colors.primary}
            />
          }
          disabled={disabled}
        />
      </BasePlanTextFieldContainer>

      <DropdownPanel<OptionConfig<string>, string>
        title={title}
        actionLabel={t('common:done')}
        visible={modalVisible}
        data={options}
        selectedItem={selectedItem as OptionConfig<string>}
        getItemLabel={item => `${renderLabelByLanguage(item.label)}`}
        getItemValue={item => item.value}
        onDismiss={() => setModalVisible(false)}
        onDone={item => {
          onChange?.(item.value);
          onBlur?.();
        }}
        labelStyle={styles.label}
      />
    </>
  );
}

const styles = StyleSheet.create({
  label: {
    flex: 1,
  },
});
