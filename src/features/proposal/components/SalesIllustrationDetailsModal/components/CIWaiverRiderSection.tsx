import { useTheme } from '@emotion/react';
import { Box, Column, Icon, Row, Typography } from 'cube-ui-components';
import useBasePlanInfo from 'features/proposal/hooks/useBasePlanInfo';
import { RiderFormValues } from 'features/proposal/types';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { RiderPlanFieldDefinition } from 'types/quotation';
import { formatCurrencyWithMask } from 'utils';
import { renderLabelByLanguage } from 'utils/helper/translation';
import { CommonDataItem } from './CommonDataItem';
import useCIWaiverRiderFields from '../../Rider/CIWaiverRider/useCIWaiverRider';

type CIWaiverRiderSectionProps = {
  rider: RiderFormValues & {
    definition?: RiderPlanFieldDefinition;
  };
  id: number;
};

export default function CIWaiverRiderSection({
  rider,
  id,
}: CIWaiverRiderSectionProps) {
  const { space, colors } = useTheme();

  const { t } = useTranslation(['common', 'proposal']);

  const basePlanInfo = useBasePlanInfo();

  const {
    labelOfAdbRider,
    labelOfHajiRider,
    labelOfTermRider,
    labelOfMedicalRider,
    labelOfAddRider,
    labelOfMedicalPlusRider,
  } = useCIWaiverRiderFields(rider.definition);

  const coverageRiders = [
    `${renderLabelByLanguage(basePlanInfo?.productName)} (${t(
      'proposal:basicPlan.basePlan',
    )})`,
    rider?.adbWaiver && renderLabelByLanguage(labelOfAdbRider),
    rider?.hajjWaiver && renderLabelByLanguage(labelOfHajiRider),
    rider?.termWaiver && renderLabelByLanguage(labelOfTermRider),
    rider?.medicalWaiver && renderLabelByLanguage(labelOfMedicalRider),
    rider?.addbWaiver && renderLabelByLanguage(labelOfAddRider),
    rider?.medicalPlusWaiver && renderLabelByLanguage(labelOfMedicalPlusRider),
  ].filter(Boolean);

  return (
    <>
      <Row px={space[4]} pt={space[4]}>
        <Column mr={space[4]}>
          <Typography.LargeBody fontWeight="bold">
            {`${id + 1}.`}
          </Typography.LargeBody>
        </Column>

        <Column flex={1}>
          <Typography.LargeBody fontWeight="bold">
            {renderLabelByLanguage(rider.definition?.label)}
          </Typography.LargeBody>

          <Row my={space[4]} mr={space[10]}>
            <CommonDataItem
              label={t('proposal:rider.sumAssured')}
              value={
                rider.sumAssured
                  ? `${basePlanInfo?.currency} ${formatCurrencyWithMask(
                      rider.sumAssured,
                      2,
                    )}`
                  : '--'
              }
            />
            <CommonDataItem
              label={t('proposal:rider.policyTerm')}
              value={
                rider.policyTerm
                  ? `${rider.policyTerm} ${t('common:years')}`
                  : '--'
              }
            />
            <CommonDataItem
              label={t('proposal:rider.premiumTerm')}
              value={
                rider.premiumTerm
                  ? `${rider.premiumTerm} ${t('common:years')}`
                  : '--'
              }
            />
            <CommonDataItem
              label={t('proposal:rider.totalPrem')}
              value={
                rider.totalPrem
                  ? `${basePlanInfo?.currency} ${formatCurrencyWithMask(
                      rider.totalPrem,
                      2,
                    )}`
                  : '--'
              }
            />
          </Row>
        </Column>
      </Row>

      {Boolean(coverageRiders.length) && (
        <Box px={space[11]}>
          <Typography.Label
            fontWeight="bold"
            color={colors.palette.fwdDarkGreen[50]}>
            {t('proposal:rider.coverageSelection')}
          </Typography.Label>

          <Row
            alignItems="center"
            flexDirection="row"
            flexWrap="wrap"
            mt={space[2]}
            mb={space[4]}>
            {coverageRiders.map(
              item =>
                item && (
                  <Row alignItems="center" flexBasis="33%" key={item ?? ''}>
                    <Box mr={1}>
                      <Icon.Tick />
                    </Box>

                    <Typography.Label
                      style={{ flex: 1 }}
                      color={colors.palette.fwdDarkGreen[100]}>
                      {item}
                    </Typography.Label>
                  </Row>
                ),
            )}
          </Row>
        </Box>
      )}
    </>
  );
}
