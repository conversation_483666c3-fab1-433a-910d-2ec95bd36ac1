import { useTheme } from '@emotion/react';
import { Body, Box, Row } from 'cube-ui-components';

import Input from 'components/Input';
import { differenceInCalendarYears } from 'date-fns';
import setYear from 'date-fns/setYear';
import useQuotationInsureds from 'features/proposal/hooks/useQuotationInsureds';
import React from 'react';
import { useWatch } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { BasePlanFormProps } from '.';
import BasePlanDatePicker from '../../BasePlanBox/BasePlanDatePicker';

import BasePlanReadonlyField from '../../BasePlanBox/BasePlanReadonlyField';
import { BasePlanControlContainer } from '../../StickyHeader/StickyHeader.tablet';

const BackdateForm = ({ control, minDate, maxDate }: BasePlanFormProps) => {
  const { space, colors } = useTheme();

  const basePlanValues = useWatch({ control })?.basePlan;

  const { mainInsured } = useQuotationInsureds();

  const { t } = useTranslation(['proposal']);

  const insuredLastBirthday =
    mainInsured?.dob &&
    dateFormatUtil(
      setYear(new Date(mainInsured?.dob), new Date().getFullYear() - 1),
    );

  const backdateAge =
    basePlanValues?.backDate &&
    mainInsured?.dob &&
    differenceInCalendarYears(
      new Date(basePlanValues?.backDate),
      new Date(mainInsured?.dob),
    );

  return (
    <Box
      backgroundColor={colors.background}
      borderTopLeftRadius={30}
      padding={space[5]}>
      <Body>{t('proposal:basicPlan.backdate.description')}</Body>
      <Row paddingY={space[5]} flexWrap="wrap" gap={space[5]}>
        <BasePlanControlContainer isMultipleInsureds={false}>
          <Input
            as={BasePlanDatePicker}
            control={control}
            name="basePlan.backDate"
            label={t('proposal:basicPlan.backToDate')}
            minDate={minDate ? new Date(minDate) : undefined}
            maxDate={maxDate ? new Date(maxDate) : undefined}
          />
        </BasePlanControlContainer>

        <BasePlanControlContainer isMultipleInsureds={false} disabled={true}>
          <BasePlanReadonlyField
            title={t('proposal:basicPlan.backdateAge')}
            value={backdateAge}
          />
        </BasePlanControlContainer>

        <BasePlanControlContainer isMultipleInsureds={false} disabled={true}>
          <BasePlanReadonlyField
            title={t('proposal:basicPlan.lastBirthday')}
            value={insuredLastBirthday}
          />
        </BasePlanControlContainer>

        <BasePlanControlContainer isMultipleInsureds={false} disabled={true}>
          <BasePlanReadonlyField
            title={t('proposal:basicPlan.currentAge')}
            value={mainInsured?.age}
          />
        </BasePlanControlContainer>
      </Row>
    </Box>
  );
};

export default BackdateForm;
