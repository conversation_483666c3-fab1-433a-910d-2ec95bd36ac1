import { SvgIconProps } from 'cube-ui-components';
import * as React from 'react';
import Svg, { Path } from 'react-native-svg';

function RiskArrowSVG(props: SvgIconProps) {
  return (
    <Svg
      width={props.width || 24}
      height={props.height || 24}
      viewBox="0 0 24 11"
      fill="none"
      // xmlns="http://www.w3.org/2000/svg"
      {...props}>
      <Path d="M9.36602 14.5C8.98112 15.1667 8.01887 15.1667 7.63397 14.5L0.272758 1.75C-0.112141 1.08334 0.368982 0.25 1.13878 0.25L15.8612 0.250002C16.631 0.250002 17.1121 1.08333 16.7272 1.75L9.36602 14.5Z" fill="#183028" />
    </Svg>
  );
}

export default RiskArrowSVG;
