import { useTheme } from '@emotion/react';
import Skeleton from 'components/Skeleton';
import { Box, Column, Row, Typography } from 'cube-ui-components';
import TabButton from 'features/affiliates/components/TabButton';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';

export default function TopicsInterestScreenLoadingUI() {
  const { colors, space, sizes, borderRadius } = useTheme();
  const { t } = useTranslation(['affiliates']);
  const [isDayTabActive, setIsDayTabActive] = useState<boolean>(true);
  const [isYearTabActive, setIsYearTabActive] = useState<boolean>(false);

  return (
    <Box
      padding={space[5]}
      borderRadius={space[4]}
      backgroundColor={colors.background}
      gap={space[3]}>
      {/* Data analysis title */}
      <Row marginBottom={space[1]}>
        <Typography.H7 fontWeight="bold">
          {t('affiliates:affiliates.topicsYourLeadsInterestedTo')}
        </Typography.H7>
      </Row>
      {/* Tabs */}
      <Row gap={space[1]} marginBottom={space[4]}>
        <TabButton
          isActive={isDayTabActive}
          label={t('affiliates:affiliates.past30Days')}
        />
        <TabButton
          isActive={isYearTabActive}
          label={t('affiliates:affiliates.thisYear')}
        />
      </Row>
      {/* Bar Chart */}
      <Row height={sizes[32]} gap={space[3]} justifyContent="center">
        {Array(7)
          .fill(true)
          .map((_, i) => (
            <GreyLoadingBarChart key={i} />
          ))}
      </Row>

      {/* bar chart title */}
      <View style={{ alignItems: 'center', marginBottom: space[4] }}>
        <Typography.SmallLabel>
          {t('affiliates:affiliates.noOfLeadGenerated')}
        </Typography.SmallLabel>
      </View>

      {/* shown data */}
      <Box
        gap={space[6]}
        marginTop={space[4]}
        flexDirection="row"
        justifyContent="space-between">
        <Row>
          <Typography.SmallLabel style={{ width: sizes[47] }}>
            {t('affiliates:affiliates.topic')}
          </Typography.SmallLabel>
        </Row>

        <Row gap={space[2]}>
          <Typography.SmallLabel style={{ width: sizes[16] }}>
            {t('affiliates:affiliates.leadGenerated')}
          </Typography.SmallLabel>
          <Typography.SmallLabel
            color={colors.palette.fwdDarkGreen[100]}
            style={{ width: sizes[16] }}>
            {t('affiliates:affiliates.noOfClick')}
          </Typography.SmallLabel>
        </Row>
      </Box>

      {Array(4)
        .fill(true)
        .map((_, i) => (
          <GreyLoadingTopicsContent key={i} />
        ))}
    </Box>
  );
}

const GreyLoadingBarChart = () => {
  const { space, sizes, borderRadius } = useTheme();

  return (
    <Column justifyContent="flex-end" gap={space[2]} height={sizes[32]}>
      <Skeleton
        width={sizes[7]}
        height={'60%'}
        radius={borderRadius['x-small']}
      />

      <Typography.ExtraSmallLabel style={{ textAlign: 'center' }}>
        -
      </Typography.ExtraSmallLabel>
    </Column>
  );
};

const GreyLoadingTopicsContent = () => {
  const { space, sizes, borderRadius, colors } = useTheme();

  return (
    <Box
      flexDirection="row"
      marginBottom={space[4]}
      justifyContent="space-between"
      gap={space[6]}>
      <Row style={{ width: sizes[47] }} gap={space[2]}>
        <View style={{ justifyContent: 'center' }}>
          <Skeleton width={sizes[3]} height={sizes[3]} radius={1} />
        </View>
        <Skeleton width={'90%'} height={sizes[4]} radius={2} />
      </Row>

      <Row gap={space[2]}>
        <Box width={sizes[16]}>
          <Row
            width={sizes[8]}
            height={sizes[4]}
            borderRadius={2}
            backgroundColor={colors.palette.fwdGrey[50]}></Row>
        </Box>

        <Box width={sizes[16]}>
          <Row
            width={sizes[8]}
            height={sizes[4]}
            borderRadius={2}
            backgroundColor={colors.palette.fwdGrey[50]}></Row>
        </Box>
      </Row>
    </Box>
  );
};
