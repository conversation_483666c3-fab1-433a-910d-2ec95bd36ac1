export default {
  app: 'Application',
  amount: 'Amount',
  yourProgress: 'Your progress',
  next: 'Next',
  ok: 'OK',
  yes: 'Yes',
  no: 'No',
  back: 'Back',
  fna: 'CFF',
  close: ' Close',
  more: ' ...more',
  done: 'Done',
  backToHome: 'Back to home',
  reminders: 'Reminders: ',
  home: 'Home',
  exitFna: 'Exit CFF',
  exitFnaDesc: 'Do you want to save before exit the CFF?',
  dontSave: "Don't save",
  cancel: 'Cancel',
  agree: 'Agree',
  select: 'Select',
  save: 'Save',
  saved: 'Saved',
  savedProfile: 'Saved to the profile successfully',
  savedLead: 'Saved to new lead profile',
  fullScreen: 'Full version',
  existingCustomer: 'Is it an existing lead?',
  existingCustomerDesc: 'Click “Yes” to retrieve data',
  years: '{{year}} years',
  year: '{{year}} year',
  shortYearsOld: 'y.o.',
  yearsOld: 'years old',
  current: 'current',
  target: 'target',
  php: 'PHP',
  name: 'Name',
  gender: 'Gender',
  lifeStage: 'Life stage',
  pickTitle: 'Pick your life stage',
  single: 'Single',
  singleWithDepend: 'Single with dependents',
  couple: 'Married',
  coupleWithKids: 'Married with dependents',
  emptyNester: 'Empty nesters',
  retired: 'Retired',
  explore: 'Explore your needs',
  saving: 'Saving goals',
  retirement: 'Retirement',
  invest: 'Investment',
  protect: 'Protection goals',
  protectIncome: 'Income protection',
  healthProtection: 'Health protection',
  savingNeeds: 'Saving needs',
  coverNeeds: 'Cover needs',
  start: 'Start',
  aboutMe: 'About me',
  section: 'Section',
  worries: 'Worries',
  breakdown: 'Breakdown: ',
  goals: 'Goals',
  hi: 'Hi {{userName}}, tell us about you',
  consents: 'Consents',
  importantNotice: 'Important notice',
  'amount.label': 'Amount (RM)',
  'amount.label.optional': 'Amount (RM) (Optional)',
  'lifeStage.detail.1': 'Pick your life stage',
  'lifeStage.detail.question.dob': 'Your birthdate',
  'lifeStage.detail.question.ageToRetire': 'Age to retire',
  'lifeStage.detail.question.numberOfKids': 'Number of kids',
  'lifeStage.detail.question.dependents': 'No. of dependents',
  'lifeStage.detail.question.dependents.phone': 'Your dependants information',
  'lifeStage.detail.question.dependents.total': 'Total ({{total}})',
  'lifeStage.detail.question.dependents.modalTitle':
    'Your dependents information',
  'lifeStage.detail.question.dependents.title': 'Dependent',
  'lifeStage.detail.question.dependents.add': 'Add dependent',
  'lifeStage.detail.question.dependentRelationship': 'Relationship',
  'lifeStage.detail.question.dependentRelationship.KID': 'Kid',
  'lifeStage.detail.question.dependentRelationship.SPOUSE': 'Spouse',
  'lifeStage.detail.question.dependentRelationship.PARENT': 'Parent',
  'lifeStage.detail.question.dependentRelationship.SIBLING': 'Sibling',
  'lifeStage.detail.question.kidAge': "Kid's age",
  'lifeStage.detail.question.kidAge.1': "First kid's age",
  'lifeStage.detail.question.kidAge.2': "Second kid's age",
  'lifeStage.detail.question.kidAge.3': "Third kid's age",
  'lifeStage.detail.question.kidAge.4': "Fourth kid's age",
  'lifeStage.detail.question.kidAge.5': "Fifth kid's age",
  'lifeStage.detail.question.havePartner': 'Have partner to take care?',
  'lifeStage.detail.aboutMe.havePartner': 'Have partner to take care',
  'lifeStage.detail.question.haveKids': 'Plan to have kids',
  'lifeStage.detail.question.coupleWithKids': 'Have {{numberOfKids}} kids',
  'lifeStage.detail.question.coupleWithKid': 'Have {{numberOfKids}} kid',
  'lifeStage.detail.question.partnerAge': "Partner's age",
  'lifeStage.detail.question.planToTravel': 'Plan to go traveling?',
  'lifeStage.detail.aboutMe.planToTravel': 'Plan to go traveling',
  'lifeStage.detail.question.income': 'Personal gross annual income',
  'lifeStage.detail.question.income.placeholder': 'Select',
  'lifeStage.detail.question.financialBudget':
    'Regular gross income set aside for your financial goals',
  'lifeStage.detail.question.financialBudget.part1': 'Regular gross income',
  'lifeStage.detail.question.financialBudget.part2':
    'That set aside for your financial goal',
  'lifeStage.detail.question.lifeStage': 'Your current life stage',
  'lifeStage.detail.concerns': 'Prioritise your goals',
  'lifeStage.detail.concerns.desc':
    '1 is the most important goal, {{maxSelection}} is the less important goal.',
  'lifeStage.concern.INCOME_PROTECTION.tablet': 'Income\nProtection',
  'lifeStage.concern.INCOME_PROTECTION.mobile': 'Income Protection',
  'lifeStage.concern.HEALTH_PROTECTION.tablet':
    'Medical /\nHealthcare Planning',
  'lifeStage.concern.HEALTH_PROTECTION.mobile':
    'Medical / Healthcare\nPlanning',
  'lifeStage.concern.HEALTH_PROTECTION.mobile.2':
    'Medical / Healthcare Planning',
  'lifeStage.concern.LOAN_PROTECTION.tablet': 'Debt\nCancellation',
  'lifeStage.concern.LOAN_PROTECTION.mobile': 'Debt Cancellation',
  'lifeStage.concern.SAVINGS.tablet': 'Savings',
  'lifeStage.concern.SAVINGS.mobile': 'Savings',
  'lifeStage.concern.INVESTMENT.tablet': 'Investments',
  'lifeStage.concern.INVESTMENT.mobile': 'Investments',
  'lifeStage.concern.RETIREMENT.tablet': 'Retirement\nIncome',
  'lifeStage.concern.RETIREMENT.mobile': 'Retirement Income',
  'lifeStage.concern.LEGACY_PLANNING.tablet': 'Legacy Planning',
  'lifeStage.concern.LEGACY_PLANNING.mobile': 'Legacy Planning',
  'lifeStage.concern.EDUCATION.tablet': "Child's\nEducation",
  'lifeStage.concern.EDUCATION.mobile': "Child's Education",
  'lifeStage.concern.priority.0': '1st priority',
  'lifeStage.concern.priority.1': '2nd priority',
  'lifeStage.concern.priority.2': '3rd priority',
  'lifeStage.concern.priority.3': '4th priority',
  'lifeStage.concern.priority.4': '5th priority',
  'lifeStage.concern.priority.5': '6th priority',
  'lifeStage.concern.priority.6': '7th priority',
  'lifeStage.concern.priority.7': '8th priority',
  'lifeStage.detail.concerns.ans.recommend': 'Recommended (Typical concern)',
  'review.age': 'Age',
  'review.dateOfBirth': 'Date of birth',
  'lifeStage.customer.existing': 'Search for existing FWD lead',
  'lifeStage.customer.existing.searchDesc': 'Lead name/ phone number/ email',
  'lifeStage.customer.existing.search': 'Search',
  'lifeStage.customer.existing.confirm': 'Confirm',
  'lifeStage.customer.existing.searchResults': 'Search results ({{count}})',
  'lifeStage.customer.existing.empty':
    'No pre-existing lead available. Please try another search.',
  'lifeStage.customer.existing.verification.title': 'Is it the same lead?',
  'lifeStage.customer.existing.verification.desc':
    'We found someone having these information in your record.',
  'lifeStage.customer.existing.error':
    'No pre-existing lead available. Please try another search.',
  'lifeStage.customer.existing.id': 'ID number',
  'lifeStage.recommend.plans': 'View recommend plans',
  'lifeStage.recommend.skip': 'Skip',
  'lifeStage.recommend.productRecommendation': 'Product recommendation',
  'lifeStage.view.plans': 'View plans',
  'lifeStage.confirm': 'Confirm',
  'validation.invalidFormat': 'Invalid format',
  'validation.requiredInput': 'Required field',
  'validation.requiredField': 'This field is required',
  'validation.maxLength16': 'Maximum length is 16',
  'validation.maxLength50': 'Maximum length is 50',
  'validation.maxLength60': 'Maximum length is 60',
  roundingRule: 'Rounding rule',
  'roundingRule.desc': 'Number here is rounding to the nearest thousand.',
  inflationRate: 'Inflation rate',
  'inflationRate.desc': 'Inflation rate is excluded from the computation.',
  'goals.totalNeedsTarget': 'Total needs target: ',
  'goals.currentAssets': 'Current assets: ',
  'goals.currentCoverage': 'Current coverage: ',
  'goals.gapToTarget': 'Gap to target amount: ',
  'savingsGoals.title': 'Savings Goal',
  'savingsGoals.totalCurrentAssets': 'Total current assets',
  'savingsGoals.retirement': 'Retirement',
  'savingsGoals.retirement.title':
    'Calculate how much you need when you retire',
  'savingsGoals.retirement.ageToRetire': 'Age to retire',
  'savingsGoals.retirement.numberOfYearsToRetirement':
    'Number of years to retirement',
  'savingsGoals.retirement.annualIncome':
    'Annual income needed during retirement',
  'savingsGoals.retirement.yearsToReplace':
    'Years needed for this annual income to be replaced',
  'savingsGoals.retirement.existingRetirementIncome':
    'Existing retirement income',
  'savingsGoals.retirement.iHaveOtherSources':
    'I have other sources of income that I expect to rely on during retirement.',
  'savingsGoals.retirement.otherSourceOfIncome':
    'Other sources of income that I expect to rely on during retirement',
  'savingsGoals.retirement.monthlyAllowance':
    'Target amount to receive as monthly allowance',
  'savingsGoals.retirement.yearsToReceive':
    'Years to receive retirement allowance',
  'savingsGoals.retirement.anyProgramStarted':
    'Already started any retirement program?',
  'savingsGoals.retirement.currentProgram':
    'How much is your current retirement program?',
  'savingsGoals.retirement.totalFunds':
    'Estimated funds you need for retirement',
  'savingsGoals.retirement.totalNeeds': 'Total needs',
  'savingsGoals.retirement.totalGap': 'Total gap to target amount',
  'savingsGoals.retirement.untilRetirement': 'until retirement',
  'savingsGoals.retirement.age': 'Age (y.o.)',
  'savingsGoals.retirement.years': 'Years',
  'savingsGoals.retirement.annualIncome.calculator.title':
    'Expected post-retirement calculator',
  'savingsGoals.retirement.annualIncome.calculator.expense': 'Expense mode',
  'savingsGoals.retirement.annualIncome.calculator.monthly': 'Monthly',
  'savingsGoals.retirement.annualIncome.calculator.yearly': 'Yearly',
  'savingsGoals.retirement.annualIncome.calculator.regularCommitment': '',
  'savingsGoals.retirement.annualIncome.calculator.housing': 'Housing',
  'savingsGoals.retirement.annualIncome.calculator.transportation':
    'Transportation',
  'savingsGoals.retirement.annualIncome.calculator.travel': 'Travel',
  'savingsGoals.retirement.annualIncome.calculator.food': 'Food',
  'savingsGoals.retirement.annualIncome.calculator.healthcare': 'Healthcare',
  'savingsGoals.retirement.annualIncome.calculator.other': 'Recreation/ other',
  'savingsGoals.retirement.annualIncome.calculator.inflationRate':
    'Inflation rate (%)',
  'savingsGoals.retirement.annualIncome.calculator.inflationRate.label':
    'Inflation rate (%)',
  'savingsGoals.retirement.annualIncome.calculator.investmentRate':
    'Investment rate of return during retirement (%)',
  'savingsGoals.retirement.annualIncome.calculator.investmentRate.label':
    'Investment rate (%)',
  'savingsGoals.retirement.annualIncome.calculator.totalExpense':
    'Future value of yearly expenses',
  'savingsGoals.retirement.annualIncome.calculator.tooltip.title':
    'Future value of yearly expenses formula',
  'savingsGoals.retirement.annualIncome.calculator.tooltip.content':
    '[Yearly expense during retirement*(1+inflation rate(%)] ^ No of Years to Retirement',
  'savingsGoals.education': 'Child education',
  'savingsGoals.education.title':
    'Calculate how much you need for kid’s education',
  'savingsGoals.education.kid': 'Kid',
  'savingsGoals.education.kids': 'Kids',
  'savingsGoals.education.age': 'Age',
  'savingsGoals.education.yourKidAge': "Your kid's age",
  'savingsGoals.education.numberOfKids': 'Number of kid(s) you have',
  'savingsGoals.education.kidInformation': "Kid's information",
  'savingsGoals.education.yourKidName': "Your kid's name",
  'savingsGoals.education.kidName': "Kid's name",
  'savingsGoals.education.gender': 'Gender',
  'savingsGoals.education.kidsInformation': "{{label}}'s information",
  'savingsGoals.education.yearBeforeCollege': '*{{age}} year before College',
  'savingsGoals.education.yearsBeforeCollege': '*{{age}} years before College',
  'savingsGoals.education.yourChildName': `Your child’s name`,
  'savingsGoals.education.yourChildBirthDate': `Your child’s birthdate`,
  'savingsGoals.education.dateOfBirth': `Date of birth`,
  'savingsGoals.education.calculator': 'Calculator',
  'savingsGoals.education.tuitionFund': 'Tuition fund (RM)',
  'savingsGoals.education.yearsToCollege':
    'Number of years to tertiary education',
  'savingsGoals.education.totalTuitionFundNeeded':
    'Total tuition fund of your preferred school/college/university',
  'savingsGoals.education.totalExpense': 'Total expense',
  'savingsGoals.education.haveYouStartedSavingForYourChild': `Have you started saving for your child's college tuition fee?`,
  'savingsGoals.education.howMuchHaveYouSaved': `Existing children education fund`,
  'savingsGoals.education.totalFunds':
    "Total funds you need for kid's education",
  'savingsGoals.education.untilUniversity': 'until university',
  'savingsGoals.education.duringUniversity': 'during university',
  'savingsGoals.education.universityExpensesCalculator':
    'University expenses calculator',
  'savingsGoals.education.universityOfChoice': 'University of choice',
  'savingsGoals.education.type': 'Type',
  'savingsGoals.education.collegeType': 'College type',
  'savingsGoals.education.yearsInCollege': 'Years in college',
  'savingsGoals.education.yearsForCollege': 'Years for college',
  'savingsGoals.education.years': 'Years',
  'savingsGoals.education.expense': 'Expense:',
  'savingsGoals.education.universityLocation': 'University location',
  'savingsGoals.education.universityLocation.title':
    'Choose an educational plan',
  'savingsGoals.education.disclaimer.expanded': `**Disclaimer**: The education cost estimates provided below are for general reference only. They are based on the average annual tuition fees of leading universities and private institutions, along with estimated living expenses for the respective countries. These living expense estimates may not fully reflect individual needs and circumstances. Accuracy of this information cannot be guaranteed as of the date it is provided, and it may change in the future.\n&nbsp;\nSource: EducationData.org, Wikipedia.org [Close]()`,
  'savingsGoals.education.countryName': 'Country name',
  'savingsGoals.education.avgTuitionFee': 'Average Tuition Fees per year (RM)',
  'savingsGoals.education.avgLivingExpenses':
    'Average Living Expenses per year (RM)',
  'savingsGoals.education.disclaimer.collapsed':
    '**Disclaimer**: The education cost estimates provided below are for general reference only. They are based on the average annual tuition fees of leading universities and private institutions, along with estimated living expenses for the respective countries. [...More]()',
  'savingsGoals.education.disclaimer.collapsed.phone':
    '**Disclaimer**: The education cost estimates provided below are for general reference only. [...More]()',
  'savingsGoals.education.universityLocation.search': 'Search country',
  'savingsGoals.education.universityLocation.tuitionFee': 'Average tuition fee',
  'savingsGoals.education.universityLocation.livingCosts':
    'Average living costs',
  'savingsGoals.education.country': 'Country',
  'savingsGoals.education.tuitionFee': 'Tuition fee',
  'savingsGoals.education.livingCosts': 'Living costs',
  'savingsGoals.education.inflationRate': 'Inflation rate (%)',
  'savingsGoals.education.inflationRate.tooltip':
    'Future value of total expense',
  'savingsGoals.education.inflationRate.tooltip.title':
    'Future value of total expense formula',
  'savingsGoals.education.inflationRate.tooltip.content':
    'Future value of tuition fees = [(Average tuition fee + Average living expense) * (1+inflation rate(%))] ^ number of years to tertiary education * number of years in school',
  'savingsGoals.education.yearsInSchool': 'Number of years attending school',
  'savingsGoals.education.annualTuitionCosts': 'Annual tuition costs',
  'savingsGoals.education.futureValue': '*Future value: ',
  'savingsGoals.education.disclaimer': 'Disclaimer',
  'savingsGoals.education.disclaimerContent1':
    'The estimated education cost provided is for reference and illustration purposes only. There is no guarantee that the information is accurate.',
  'savingsGoals.education.disclaimerContent2':
    'Tuition is based on current tuition fees with an increase of 10% national average for 2017, as approved by the CHED.',
  'savingsGoals.education.disclaimerContent3':
    'Future estimates of tuition fees use the 10% national average for 2017, year-on-year. Calculation does not include other university expenses (books, living and lodging expenses).',
  'savingsGoals.education.universityTuitionFee': 'University tuition fee',
  'savingsGoals.education.search.msg1': 'No results found.',
  'savingsGoals.education.search.msg2': 'Adjust keyword to get better results.',
  'savingsGoals.education.forTheMostUpToDate':
    'For the most up-to-date university tuition fee rates, visit ',
  'savingsGoals.education.edukasyon': 'edukasyon.ph',
  'savingsGoals.education.first': '1st kid',
  'savingsGoals.education.second': '2nd kid',
  'savingsGoals.education.third': '3rd kid',
  'savingsGoals.education.fourth': '4th kid',
  'savingsGoals.education.fifth': '5th kid',
  'savingsGoals.education.sixth': '6th kid',
  'savingsGoals.education.seventh': '7th kid',
  'savingsGoals.education.eighth': '8th kid',
  'savingsGoals.education.ninth': '9th kid',
  'savingsGoals.education.tenth': '10th kid',
  'savingsGoals.investment': 'Investment',
  'savingsGoals.investment.title': 'Calculate how much you need for investment',
  'savingsGoals.investment.whatToSave':
    'What do you want to save for in the next five to ten years?',
  'savingsGoals.investment.homeOwnership': 'Home Ownership',
  'savingsGoals.investment.carOwnership': 'Car Ownership',
  'savingsGoals.investment.businessVenture': 'Business Venture',
  'savingsGoals.investment.travelExpense': 'Travel Expense',
  'savingsGoals.investment.other': 'Other',
  'savingsGoals.investment.saveFor': 'Save for',
  'savingsGoals.investment.yearsToAchieve': 'Years to achieve this goal',
  'savingsGoals.investment.years': 'Years',
  'savingsGoals.investment.targetAmountToAchieve':
    'Target amount to achieve this goal',
  'savingsGoals.investment.startedSaving': 'Started saving for this goal?',
  'savingsGoals.investment.specificTarget': 'Specific target',
  'savingsGoals.investment.yes': 'Yes',
  'savingsGoals.investment.no': 'No',
  'savingsGoals.investment.amountSaving':
    'Amount you have been saving for this goal',
  'savingsGoals.investment.invalid': 'Invalid',
  'savingsGoals.investment.totalFunds':
    'Estimated funds you need for Investment',
  'savingsGoals.investment.totalNeeds': 'Total needs',
  'savingsGoals.investment.totalGap': 'Total gap to target amount',
  'savingsGoals.investment.toReachYouGoal': 'to reach your goal',
  'savingsGoals.savings': 'Savings',
  'savingsGoals.savings.title':
    'Calculate how much you need for your custom goal',
  'savingsGoals.savings.whatYouWantToSave': 'What you want to save for',
  'savingsGoals.savings.customGoal': 'Custom goal',
  'savingsGoals.savings.expectedSavingsNeeded': 'Expected savings needed',
  'savingsGoals.savings.yearsNeeded':
    'Years needed to meet the expected saving',
  'savingsGoals.savings.currentSavings': 'Current savings for this goal',
  'savingsGoals.savings.years': 'Years',
  'savingsGoals.savings.totalNeeds': 'Total needs',
  'savingsGoals.savings.totalGap': 'Total gap to target amount',
  'savingsGoals.savings.totalFunds':
    'Estimated funds you need for your custom goal',
  'savingsGoals.savings.invest': 'invest',
  'savingsGoals.investment.initialAmount':
    'Initial amount you intend to invest',
  'savingsGoals.investment.regularAmount':
    'Regular amount you intend to invest',
  'savingsGoals.investment.investmentDuration': 'Investment duration',
  'savingsGoals.investment.duration': 'Duration',
  'savingsGoals.investment.0year': 'In 0 year',
  'savingsGoals.investment.LessThan5Years': 'Less than 5 years',
  'savingsGoals.investment.5YearsAndAbove': '5 years and above',
  'savingsGoals.investment.ExpectedMonthlyPayout': 'Expected monthly pay-out',
  'savingsGoals.investment.PayOutPeriod': 'Pay-out period (years)',
  'savingsGoals.investment.invest': 'invest',
  'savingsGoals.totalSavingNeeds': 'Total saving needs',
  'protectionGoals.totalCurrentCoverage': 'Total current coverage',
  'protectionGoals.totalCoverNeeds': 'Total cover needs',
  'protectionGoals.legacyPlanning': 'Legacy planning',
  'protectionGoals.legacyPlanning.title':
    'Calculate how much you need to safeguard your legacy',
  'protectionGoals.legacyPlanning.question1': 'Net taxable estate',
  'protectionGoals.legacyPlanning.question2': 'Estimated estate tax liability',
  'protectionGoals.legacyPlanning.question3':
    'How much is your current savings allotted for your estate tax?',
  'protectionGoals.legacyPlanning.totalFunds':
    'Total funds you need for legacy planning',
  'protectionGoals.legacyPlanning.totalNeeds': 'Total needs',
  'protectionGoals.legacyPlanning.totalGap': 'Total gap to target amount',
  'protectionGoals.legacyPlanning.toReachYourGoal': 'to reach your goal',
  'protectionGoals.loan': 'Debt Cancellation',
  'protectionGoals.loan.title':
    'Calculate how much you need to cover your loan obligations',
  'protectionGoals.loan.question1': 'Loan Amount (Principal Amount)',
  'protectionGoals.loan.question2': 'Loan term',
  'protectionGoals.loan.years': 'Years',
  'protectionGoals.loan.totalFunds':
    'Estimated funds you need for cover your loan obligations',
  'protectionGoals.loan.totalNeeds': 'Total needs',
  'protectionGoals.loan.totalGap': 'Total gap to target amount',
  'protectionGoals.loan.toReachYourGoal': 'to reach your goal',
  'protectionGoals.target': 'target',
  'protectionGoals.title': 'Protection goals',
  'protectionGoals.health.title':
    'Calculate how much you need to protect your health',
  'protectionGoals.health.existingHealth': 'Do you have an insurance coverage?',
  'protectionGoals.health.existingHealth.total':
    'How much is your total health insurance coverage?',
  'protectionGoals.health.annualIncome': 'Your annual income',
  'protectionGoals.health.aside':
    'How much total savings have you set aside for health emergencies?',
  'protectionGoals.health.totalFunds':
    'Estimated funds you need to protect health',
  'protectionGoals.health.totalNeeds': 'Total needs',
  'protectionGoals.health.target': 'target',
  'protectionGoals.health.levelOfHospital': 'Level',
  'protectionGoals.health.levelOfHospital.title': 'Level of Hospital',
  'protectionGoals.health.levelOfHospital.modalTitle':
    'Choose between levels of hospital',
  'protectionGoals.health.totalGap': 'Total gap to target amount',
  'protectionGoals.health.levelOfHospital.level1': 'Level 1 (PHP 1,000,000.00)',
  'protectionGoals.health.levelOfHospital.level1.displayLabel': 'Level 1',
  'protectionGoals.health.levelOfHospital.level2': 'Level 2 (PHP 2,000,000.00)',
  'protectionGoals.health.levelOfHospital.level2.displayLabel': 'Level 2',
  'protectionGoals.health.levelOfHospital.level3': 'Level 3 (PHP 5,000,000.00)',
  'protectionGoals.health.levelOfHospital.level3.displayLabel': 'Level 3',
  'protectionGoals.income.title':
    'Calculate how much you need to protect your income',
  'protectionGoals.income.monthlyExpenses':
    'Could you provide an estimate of your monthly expenses?',
  'protectionGoals.income.calculator': 'Calculator',
  'protectionGoals.income.existingPolicies':
    'Any savings or existing life insurance policies?',
  'protectionGoals.income.howMuch':
    'How much is your current saving / existing life coverage?',
  'protectionGoals.income.totalFunds':
    'Estimated funds you need for income protection',
  'protectionGoals.income.totalNeeds': 'Total needs',
  'protectionGoals.income.totalGap': 'Total gap to target amount',
  'protectionGoals.income.calculator.title': 'Family expenses calculator',
  'protectionGoals.income.calculator.expense': 'Monthly expense:',
  'protectionGoals.income.calculator.monthly': 'Monthly',
  'protectionGoals.income.calculator.yearly': 'Yearly',
  'protectionGoals.income.calculator.housing': 'Housing',
  'protectionGoals.income.calculator.transportation': 'Transportation',
  'protectionGoals.income.calculator.travel': 'Travel',
  'protectionGoals.income.calculator.regularCommitment': 'Regular commitment',
  'protectionGoals.income.calculator.food': 'Food',
  'protectionGoals.income.calculator.healthcare': 'Healthcare',
  'protectionGoals.income.calculator.other': 'Recreation/ other',
  'protectionGoals.income.calculator.totalExpense': 'Total expense',
  'protectionGoals.income.amount': 'Amount',
  'protectionGoals.income.toReach': 'to reach your goal',
  'protectionGoals.roundingRule': 'Rounding rule',
  inYears_one: 'In {{count}} year',
  inYears_other: 'In {{count}} years',
  firstName: 'First name',
  middleName: 'Middle name',
  lastName: 'Last name',
  fullName: 'Full name',
  countryCode: 'Code',
  mobileNumber: 'Mobile number',
  email: 'Email',
  saveFna: 'Save CFF in a lead profile',
  below10: 'Below 10%',
  from10to14: '10% to 14%',
  from15to19: '15% to 19%',
  from20to24: '20% to 24%',
  above25: '25% and above',
  mmddyyyy: 'MM/DD/YYYY',
  nextGoal: 'Next goal',
  yo: '{{age}} y.o.',
  currency: 'RM',
  'income.lessThan': '< RM {{to}}',
  'income.moreThan': '> RM {{from}}',
  'income.between': 'RM {{from}} – RM {{to}}',
  'income.<50k': 'Less than PHP 50,000',
  'income.50k-100k': 'PHP 50,000 – less than PHP 100,000',
  'income.100k-150k': 'PHP 100,000 – less than PHP 150,000',
  'income.150k-200k': 'PHP 150,000 - less than PHP 200,000',
  'income.200k-250k': 'PHP 200,000 – less than PHP 250,000',
  'income.>250k': 'PHP 250,000 and above',
  'education.stateUniversity': 'State University (Php 50,000+)',
  'education.stateUniversity.short': 'State University',
  'education.privateUniversity': 'Private University (Php 160,000+)',
  'education.privateUniversity.short': 'Private University',
  'education.premierUniversity': 'Premier University (Php 300,000+)',
  'education.premierUniversity.short': 'Premier University',
  tellUsAboutYou: 'Tell us more about yourself',
  male: 'Male',
  female: 'Female',
  customer: 'Customer',
  expectedIncomeIncrement: 'Expected income increment (%)',
  yourBirthdate: 'Your birthdate',
  currentStage: 'Current',
  futureStage: 'Future',
  calculateNeed: 'Calculate how much you need',
  mandatoryToCompleted: '*Mandatory to be completed',
  startCalculation: 'Start calculation',
  continue: 'Continue',
  editPrioritization: 'Edit profile and prioritisation',
  summary: "{{name}}'s summary",
  targetNeeds: 'Target needs',
  currentSavingsCoverage: 'Current saving/ coverage',
  gapToTarget: 'Gap to target',
  search: 'Search',
  confirm: 'Confirm',
  'searchResult.header.name': 'Lead name',
  'searchResult.header.mobile': 'Mobile',
  'searchResult.header.email': 'Email',
  'disclaimer.title': 'Agreement to start CFF',
  'language.en': 'English',
  'language.my': 'Bahasa',
  'language.ib': 'Bahasa',
  'language.ph': 'Filipino',
  'language.id': 'Bahasa Indonesia',
  totalAssets: 'Total assets',
  totalLiabilities: 'Total liabilities',
  'totalAssets.reminder':
    'Includes the combine value of property, net income, investment, savings, cash and gold, which includes tangible and intangible assets and overall net worth value.',
  'totalLiabilities.reminder':
    'Includes the combine debt and obligations that a persons owes to financial institution or other parties including short-term or long-term liabilities.',
  title: 'Your title',
  'title.placeholder': 'Title',
  'vulnerableCustomer.title': 'Please answer the following questions',
  'vulnerableCustomer.q1':
    '1. Do you require any special assistance or services?',
  'vulnerableCustomer.yes': 'Yes, I do',
  'vulnerableCustomer.no': 'No, I do not',
  'vulnerableCustomer.q1.1': 'Please specify',
  'vulnerableCustomer.q2': '2. Are you currently employed?',
  'vulnerableCustomer.q3':
    '3. Do you have existing Medical and Health Insurance/Takaful (MHIT) coverage provided by your employer?',
  'vulnerableCustomer.01.tooltip': 'Individuals aged 60 years old and above.',
  'vulnerableCustomer.02.tooltip':
    ' - Physical & Mental Health Condition Disabilities\n - Hearing or Visual or Speech impairment\n - Slow Learner tested by qualified medical practitioner\n - Severe Long-term illness',
  'vulnerableCustomer.03.tooltip': '',
  'vulnerableCustomer.04.tooltip':
    ' - Bankruptcy & Unemployment\n - Temporarily Unemployment & Retirees/Pensioner',
  'vulnerableCustomer.05.tooltip':
    'Student / college student / pilot student / university student/ nursing student (> 16 y/o)',
  'customerChoice.title': 'Please answer the following questions',
  'customerChoice.fullAdvice': 'Option 1: Full advice',
  'customerChoice.fullAdvice.instruction': 'You must complete all 6 goals',
  'customerChoice.fullAdvice.description':
    '(I/We wish to disclose all information requested for in this form) refers to full completion of the fact find where the customer wishes to receive the intermediary’s recommendation on product suitability.',
  'customerChoice.simpleAdvice': 'Option 2: Simple advice',
  'customerChoice.simpleAdvice.instruction':
    'You must complete top 3 goals\n(including Medical/Healthcare planning)',
  'customerChoice.simpleAdvice.description':
    '(I/We wish to disclose partially information requested for in this form) refers to partial completion of the fact find where the customer wishes to receive the intermediary’s recommendation on product suitability but understands that any recommendation made is limited by the information provided by the customer.',
  'customerChoice.popularChoice': 'Popular choice',
  'goals.popup.title': 'Goals already planned/to discuss',
  'goals.popup.alreadyPlanned': 'Already planned',
  'goals.popup.toBeDiscussed': 'To be discussed',
  'protectionGoals.annual.income':
    'Annual income needed in the event of death or disability',
  'protectionGoals.years.income':
    'Years needed for this annual income to be replace',
  'protectionGoals.existing.life.insurance':
    'I have existing Life Insurance/ Takaful plan.',
  'protectionGoals.existing.income': 'Existing income protection',
  'protectionGoals.income.years': 'Years',
  'protectionGoals.income.years.error': 'Years must be greater than 0',
  rm: 'RM',
  'protectionGoals.invest': 'invest',
  'protectionGoals.health.amount.needed': 'Amount needed for',
  'protectionGoals.health.existing.protection':
    'I have existing Medical/ Healthcare protection',
  'protectionGoals.health.amount.existing.protection': 'Existing protection',
  'protectionGoals.health.expiry.age': 'Protection expiry age',
  'protectionGoals.health.hospitalisation': 'Hospitalisation',
  'protectionGoals.health.diagnosis.critical.illness':
    'Diagnosis of critical illness',
  'protectionGoals.health.current.protection': 'current protection',
  'protectionGoals.health.future.protection': 'Future protection',
  'protectionGoals.health.age': 'Age',
  existingPolicies: 'Existing policies',
  'existingPolicies.title': 'Your existing policies',
  'existingPolicies.policyPosition': 'Existing policies {{position}}',
  'existingPolicies.form.typeOfPlan': 'Type of plan',
  'existingPolicies.form.insurer': 'Insurer',
  'existingPolicies.form.premium': 'Premium (RM)',
  'existingPolicies.form.freqOfPayment': 'Frequency of payment',
  'existingPolicies.form.maturityDate': 'Maturity date',
  'existingPolicies.form.premiumOfBenefits': 'Premium of benefits',
  'existingPolicies.form.premiumOfBenefits.death': 'Death',
  'existingPolicies.form.premiumOfBenefits.disability': 'Disability',
  'existingPolicies.form.premiumOfBenefits.criticalIllness': 'Critical illness',
  'existingPolicies.form.premiumOfBenefits.others': 'Others',
  'existingPolicies.add': 'Add policy',
  'protectionGoals.debt.outstanding.loan': 'Outstanding loan',
  'protectionGoals.debt.years.loan.settlement':
    'No. of years left for loan settlement',
  'protectionGoals.debt.existing.cancellation': 'Existing debt cancellation',
  'protectionGoals.debt.has.ci':
    'I would like to include critical illness protection',
  'protectionGoals.debt.ci.protection': 'Critical illness protection',
  add: 'Add',
  viewMore: 'View more',
  'dependentSummary.title': 'Your dependents information',
  savedSuccessfully: 'Saved to the profile successfully',
  'lifeStage.concern.recommendation.INCOME_PROTECTION': 'Income Protection',
  'lifeStage.concern.recommendation.HEALTH_PROTECTION':
    'Medical / Healthcare Planning',
  'lifeStage.concern.recommendation.LOAN_PROTECTION': 'Debt Cancellation',
  'lifeStage.concern.recommendation.RETIREMENT': 'Retirement Income',
  'lifeStage.concern.recommendation.EDUCATION': "Child's Education",
  'lifeStage.concern.recommendation.SAVINGS': 'Savings',
  'lifeStage.concern.recommendation.INVESTMENT': 'Investments',
  'lifeStage.concern.recommendation.LEGACY_PLANNING': 'Legacy Planning',
  'goal.disclaimer': 'Disclaimer:',
  'goal.disclaimer.description':
    'Please note that the Financial Need Analysis provides guidance based on the Customer Fact Find and Customer Profile and the calculator will be refreshed with customer inputs in the Financial Need Analysis accordingly.',
  'goal.skipThisGoal': "Skip for now, I don't need this goal yet",
  'goal.mandatoryWarning': '*Mandatory to complete',
  'lifeStage.detail.question.added.dependents.phone':
    'Added dependants ({{total}})',
  'lifeStage.detail.question.added.policies.phone':
    'Added policies ({{total}})',
  edit: 'Edit',
  'goal.skipChildEducation.tooltip':
    'This checkbox value depends on the dependent information provided in the “Your current life stage” section from the previous step.',
  'goal.not.have.children': 'I don’t have any children',
  'goal.not.have.debt': 'I don’t have any debts',
  incompleteFields_other: '{{count}} incomplete fields',
  incompleteFields_one: '{{count}} incomplete field',
};
