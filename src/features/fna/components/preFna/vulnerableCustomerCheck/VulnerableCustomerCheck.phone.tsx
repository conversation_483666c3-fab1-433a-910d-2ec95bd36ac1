import { useTheme } from '@emotion/react';
import Tooltip from 'components/Tooltip';
import {
  Box,
  H6,
  LargeLabel,
  RadioButton,
  RadioButtonGroup,
  Row,
} from 'cube-ui-components';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { useGetOptionList } from 'hooks/useGetOptionList';
import React, { useCallback, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { NumberSequence } from 'types';
import { shallow } from 'zustand/shallow';
import { Portal } from '@gorhom/portal';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  useBottomSheetDynamicSnapPoints,
  BottomSheetView,
} from '@gorhom/bottom-sheet';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import { VulnerableCustomerCheckProps } from './VulnerableCustomerCheck';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import FnaConfirmFooter from '../../common/FnaConfirmFooter';
import { SharedValue } from 'react-native-reanimated';
import { useRootStackNavigation } from 'hooks/useRootStack';

export default function VulnerableCustomerCheck({
  visible,
  onDismiss,
  onAccept,
}: VulnerableCustomerCheckProps) {
  const { space } = useTheme();
  const { t } = useTranslation(['fna']);
  const { data: optionList } = useGetOptionList<'ib'>();
  const {
    vulnerableCustomerOption,
    vulnerableCustomerTag,
    isEmployed,
    haveExistingMhit,
    updateVulnerable,
  } = useFnaStore(
    state => ({
      vulnerableCustomerOption: state.vulnerable.vulnerableCustomerOption,
      vulnerableCustomerTag: state.vulnerable.vulnerableCustomerTag,
      isEmployed: state.vulnerable.isEmployed,
      haveExistingMhit: state.vulnerable.haveExistingMhit,
      updateVulnerable: state.updateVulnerable,
    }),
    shallow,
  );

  const vulnerableOptions = useMemo(
    () =>
      optionList?.VUL_CUSTOMER?.options.map(option => ({
        ...option,
        tooltip: t(
          `fna:vulnerableCustomer.${
            option.value as `0${NumberSequence<1, 5>}`
          }.tooltip`,
        ),
      })),
    [optionList?.VUL_CUSTOMER?.options, t],
  );

  const isValid =
    Boolean(vulnerableCustomerTag) &&
    (vulnerableCustomerTag === 'N' || Boolean(vulnerableCustomerOption)) &&
    isEmployed !== undefined &&
    haveExistingMhit !== undefined;

  const bottomSheetProps = useBottomSheet();
  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const initialSnapPoints = useMemo(() => ['CONTENT_HEIGHT'], []);

  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(initialSnapPoints);

  const navigation = useRootStackNavigation();

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FnaConfirmFooter
          {...props}
          buttonTitle={t('fna:next')}
          onPress={() => {
            onDismiss?.();
            setTimeout(() => {
              onAccept?.();
            }, 200);
          }}
          secondaryButtonTitle={t('fna:backToHome')}
          onSecondaryPress={() => {
            onDismiss?.();
            if (navigation.canGoBack()) {
              navigation.goBack();
            } else {
              navigation.navigate('Main', {
                screen: 'Home',
              });
            }
          }}
          visible={true}
          disabled={!isValid}
        />
      );
    },
    [navigation, onAccept, isValid, onDismiss],
  );

  return (
    <>
      {visible && (
        <Portal>
          <BottomSheetModalProvider>
            <BottomSheetModal
              {...bottomSheetProps}
              onDismiss={onDismiss}
              handleHeight={animatedHandleHeight}
              contentHeight={animatedContentHeight}
              style={{ padding: 0 }}
              snapPoints={
                animatedSnapPoints as SharedValue<(string | number)[]>
              }
              handleStyle={{ display: 'flex' }}
              enablePanDownToClose={false}
              enableDismissOnClose={false}
              backdropComponent={null}
              backgroundComponent={undefined}
              footerComponent={renderFooter}>
              <BottomSheetView onLayout={handleContentLayout}>
                <Box
                  paddingX={space[isNarrowScreen ? 3 : 4]}
                  paddingY={space[4]}>
                  <H6 fontWeight="bold">{t('fna:vulnerableCustomer.title')}</H6>
                </Box>
                <Box p={space[isNarrowScreen ? 3 : 4]} pt={0}>
                  <Box gap={space[5]}>
                    <LargeLabel fontWeight="medium">
                      {t('fna:vulnerableCustomer.q1')}
                    </LargeLabel>

                    <RadioButtonGroup
                      value={vulnerableCustomerTag}
                      onChange={vulnerableCustomerTag => {
                        updateVulnerable({
                          vulnerableCustomerTag,
                          vulnerableCustomerOption:
                            vulnerableCustomerTag === 'N'
                              ? undefined
                              : vulnerableCustomerOption,
                        });
                      }}>
                      <Row ml={space[4]}>
                        <Box flex={1}>
                          <RadioButton
                            value="Y"
                            label={t('fna:vulnerableCustomer.yes')}
                          />
                        </Box>
                        <Box flex={1}>
                          <RadioButton
                            value="N"
                            label={t('fna:vulnerableCustomer.no')}
                          />
                        </Box>
                      </Row>
                    </RadioButtonGroup>
                  </Box>
                  {vulnerableCustomerTag === 'Y' && (
                    <Box mt={space[4]} gap={space[5]} mx={space[4]}>
                      <LargeLabel fontWeight="medium">
                        {t('fna:vulnerableCustomer.q1.1')}
                      </LargeLabel>
                      <RadioButtonGroup
                        value={vulnerableCustomerOption}
                        onChange={vulnerableCustomerOption =>
                          updateVulnerable({ vulnerableCustomerOption })
                        }>
                        <Row
                          columnGap={space[9]}
                          rowGap={space[5]}
                          flexWrap="wrap">
                          {vulnerableOptions?.map(option => (
                            <VulnerableItem
                              key={option.value}
                              option={option}
                            />
                          ))}
                        </Row>
                      </RadioButtonGroup>
                    </Box>
                  )}
                  <Box mt={space[6]} gap={space[5]}>
                    <LargeLabel fontWeight="medium">
                      {t('fna:vulnerableCustomer.q2')}
                    </LargeLabel>
                    <RadioButtonGroup
                      value={isEmployed}
                      onChange={newIsEmployed => {
                        let newHaveExistingMhit = haveExistingMhit;
                        if (newIsEmployed === false) {
                          newHaveExistingMhit = false;
                        } else if (
                          newIsEmployed === true &&
                          isEmployed === false
                        ) {
                          newHaveExistingMhit = undefined;
                        }
                        updateVulnerable({
                          isEmployed: newIsEmployed,
                          haveExistingMhit: newHaveExistingMhit,
                        });
                      }}>
                      <Row ml={space[4]}>
                        <Box flex={1}>
                          <RadioButton value={true} label={t('fna:yes')} />
                        </Box>
                        <Box flex={1}>
                          <RadioButton value={false} label={t('fna:no')} />
                        </Box>
                      </Row>
                    </RadioButtonGroup>
                  </Box>
                  <Box mt={space[6]} gap={space[5]}>
                    <LargeLabel fontWeight="medium">
                      {t('fna:vulnerableCustomer.q3')}
                    </LargeLabel>
                    <RadioButtonGroup
                      value={haveExistingMhit}
                      onChange={haveExistingMhit =>
                        updateVulnerable({ haveExistingMhit })
                      }>
                      <Row ml={space[4]}>
                        <Box flex={1}>
                          <RadioButton
                            value={true}
                            label={t('fna:yes')}
                            disabled={isEmployed === false}
                          />
                        </Box>
                        <Box flex={1}>
                          <RadioButton
                            value={false}
                            label={t('fna:no')}
                            disabled={isEmployed === false}
                          />
                        </Box>
                      </Row>
                    </RadioButtonGroup>
                  </Box>
                </Box>
                <BottomSheetFooterSpace />
              </BottomSheetView>
            </BottomSheetModal>
          </BottomSheetModalProvider>
        </Portal>
      )}
    </>
  );
}

const VulnerableItem = ({
  option,
}: {
  option: {
    tooltip: string;
    value: string;
    label: string;
  };
}) => {
  const { space } = useTheme();

  return (
    <Row key={option.value} gap={space[2]} flexBasis={'100%'}>
      <RadioButton value={option.value} label={option.label} />
      {Boolean(option.tooltip) && (
        <>
          <Tooltip
            placement="overlay"
            title={option.label}
            content={option.tooltip}
          />
        </>
      )}
    </Row>
  );
};
