import { useTheme } from '@emotion/react';
import { Box, H6, H7 } from 'cube-ui-components';
import {
  AdviceType as AdviceTypeKey,
  useFnaStore,
} from 'features/fna/utils/store/fnaStore';
import { useTranslation } from 'react-i18next';
import { shallow } from 'zustand/shallow';
import FullAdviceIcon from 'features/fna/components/icons/FullAdviceIcon';
import PartialAdviceIcon from 'features/fna/components/icons/PartialAdviceIcon';
import React, { useCallback, useEffect, useMemo } from 'react';
import { CustomerChoiceProps } from './CustomerChoice';
import ChoiceButton from './ChoiceButton';
import { Portal } from '@gorhom/portal';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  useBottomSheetDynamicSnapPoints,
  BottomSheetView,
} from '@gorhom/bottom-sheet';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import FnaConfirmFooter from '../../common/FnaConfirmFooter';
import { useRootStackNavigation } from 'hooks/useRootStack';
import { SharedValue } from 'react-native-reanimated';

export default function CustomerChoice({
  visible,
  onDismiss,
  onAccept,
}: CustomerChoiceProps) {
  const { space } = useTheme();
  const { t } = useTranslation(['fna']);
  const { adviceType, updateAdviceType, vulnerableCustomerOption } =
    useFnaStore(
      state => ({
        adviceType: state.adviceType,
        updateAdviceType: state.updateAdviceType,
        vulnerableCustomerOption: state.vulnerable.vulnerableCustomerOption,
      }),
      shallow,
    );

  useEffect(() => {
    if (vulnerableCustomerOption === 'Y') {
      updateAdviceType(AdviceTypeKey.FULL);
    }
  }, [updateAdviceType, vulnerableCustomerOption]);

  const isValid = Boolean(adviceType);
  const bottomSheetProps = useBottomSheet();
  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const initialSnapPoints = useMemo(() => ['CONTENT_HEIGHT'], []);

  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(initialSnapPoints);

  const navigation = useRootStackNavigation();

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FnaConfirmFooter
          {...props}
          buttonTitle={t('fna:next')}
          onPress={() => {
            onDismiss?.();
            setTimeout(() => {
              onAccept?.();
            }, 200);
          }}
          secondaryButtonTitle={t('fna:backToHome')}
          onSecondaryPress={() => {
            onDismiss?.();
            if (navigation.canGoBack()) {
              navigation.goBack();
            } else {
              navigation.navigate('Main', {
                screen: 'Home',
              });
            }
          }}
          visible={true}
          disabled={!isValid}
        />
      );
    },
    [navigation, t, isValid, onDismiss, onAccept],
  );

  return (
    <>
      {visible && (
        <>
          <Portal>
            <BottomSheetModalProvider>
              <BottomSheetModal
                {...bottomSheetProps}
                onDismiss={onDismiss}
                handleHeight={animatedHandleHeight}
                contentHeight={animatedContentHeight}
                style={{ padding: 0 }}
                snapPoints={animatedSnapPoints as SharedValue<(string | number)[]>}
                handleStyle={{ display: 'flex' }}
                enablePanDownToClose={false}
                enableDismissOnClose={false}
                backdropComponent={null}
                backgroundComponent={undefined}
                footerComponent={renderFooter}>
                <BottomSheetView onLayout={handleContentLayout}>
                  <Box
                    paddingX={space[isNarrowScreen ? 3 : 4]}
                    paddingY={space[4]}>
                    <H6 fontWeight="bold">
                      {t('fna:vulnerableCustomer.title')}
                    </H6>
                  </Box>
                  <Box p={space[4]} pt={0}>
                    <Box gap={space[3]}>
                      <ChoiceButton
                        type={AdviceTypeKey.FULL}
                        label={t('fna:customerChoice.fullAdvice')}
                        instruction={t(
                          'fna:customerChoice.fullAdvice.instruction',
                        )}
                        description={t(
                          'fna:customerChoice.fullAdvice.description',
                        )}
                        selected={adviceType === AdviceTypeKey.FULL}
                        onPress={() => updateAdviceType(AdviceTypeKey.FULL)}
                        icon={FullAdviceIcon}
                      />
                      <ChoiceButton
                        type={AdviceTypeKey.PARTIAL}
                        label={t('fna:customerChoice.simpleAdvice')}
                        instruction={t(
                          'fna:customerChoice.simpleAdvice.instruction',
                        )}
                        description={t(
                          'fna:customerChoice.simpleAdvice.description',
                        )}
                        selected={adviceType === AdviceTypeKey.PARTIAL}
                        onPress={() => updateAdviceType(AdviceTypeKey.PARTIAL)}
                        icon={PartialAdviceIcon}
                        disabled={vulnerableCustomerOption === 'Y'}
                      />
                    </Box>
                  </Box>
                  <BottomSheetFooterSpace />
                </BottomSheetView>
              </BottomSheetModal>
            </BottomSheetModalProvider>
          </Portal>
        </>
      )}
    </>
  );
}
