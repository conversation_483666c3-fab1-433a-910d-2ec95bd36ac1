import { useTheme } from '@emotion/react';
import MarkdownText, { useMarkdownStyle } from 'components/MarkdownText';
import { H6, Icon, Label, LargeLabel, Picker, Row } from 'cube-ui-components';
import { FNA_DISCLAIMER } from 'features/fna/constants/fnaDisclaimer';
import React, { useCallback, useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { LanguagesKeys } from 'utils/translation';
import { DisclaimerProps } from './Disclaimer';
import {
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
} from '@gorhom/bottom-sheet';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { useFnaSnapPoints } from 'features/fna/hooks/useFnaSnapPoint';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import FnaConfirmFooter from '../../../common/FnaConfirmFooter';
import { useRootStackNavigation } from 'hooks/useRootStack';

export default function Disclaimer({
  visible,
  onDismiss,
  onAccept,
}: DisclaimerProps) {
  const { t } = useTranslation(['fna']);
  const snapPoints = useFnaSnapPoints();
  const bottomSheetProps = useBottomSheet();
  const { space, typography, colors } = useTheme();
  const [language, setLanguage] = useState<LanguagesKeys>('en');
  const mdStyle = useMarkdownStyle();
  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const languages = useMemo(
    () =>
      FNA_DISCLAIMER
        ? Object.keys(FNA_DISCLAIMER).map(key => ({
            label: t(`fna:language.${key as LanguagesKeys}`),
            value: key,
          }))
        : [],
    [t],
  );

  const navigation = useRootStackNavigation();

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <FnaConfirmFooter
          {...props}
          buttonTitle={t('fna:agree')}
          onPress={() => {
            onDismiss?.();
            setTimeout(() => {
              onAccept?.();
            }, 200);
          }}
          secondaryButtonTitle={t('fna:backToHome')}
          onSecondaryPress={() => {
            onDismiss?.();
            if (navigation.canGoBack()) {
              navigation.goBack();
            } else {
              navigation.navigate('Main', {
                screen: 'Home',
              });
            }
          }}
          visible={true}
        />
      );
    },
    [navigation, onAccept, onDismiss, t],
  );

  const markdownStyle = useMemo(
    () => ({
      body: {
        ...mdStyle.body,
        fontSize: typography.largeBody.size,
        lineHeight: typography.largeBody.lineHeight,
      },
      strong: {
        ...mdStyle.strong,
        fontSize: typography.largeLabel.size,
        lineHeight: typography.largeLabel.lineHeight,
      },
      paragraph: {
        marginBottom: space[2],
        marginTop: 0,
      },
      ordered_list: {
        marginBottom: space[6],
      },
      em: {
        fontFamily: 'FWDCircularTT-Medium',
        fontSize: typography.largeLabel.size,
        lineHeight: typography.largeLabel.lineHeight,
      },
      hr: {
        backgroundColor: 'transparent',
        height: 4,
      },
    }),
    [mdStyle, space, typography],
  );

  return (
    <>
      {visible && (
        <BottomSheetModalProvider>
          <BottomSheetModal
            onDismiss={onDismiss}
            index={0}
            snapPoints={snapPoints}
            {...bottomSheetProps}
            style={{ padding: 0 }}
            footerComponent={renderFooter}
            handleStyle={{ display: 'flex' }}
            enablePanDownToClose={false}
            enableDismissOnClose={false}
            backdropComponent={null}
            backgroundComponent={undefined}>
            <Row
              paddingX={space[isNarrowScreen ? 3 : 4]}
              paddingY={space[4]}
              gap={space[4]}
              justifyContent="space-between">
              <Row alignItems="center" gap={space[3]} flex={1}>
                <H6 fontWeight="bold">{t('fna:disclaimer.title')}</H6>
              </Row>
              {languages.length > 1 && (
                <Picker
                  type="chip"
                  items={languages}
                  value={language}
                  onChange={language => setLanguage(language as LanguagesKeys)}
                />
              )}
            </Row>
            <BottomSheetScrollView
              keyboardDismissMode="on-drag"
              style={{
                paddingHorizontal: space[isNarrowScreen ? 3 : 4],
              }}>
              <Row marginBottom={space[4]} alignItems="center" gap={space[2]}>
                <LargeLabel fontWeight="bold">{t('fna:consents')}</LargeLabel>
                <Row gap={space[1]} alignItems="center">
                  <Icon.Warning
                    fill={colors.palette.alertRed}
                    size={space[4]}
                  />
                  <Label color={colors.palette.alertRed}>
                    {t('fna:importantNotice')}
                  </Label>
                </Row>
              </Row>
              <MarkdownText style={markdownStyle}>
                {FNA_DISCLAIMER?.[language] || ''}
              </MarkdownText>
              <BottomSheetFooterSpace />
            </BottomSheetScrollView>
          </BottomSheetModal>
        </BottomSheetModalProvider>
      )}
    </>
  );
}
