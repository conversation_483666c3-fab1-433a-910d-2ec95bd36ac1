import styled from '@emotion/native';
import {
  Body,
  Box,
  Card,
  Column,
  H6,
  Icon,
  Row,
  SmallLabel,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import { useMemo } from 'react';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { shallow } from 'zustand/shallow';
import { Gender } from 'types/person';
import { CardHeader } from '../../../common/CardHeader';
import CoupleLogo from 'features/fna/components/illustrations/CoupleLogo';
import CoupleWithKidLogo from 'features/fna/components/illustrations/CoupleWithKidLogo';
import EmptyNesterMaleLogo from 'features/fna/components/illustrations/EmptyNesterMaleLogo';
import EmptyNesterFemaleLogo from 'features/fna/components/illustrations/EmptyNesterFemaleLogo';
import RetireLogo from 'features/fna/components/illustrations/RetireLogo';
import SingleWithDependFemaleLogo from 'features/fna/components/illustrations/SingleWithDependFemaleLogo';
import SingleLogo from 'features/fna/components/illustrations/SingleLogo';
import SingleFemaleLogo from 'features/fna/components/illustrations/SingleFemaleLogo';
import SingleWithDependMaleLogo from 'features/fna/components/illustrations/SingleWithDependMaleLogo';
import { Concern } from 'features/fna/types/concern';
import LeaderIcon from '../../../icons/LeaderIcon';
import { TouchableOpacity, View } from 'react-native';
import { calculateAge } from 'utils/helper/calculateAge';
import { MAX_AGE_RETIREMENT } from 'features/fna/constants/goalCalculation';
import { LifeStageItemType } from 'features/fna/types/lifeJourney';

interface IProps {
  onPress: () => void;
}

export const AboutMe = (props: IProps) => {
  const { onPress } = props;
  const { t } = useTranslation(['fna']);
  const { colors, sizes, space } = useTheme();
  const { lifeJourney, getCustomerName } = useFnaStore(
    state => ({
      lifeJourney: state.lifeJourney,
      getCustomerName: state.getCustomerName,
    }),
    shallow,
  );

  const DEFAULT_LIFE_STAGE_DATA: LifeStageItemType[] = useMemo(
    () => [
      {
        title: t('fna:single'),
        img:
          lifeJourney.gender === Gender.FEMALE ? (
            <SingleFemaleLogo />
          ) : (
            <SingleLogo />
          ),
        lifeStage: 'SINGLE',
      },
      {
        title: t('fna:singleWithDepend'),
        img:
          lifeJourney.gender === Gender.FEMALE ? (
            <SingleWithDependFemaleLogo />
          ) : (
            <SingleWithDependMaleLogo />
          ),
        lifeStage: 'SINGLE_WITH_DEPENDENT',
      },
      {
        title: t('fna:couple'),
        img: <CoupleLogo />,
        lifeStage: 'COUPLE',
      },
      {
        title: t('fna:coupleWithKids'),
        img: <CoupleWithKidLogo />,
        lifeStage: 'COUPLE_WITH_KIDS',
      },
      {
        title: t('fna:emptyNester'),
        img:
          lifeJourney.gender === Gender.FEMALE ? (
            <EmptyNesterFemaleLogo />
          ) : (
            <EmptyNesterMaleLogo />
          ),
        lifeStage: 'EMPTY_NESTER',
      },
      {
        title: t('fna:retired'),
        img: <RetireLogo />,
        lifeStage: 'RETIRED',
      },
    ],
    [lifeJourney.gender, t],
  );

  const lifeStageSelected = useMemo(() => {
    const { lifeStage } = lifeJourney;
    const lifeStageObject = DEFAULT_LIFE_STAGE_DATA.find(
      item => item?.lifeStage === lifeStage,
    );
    return lifeStageObject;
  }, [DEFAULT_LIFE_STAGE_DATA, lifeJourney]);

  const DEFAULT_CONCERNS_DATA: Concern[] = useMemo(() => {
    const dataConcerns: Concern[] = [
      {
        id: 'INCOME_PROTECTION',
        title: t('fna:lifeStage.concern.INCOME_PROTECTION.mobile'),
      },
      {
        id: 'HEALTH_PROTECTION',
        title: t('fna:lifeStage.concern.HEALTH_PROTECTION.mobile'),
      },
      {
        id: 'LOAN_PROTECTION',
        title: t('fna:lifeStage.concern.LOAN_PROTECTION.mobile'),
      },
      {
        id: 'INVESTMENT',
        title: t('fna:lifeStage.concern.INVESTMENT.mobile'),
      },
      {
        id: 'RETIREMENT',
        title: t('fna:lifeStage.concern.RETIREMENT.mobile'),
      },
      {
        id: 'LEGACY_PLANNING',
        title: t('fna:lifeStage.concern.LEGACY_PLANNING.mobile'),
      },
      {
        id: 'EDUCATION',
        title: t('fna:lifeStage.concern.EDUCATION.mobile'),
      },
    ];
    if (
      lifeJourney.dob &&
      calculateAge(lifeJourney.dob) >= MAX_AGE_RETIREMENT
    ) {
      dataConcerns.splice(4, 1);
    }
    return dataConcerns;
  }, []);

  const concernsSelected = useMemo(() => {
    const { concerns } = lifeJourney;
    const lifeStageObject = DEFAULT_CONCERNS_DATA.filter(item =>
      concerns?.includes(item?.id),
    )?.map(item => item?.title);
    return lifeStageObject;
  }, [lifeJourney]);

  const genderIconRender = useMemo(() => {
    const isMale = lifeJourney?.gender === Gender.MALE;
    if (isMale) {
      return <Icon.Male2 size={sizes[4]} />;
    }
    return <Icon.Female2 size={sizes[4]} />;
  }, [lifeJourney]);

  const genderContentWithLifeStage = useMemo(() => {
    if (!lifeStageSelected) {
      return;
    }
    switch (lifeStageSelected?.lifeStage) {
      case 'COUPLE':
        return (
          <Row alignItems="flex-start" mt={space[1]}>
            <Icon.Flag size={sizes[4]} />
            <Box width={sizes[1]} />
            <ConcernText fontWeight="medium">
              {`${t('fna:goals')}: ${concernsSelected?.join?.(', ')}`}
            </ConcernText>
          </Row>
        );
      case 'COUPLE_WITH_KIDS':
        return (
          <Column>
            <Row alignItems="flex-start" mt={space[1]}>
              <Icon.Kid size={sizes[4]} />
              <Box width={sizes[1]} />
              <ConcernText fontWeight="medium">
                {lifeJourney.numberOfKids > 1
                  ? t('fna:lifeStage.detail.question.coupleWithKids', {
                      numberOfKids: lifeJourney.numberOfKids,
                    })
                  : t('fna:lifeStage.detail.question.coupleWithKid', {
                      numberOfKids: lifeJourney.numberOfKids,
                    })}
              </ConcernText>
            </Row>
            <Row alignItems="flex-start" mt={space[1]}>
              <Icon.Flag size={sizes[4]} />
              <Box width={sizes[1]} />
              <ConcernText fontWeight="medium">
                {`${t('fna:goals')}: ${concernsSelected?.join?.(', ')}`}
              </ConcernText>
            </Row>
          </Column>
        );
      case 'EMPTY_NESTER':
        return (
          <Column>
            {lifeJourney.havePartner ? (
              <Row alignItems="flex-start" mt={space[1]}>
                <Icon.Retirement size={sizes[4]} />
                <Box width={sizes[1]} />
                <ConcernText fontWeight="medium">
                  {t('fna:lifeStage.detail.aboutMe.havePartner')}
                </ConcernText>
              </Row>
            ) : null}
            {lifeJourney.planToTravel ? (
              <Row alignItems="flex-start" mt={space[1]}>
                <Icon.Travel size={sizes[4]} />
                <Box width={sizes[1]} />
                <ConcernText fontWeight="medium">
                  {t('fna:lifeStage.detail.aboutMe.planToTravel')}
                </ConcernText>
              </Row>
            ) : null}
            <Row alignItems="flex-start" mt={space[1]}>
              <Icon.Flag size={sizes[4]} />
              <Box width={sizes[1]} />
              <ConcernText fontWeight="medium">
                {`${t('fna:goals')}: ${concernsSelected?.join?.(', ')}`}
              </ConcernText>
            </Row>
          </Column>
        );
      case 'RETIRED':
        return (
          <Column>
            {lifeJourney.havePartner ? (
              <Row alignItems="flex-start" mt={space[1]}>
                <Icon.Retirement size={sizes[4]} />
                <Box width={sizes[1]} />
                <ConcernText fontWeight="medium">
                  {t('fna:lifeStage.detail.aboutMe.havePartner')}
                </ConcernText>
              </Row>
            ) : null}
            {lifeJourney.planToTravel ? (
              <Row alignItems="flex-start" mt={space[1]}>
                <Icon.Travel size={sizes[4]} />
                <Box width={sizes[1]} />
                <ConcernText fontWeight="medium">
                  {t('fna:lifeStage.detail.aboutMe.planToTravel')}
                </ConcernText>
              </Row>
            ) : null}
            <Row alignItems="flex-start" mt={space[1]}>
              <Icon.Flag size={sizes[4]} />
              <Box width={sizes[1]} />
              <ConcernText fontWeight="medium">
                {`${t('fna:goals')}: ${concernsSelected?.join?.(', ')}`}
              </ConcernText>
            </Row>
          </Column>
        );
      default:
        return (
          <Row alignItems="flex-start" mt={space[1]}>
            <Icon.Flag size={sizes[4]} />
            <Box width={sizes[1]} />
            <ConcernText fontWeight="medium">
              {`${t('fna:goals')}: ${concernsSelected?.join?.(', ')}`}
            </ConcernText>
          </Row>
        );
    }
  }, [lifeStageSelected, lifeJourney]);

  return (
    <TouchableOpacity onPress={onPress}>
      <CardContainer>
        <Row mb={space[2]} alignItems="flex-start">
          <CardHeader
            style={{ flex: 1, marginBottom: 0 }}
            title={t('fna:aboutMe')}
            icon={<LeaderIcon />}
          />
          <BadgeDoneView>
            <Body fontWeight="normal" color={colors.palette.fwdOrange[100]}>
              {t('fna:section')}
            </Body>
            <Row>
              <Icon.TickCircleFill />
              <Box width={sizes[1]} />
              <H6 fontWeight="bold" color={colors.palette.fwdOrange[100]}>
                {t('fna:done')}
              </H6>
            </Row>
          </BadgeDoneView>
        </Row>
        <Row alignItems="center">
          <Column
            borderRadius={sizes[1]}
            overflow="hidden"
            backgroundColor={colors.palette.fwdOrange[5]}>
            <Box pt={sizes[2]}>{lifeStageSelected?.img}</Box>
            <TitleContainer>
              <SmallLabel fontWeight="medium">
                {lifeStageSelected?.title}
              </SmallLabel>
            </TitleContainer>
          </Column>
          <Column marginLeft={sizes[2]} flex={1}>
            <Row alignItems="center">
              {genderIconRender}
              <Box width={sizes[1]} />
              <SmallLabel
                fontWeight="medium"
                numberOfLines={2}
                style={{ flex: 1 }}>
                {`${getCustomerName()} (${t('fna:yo', {
                  age: lifeJourney.dob ? calculateAge(lifeJourney.dob) : '--',
                })})`}
              </SmallLabel>
            </Row>
            {genderContentWithLifeStage}
          </Column>
        </Row>
      </CardContainer>
    </TouchableOpacity>
  );
};

const CardContainer = styled(Card)(({ theme }) => ({
  marginTop: theme.space[4],
  width: '100%',
  backgroundColor: theme.colors.background,
  borderRadius: theme.borderRadius.large,
  padding: theme.space[2],
}));

const ConcernText = styled(SmallLabel)(() => ({
  flexShrink: 1,
}));

const BadgeDoneView = styled(View)(
  ({ theme: { colors, space, borderRadius } }) => ({
    marginTop: -space[6],
    marginRight: -space[2],
    width: 147,
    backgroundColor: colors.primaryVariant2,
    borderColor: colors.primary,
    borderWidth: 1,
    borderRadius: borderRadius.medium,
    borderBottomRightRadius: 0,
    paddingVertical: space[2],
    paddingHorizontal: space[3],
  }),
);

const TitleContainer = styled(View)(({ theme: { space, colors } }) => ({
  position: 'absolute',
  bottom: space[1],
  justifyContent: 'center',
  borderWidth: 2,
  alignItems: 'center',
  alignSelf: 'center',
  borderRadius: space[4],
  backgroundColor: colors.palette.white,
  paddingLeft: space[2],
  paddingRight: space[2],
  paddingTop: 2,
  paddingBottom: 2,
  borderColor: colors.primary,
}));
