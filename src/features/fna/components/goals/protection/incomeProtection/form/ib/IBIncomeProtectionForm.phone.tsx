import { useTheme } from '@emotion/react';
import { View } from 'react-native';
import styled from '@emotion/native';
import React from 'react';
import { useTranslation } from 'react-i18next';

import {
  Box,
  H6,
  LargeLabel,
  Row,
  Checkbox,
  TextField,
  LargeBody,
} from 'cube-ui-components';
import IncomeProtectionLogo from 'features/fna/components/illustrations/IncomeProtectionLogo';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import DecimalTextField from 'components/DecimalTextField';
import { IBIncomeProtectionFormProps } from './IBIncomeProtectionForm';

export function IBIncomeProtectionFormPhone({
  formData: incomeGoal,
  updateCoverageAmount,
  updateAnnualIncome,
  updateIncomeYears,
  updateHaveExisting,
}: IBIncomeProtectionFormProps) {
  const { t } = useTranslation(['fna']);
  const { space, colors } = useTheme();
  const { isNarrowScreen } = useWindowAdaptationHelpers();

  return (
    <Box py={space[4]} px={space[isNarrowScreen ? 3 : 4]}>
      <Row mb={space[10]} alignItems="center">
        <Box flex={1}>
          <H6 fontWeight="bold">{t('fna:protectionGoals.income.title')}</H6>
        </Box>
        <Box mx={space[4]}>
          <IncomeProtectionLogo />
        </Box>
      </Row>
      <FormContainer
        enabled={Boolean(incomeGoal.enabled)}
        pointerEvents={incomeGoal.enabled ? 'auto' : 'none'}>
        <Row>
          <Row flex={1}>
            <LargeLabel>1.</LargeLabel>
            <Box flex={1}>
              <LargeLabel>{t('fna:protectionGoals.annual.income')}</LargeLabel>
            </Box>
          </Row>
          <Box w={space[3]} />
          <DecimalTextField
            label={t('fna:amount.label')}
            value={incomeGoal.annualIncome}
            onChange={updateAnnualIncome}
            style={{ flex: 1 }}
            max={MAX_AMOUNT_VALUE}
          />
        </Row>
        <Row mt={space[8]}>
          <Row style={{ flex: 1 }}>
            <LargeLabel>2.</LargeLabel>
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:protectionGoals.years.income')}
            </LargeLabel>
          </Row>
          <Box w={space[3]} />
          <TextField
            label={t('fna:protectionGoals.income.years')}
            value={String(incomeGoal.yearsToReplace ?? '')}
            keyboardType="number-pad"
            returnKeyType="done"
            maxLength={2}
            style={{ flex: 1 }}
            onChange={updateIncomeYears}
          />
        </Row>
        <Box mt={space[7]}>
          <Checkbox
            label={t('fna:protectionGoals.existing.life.insurance')}
            checked={!!incomeGoal.hasSavings}
            onChange={updateHaveExisting}
          />
        </Box>
        {incomeGoal.hasSavings && (
          <Row mt={space[7]} alignItems="center">
            <Row flex={1} alignSelf="center">
              <LargeLabel>3.</LargeLabel>
              <LargeLabel style={{ flex: 1 }}>
                {t('fna:protectionGoals.existing.income')}
              </LargeLabel>
            </Row>
            <Box w={space[3]} />
            <DecimalTextField
              label={t('fna:amount.label')}
              value={incomeGoal.coverageAmount}
              onChange={updateCoverageAmount}
              style={{ flex: 1 }}
              max={MAX_AMOUNT_VALUE}
            />
          </Row>
        )}
      </FormContainer>

      <Divider />
      <Box mb={space[10]}>
        <LargeBody>
          <LargeBody fontWeight="bold">{t('fna:goal.disclaimer')}</LargeBody>{' '}
          {t('fna:goal.disclaimer.description')}
        </LargeBody>
      </Box>
    </Box>
  );
}

const Divider = styled(View)(({ theme }) => {
  return {
    marginVertical: theme.space[7],
    height: 1,
    backgroundColor: theme.colors.palette.fwdGrey[100],
  };
});

const FormContainer = styled.View<{ enabled: boolean }>(({ enabled }) => ({
  opacity: enabled ? 1 : 0.3,
}));
