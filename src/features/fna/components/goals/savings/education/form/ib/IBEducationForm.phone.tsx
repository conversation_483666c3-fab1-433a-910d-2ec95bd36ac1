import { useTheme } from '@emotion/react';
import {
  Box,
  Checkbox,
  H6,
  LargeBody,
  LargeLabel,
  Row,
  Icon,
} from 'cube-ui-components';
import <PERSON><PERSON>ogo from 'features/fna/components/illustrations/KidLogo';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { useContext, useEffect, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { findNodeHandle, ScrollView, View } from 'react-native';
import KidForm from '../../input/kidForm/KidForm';
import KidsTab from '../../input/kidsTab/KidsTab';
import { NumberOfKids } from '../../input/NumberOfKids';
import type { IBEducationFormProps } from './IBEducationForm';
import styled from '@emotion/native';
import DecimalTextField from 'components/DecimalTextField';
import { MAX_AMOUNT_VALUE } from 'constants/inputAmount';
import { GoalContext } from 'features/fna/components/goals/goalGroup/GoalContext';
import Tooltip from 'components/Tooltip';

export default function IBEducationFormPhone({
  formData: educationGoal,
  setFormData: updateEducationGoal,
  tabOfKids: tabOfKids,
  calculateTotalFunds: calculateTotalFunds,
  onToggleSkipGoal,
  onChangeCoverageAmount,
}: IBEducationFormProps) {
  const { t } = useTranslation(['fna']);
  const { space, colors } = useTheme();
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const [currentTab, setCurrentTab] = useState(0);
  const tabsPosition = useRef(0);
  const tabsHeight = useRef(0);
  const formPositions = useRef<number[]>([]);

  if (formPositions.current.length > tabOfKids.length) {
    formPositions.current.splice(0, tabOfKids.length);
  } else if (formPositions.current.length < tabOfKids.length) {
    formPositions.current.fill(
      0,
      formPositions.current.length,
      tabOfKids.length,
    );
  }

  const goalContext = useContext(GoalContext);

  useEffect(() => {
    return goalContext?.addOnScrollListener(e => {
      e.persist();
      if (
        goalContext.scrollViewRef.current &&
        e?.nativeEvent?.contentOffset?.y !== undefined
      ) {
        let activeIndex = 0;
        for (let i = 0; i < formPositions.current.length; i++) {
          const pos = formPositions.current[i];
          if (pos <= e.nativeEvent.contentOffset.y) {
            activeIndex = i;
          }
        }
        setCurrentTab(activeIndex);
      }
    });
  }, [colors.background, goalContext, isNarrowScreen, space, tabOfKids.length]);

  return (
    <Box py={space[4]} px={space[isNarrowScreen ? 3 : 4]}>
      <Row mb={space[5]} alignItems="center">
        <Box flex={1}>
          <H6 fontWeight="bold">{t('fna:savingsGoals.education.title')}</H6>
        </Box>
        <Box mr={space[4]} ml={space[4]}>
          <KidLogo />
        </Box>
      </Row>
      <Row alignItems="center" gap={space[1]} mb={space[6]}>
        <Checkbox
          label={t('fna:goal.not.have.children')}
          checked={!educationGoal.enabled}
          onChange={onToggleSkipGoal}
          disabled
          labelStyle={{ opacity: 0.5 }}
          style={{ opacity: 0.5 }}
        />
        <Tooltip
          content={t('fna:goal.skipChildEducation.tooltip')}
          icon={<Icon.InfoCircle />}
        />
      </Row>
      <FormContainer
        enabled={Boolean(educationGoal.enabled)}
        pointerEvents={educationGoal.enabled ? 'auto' : 'none'}>
        <Row
          mt={space[4]}
          pb={space[3]}
          borderBottomColor={'#D9D9D9'}
          borderBottomWidth={1}>
          <Box flex={1}>
            <LargeLabel style={{ flex: 1 }}>
              {t('fna:savingsGoals.education.numberOfKids')}
            </LargeLabel>
          </Box>
          <Box w={space[3]} />
          <NumberOfKids
            formData={educationGoal}
            setFormData={updateEducationGoal}
            calculateTotalFunds={calculateTotalFunds}
          />
        </Row>

        {tabOfKids.length > 1 && (
          <View
            onLayout={e => (tabsHeight.current = e.nativeEvent.layout.height)}
            ref={ref => {
              if (goalContext && goalContext.scrollViewRef.current) {
                const containerNode = findNodeHandle(
                  goalContext.scrollViewRef.current,
                );
                if (containerNode) {
                  ref?.measureLayout(containerNode, (...layout) => {
                    tabsPosition.current = layout[1];
                  });
                }
              }
            }}>
            <Row
              py={space[4]}
              bgColor={colors.background}
              flexWrap="wrap"
              rowGap={space[2]}>
              {tabOfKids.map(tab => {
                const isFocused = currentTab === tab.index;
                return (
                  <KidsTab
                    key={'KID_TABS_' + tab?.index}
                    text={tab?.label}
                    onPress={() => {
                      setCurrentTab(tab.index);
                      goalContext?.scrollViewRef?.current?.scrollToPosition(
                        0,
                        formPositions.current[tab.index],
                        true,
                      );
                    }}
                    isFocused={isFocused}
                  />
                );
              })}
            </Row>
          </View>
        )}

        {tabOfKids.map((item, idx) => (
          <View
            key={item.index}
            ref={ref => {
              if (goalContext && goalContext.scrollViewRef.current) {
                const containerNode = findNodeHandle(
                  goalContext.scrollViewRef.current,
                );
                if (containerNode) {
                  ref?.measureLayout(containerNode, (...layout) => {
                    formPositions.current[idx] = layout[1];
                  });
                }
              }
            }}>
            <KidForm
              index={item.index}
              formData={educationGoal}
              setFormData={updateEducationGoal}
              calculateTotalFunds={calculateTotalFunds}
            />
          </View>
        ))}
        <Box h={1} backgroundColor={'#D9D9D9'} mt={space[5]} />
        <Row mt={28} alignItems="center" gap={space[3]}>
          <Row flex={1}>
            <LargeBody>{tabOfKids.length * 5 + 1}.</LargeBody>
            <LargeBody style={{ flex: 1 }}>
              {t('fna:savingsGoals.education.howMuchHaveYouSaved')}
            </LargeBody>
          </Row>
          <DecimalTextField
            label={t('fna:amount.label')}
            value={educationGoal.coverageAmount}
            onChange={onChangeCoverageAmount}
            style={{ flex: 1 }}
            max={MAX_AMOUNT_VALUE}
          />
        </Row>
      </FormContainer>
      <Box h={1} backgroundColor={'#D9D9D9'} mt={space[5]} mb={space[6]} />
      <Box mb={space[10]}>
        <LargeBody>
          <LargeBody fontWeight="bold">{t('fna:goal.disclaimer')}</LargeBody>{' '}
          {t('fna:goal.disclaimer.description')}
        </LargeBody>
      </Box>
    </Box>
  );
}

const FormContainer = styled.View<{ enabled: boolean }>(({ enabled }) => ({
  opacity: enabled ? 1 : 0.3,
}));
