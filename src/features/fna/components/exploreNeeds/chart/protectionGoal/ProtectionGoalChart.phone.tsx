import {
  Body,
  Box,
  Card,
  Column,
  ExtraSmallLabel,
  H6,
  H7,
  Icon,
  LargeLabel,
  Row,
  SmallLabel,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import { CardHeader } from '../../../common/CardHeader';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { NumberSequence, RootStackParamList } from 'types';
import styled from '@emotion/native';
import { TouchableOpacity } from 'react-native';
import { ConcernId } from 'features/fna/types/concern';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import useToggle from 'hooks/useToggle';
import { GoalBreakdown } from '../breakdown/GoalBreakdown';
import {
  currencyKFormat,
  getPriorityColor,
} from 'features/fna/utils/helper/fnaUtils';
import DialogPhone from 'components/Dialog.phone';
import { InternalProtectionGoalChartProps } from './ProtectionGoalChart';
import ShieldIcon from 'features/fna/components/icons/ShieldIcon';
import ExpandSimplifyIcon from 'features/fna/components/icons/ExpandSimplifyIcon';
import { Fragment, useMemo } from 'react';

export default function ProtectionGoalChartPhone({
  isFnaValid,
  data,
  hasIllustration,
  isOptional,
  totalGap,
}: InternalProtectionGoalChartProps) {
  const { t } = useTranslation(['fna']);
  const { space, colors, sizes, borderRadius } = useTheme();
  const { navigate } = useNavigation<NavigationProp<RootStackParamList>>();
  const [summaryVisible, showSummary, hideSummary] = useToggle();
  const priorityColor = useMemo(() => getPriorityColor(colors), [colors]);

  const fnaState = useFnaStore();
  const concerns = fnaState.lifeJourney.concerns;

  return (
    <Fragment>
      <TouchableOpacity
        disabled={!isFnaValid}
        onPress={() => {
          navigate('ProtectionGoal');
        }}>
        <CardContainer style={{ marginTop: space[hasIllustration ? 7 : 3] }}>
          <Row alignItems="flex-start">
            <CardHeader
              hasRightBtn={!hasIllustration}
              rightBtnVariant={isOptional ? 'secondary' : 'primary'}
              title={t('fna:protect')}
              icon={<ShieldIcon width={sizes[9]} height={sizes[9]} />}
              onPressRightBtn={() => {
                navigate('ProtectionGoal');
              }}
              btnDisabled={!isFnaValid}
              style={{ flex: 1 }}
            />
            {hasIllustration && (
              <BadgeDoneView onPress={showSummary}>
                <Body fontWeight="normal" color={colors.palette.fwdOrange[100]}>
                  {t('fna:protectionGoals.totalCoverNeeds')}
                </Body>
                <Row alignItems="center">
                  <Row>
                    <SmallLabel
                      style={{ marginTop: 3 }}
                      color={colors.palette.alertRed}>
                      {t('fna:currency')}
                    </SmallLabel>
                    <Box w={2} />
                    <H6 fontWeight="bold" color={colors.palette.alertRed}>
                      {currencyKFormat(totalGap)}
                    </H6>
                  </Row>
                  <Box flex={1} />
                  <ExpandSimplifyIcon />
                </Row>
              </BadgeDoneView>
            )}
          </Row>
          {data.map((item, index) => {
            const priority = concerns.indexOf(item.id as ConcernId);

            return (
              <TouchableOpacity
                key={item.id}
                disabled={!isFnaValid}
                onPress={() => {
                  navigate('ProtectionGoal', { tab: item.id });
                }}>
                <Row
                  alignItems="center"
                  alignContent="center"
                  p={space[1]}
                  borderRadius={borderRadius['small']}
                  backgroundColor={
                    item.mandatory && !item.isCompleted
                      ? colors.primaryVariant3
                      : colors.background
                  }
                  marginBottom={
                    index === data.length - 1 ? space[0] : space[5]
                  }>
                  <Row
                    flex={3}
                    alignItems="center"
                    alignContent="center"
                    style={{ gap: space[2] }}>
                    <Box alignItems="center" width={sizes[10]}>
                      {item?.icon && item?.icon}
                    </Box>
                    <Column alignItems="flex-start">
                      <H7 fontWeight="bold">
                        {item.title}
                        {item.mandatory ? '*' : ''}
                      </H7>
                      {priority >= 0 ? (
                        <Box
                          bgColor={
                            priorityColor[priority as NumberSequence<0, 2>]
                          }
                          mt={space[1]}
                          borderRadius={space[3]}
                          paddingX={space[2]}
                          paddingY={space[1] / 2}>
                          <ExtraSmallLabel>
                            {t(
                              `fna:lifeStage.concern.priority.${
                                priority as 0 | 1 | 2
                              }`,
                            )}
                          </ExtraSmallLabel>
                        </Box>
                      ) : null}
                    </Column>
                  </Row>
                  {item.isCompleted || !hasIllustration ? (
                    <Row
                      alignSelf="center"
                      alignItems="flex-end"
                      justifyContent="center">
                      <Column
                        alignSelf="stretch"
                        alignItems="flex-end"
                        justifyContent="space-between">
                        <SmallLabel
                          color={
                            item.isCompleted
                              ? colors.palette.fwdGreyDarkest
                              : colors.palette.fwdGreyDark
                          }>
                          {t('fna:coverNeeds')}
                        </SmallLabel>
                        <Row>
                          <ExtraSmallLabel
                            style={{ marginTop: 3 }}
                            color={
                              item.isCompleted
                                ? colors.onBackground
                                : colors.palette.fwdGreyDark
                            }>
                            {t('fna:currency')}
                          </ExtraSmallLabel>
                          <Box w={2} />
                          <LargeLabel
                            fontWeight="bold"
                            color={
                              item.isCompleted
                                ? colors.onBackground
                                : colors.palette.fwdGreyDark
                            }>
                            {item.isCompleted
                              ? currencyKFormat(item.value ?? 0)
                              : '--'}
                          </LargeLabel>
                        </Row>
                      </Column>
                    </Row>
                  ) : (
                    <AddButton
                      onPress={() => {
                        navigate('ProtectionGoal', { tab: item.id });
                      }}>
                      <Body>{t('fna:add')}</Body>
                    </AddButton>
                  )}
                </Row>
              </TouchableOpacity>
            );
          })}
        </CardContainer>
      </TouchableOpacity>
      <DialogPhone visible={summaryVisible} style={{ paddingRight: space[2] }}>
        <Box>
          <CloseButtonDialog onPress={hideSummary}>
            <Icon.Close fill={colors.onBackground} />
          </CloseButtonDialog>
          <GoalBreakdown
            needType="PROTECTION"
            data={fnaState}
            hasTooltip
            concerns={fnaState.lifeJourney.concerns}
            adviceType={fnaState.adviceType}
          />
        </Box>
      </DialogPhone>
    </Fragment>
  );
}

const CloseButtonDialog = styled.TouchableOpacity(({ theme }) => ({
  flexDirection: 'row',
  justifyContent: 'flex-end',
  paddingRight: theme.space[2],
}));

const CardContainer = styled(Card)(({ theme }) => ({
  width: '100%',
  backgroundColor: theme.colors.background,
  borderRadius: theme.borderRadius.large,
  padding: theme.space[4],
}));

const BadgeDoneView = styled(TouchableOpacity)(
  ({ theme: { colors, space, borderRadius } }) => ({
    marginTop: -space[8],
    marginRight: -space[4],
    width: 147,
    backgroundColor: colors.primaryVariant2,
    borderColor: colors.primary,
    borderWidth: 1,
    borderRadius: borderRadius.medium,
    borderBottomRightRadius: 0,
    paddingVertical: space[2],
    paddingHorizontal: space[3],
  }),
);

const AddButton = styled.TouchableOpacity(({ theme }) => {
  return {
    borderWidth: 1,
    borderColor: theme.colors.palette.fwdGrey[100],
    borderRadius: 9999,
    paddingVertical: 5.5,
    paddingHorizontal: theme.space[3],
    marginBottom: theme.space[1],
    backgroundColor: theme.colors.background,
  };
});
