import { useTheme } from '@emotion/react';
import {
  Body,
  Box,
  Card,
  Column,
  ExtraSmallLabel,
  H6,
  H7,
  Icon,
  LargeLabel,
  PictogramIcon,
  Row,
  SmallBody,
  SmallLabel,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { CardHeader } from '../../../common/CardHeader';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { NumberSequence, RootStackParamList } from 'types';
import styled from '@emotion/native';
import { TouchableOpacity } from 'react-native';
import { ConcernId } from 'features/fna/types/concern';
import { GoalBreakdown } from '../breakdown/GoalBreakdown';
import useToggle from 'hooks/useToggle';
import {
  currencyKFormat,
  getDisplayInYear,
  getPriorityColor,
} from 'features/fna/utils/helper/fnaUtils';
import DialogPhone from 'components/Dialog.phone';
import { InternalSavingsChartProps } from './SavingsGoalChart';
import { Fragment, useMemo } from 'react';
import ExpandSimplifyIcon from 'features/fna/components/icons/ExpandSimplifyIcon';
import { country } from 'utils/context';

export default function SavingsGoalChartPhone({
  isFnaValid,
  data,
  hasIllustration,
  isOptional,
  totalGap,
}: InternalSavingsChartProps) {
  const { t } = useTranslation(['fna']);
  const { space, colors, sizes, borderRadius } = useTheme();
  const { navigate } = useNavigation<NavigationProp<RootStackParamList>>();
  const [summaryVisible, showSummary, hideSummary] = useToggle();
  const priorityColor = useMemo(() => getPriorityColor(colors), [colors]);

  const fnaState = useFnaStore();
  const concerns = fnaState.lifeJourney.concerns;

  const breakdownData = useMemo(
    () => ({
      ...fnaState,
      name: fnaState.getCustomerName(),
      dob: fnaState.lifeJourney.dob,
    }),
    [fnaState],
  );

  return (
    <Fragment>
      <TouchableOpacity
        disabled={!isFnaValid}
        onPress={() => {
          navigate('SavingsGoal');
        }}>
        <CardContainer>
          <Row alignItems="flex-start">
            <CardHeader
              title={t('fna:saving')}
              hasRightBtn={!hasIllustration}
              rightBtnVariant={isOptional ? 'secondary' : 'primary'}
              icon={<PictogramIcon.Cash2 size={sizes[9]} />}
              onPressRightBtn={() => {
                navigate('SavingsGoal');
              }}
              btnDisabled={!isFnaValid}
              style={{ flex: 1 }}
            />
            {hasIllustration && (
              <BadgeDoneView onPress={showSummary}>
                <Body fontWeight="normal" color={colors.palette.fwdOrange[100]}>
                  {t('fna:savingsGoals.totalSavingNeeds')}
                </Body>
                <Row alignItems="center">
                  <Row>
                    <SmallLabel
                      style={{ marginTop: 3 }}
                      color={colors.palette.alertRed}>
                      {t('fna:currency')}
                    </SmallLabel>
                    <Box w={2} />
                    <H6 fontWeight="bold" color={colors.palette.alertRed}>
                      {currencyKFormat(totalGap)}
                    </H6>
                  </Row>
                  <Box flex={1} />
                  <ExpandSimplifyIcon />
                </Row>
              </BadgeDoneView>
            )}
          </Row>
          {data.map((item, index) => {
            const priority = concerns.indexOf(item.id as ConcernId);

            return (
              <TouchableOpacity
                key={item.id}
                disabled={!isFnaValid}
                onPress={() => {
                  navigate('SavingsGoal', { tab: item.id });
                }}>
                <Row
                  alignItems="center"
                  alignContent="center"
                  gap={space[1]}
                  p={space[1]}
                  borderRadius={borderRadius['small']}
                  backgroundColor={
                    item.mandatory && !item.isCompleted
                      ? colors.primaryVariant3
                      : colors.background
                  }
                  marginBottom={
                    index === data.length - 1 ? space[0] : space[3]
                  }>
                  <Row
                    flex={3}
                    alignItems="center"
                    alignContent="center"
                    style={{ gap: space[2] }}>
                    <Box alignItems="center" width={sizes[10]}>
                      {item?.icon && item?.icon}
                    </Box>
                    <Column
                      flex={1}
                      alignItems="flex-start"
                      justifyContent="space-between">
                      <Row>
                        <H7 style={{ flexShrink: 1 }} fontWeight="bold">
                          {item.title}
                          {item.mandatory ? '*' : ''}
                        </H7>
                      </Row>
                      <Row mt={space[1]} alignItems="center" gap={space[1]}>
                        {priority >= 0 ? (
                          <Box
                            bgColor={
                              priorityColor[priority as NumberSequence<0, 2>]
                            }
                            borderRadius={space[3]}
                            paddingX={space[2]}
                            paddingY={space[1] / 2}>
                            <ExtraSmallLabel>
                              {t(
                                `fna:lifeStage.concern.priority.${
                                  priority as 0 | 1 | 2
                                }`,
                              )}
                            </ExtraSmallLabel>
                          </Box>
                        ) : null}
                        {item.isCompleted &&
                          typeof item.yearsToAchieve === 'number' && (
                            <SmallBody
                              style={{ marginTop: 2 }}
                              color={colors.palette.fwdGreyDarkest}>
                              ({getDisplayInYear(item, t)})
                            </SmallBody>
                          )}
                      </Row>
                    </Column>
                  </Row>
                  {item.isCompleted || !hasIllustration ? (
                    <Column
                      alignSelf="stretch"
                      alignItems="flex-end"
                      justifyContent="center">
                      <SmallLabel
                        color={
                          item.isCompleted
                            ? colors.palette.fwdGreyDarkest
                            : colors.palette.fwdGreyDark
                        }>
                        {t('fna:savingNeeds')}
                      </SmallLabel>
                      <Row>
                        <ExtraSmallLabel
                          style={{ marginTop: 3 }}
                          color={colors.onBackground}>
                          {t('fna:currency')}
                        </ExtraSmallLabel>
                        <Box w={2} />
                        <LargeLabel
                          fontWeight="bold"
                          color={
                            item.isCompleted
                              ? colors.onBackground
                              : colors.palette.fwdGreyDark
                          }>
                          {item.isCompleted
                            ? currencyKFormat(item.value ?? 0)
                            : '--'}
                        </LargeLabel>
                      </Row>
                    </Column>
                  ) : (
                    <AddButton
                      onPress={() => {
                        navigate('SavingsGoal', { tab: item.id });
                      }}>
                      <Body>{t('fna:add')}</Body>
                    </AddButton>
                  )}
                </Row>
              </TouchableOpacity>
            );
          })}
        </CardContainer>
      </TouchableOpacity>
      <DialogPhone visible={summaryVisible} style={{ paddingRight: space[2] }}>
        <Box>
          <CloseButtonDialog onPress={hideSummary}>
            <Icon.Close fill={colors.onBackground} />
          </CloseButtonDialog>
          <GoalBreakdown
            needType="SAVINGS"
            data={breakdownData}
            hasTooltip
            concerns={fnaState.lifeJourney.concerns}
            adviceType={fnaState.adviceType}
          />
        </Box>
      </DialogPhone>
    </Fragment>
  );
}

const CloseButtonDialog = styled.TouchableOpacity(({ theme }) => ({
  flexDirection: 'row',
  justifyContent: 'flex-end',
  paddingRight: theme.space[2],
}));

const CardContainer = styled(Card)(({ theme }) => ({
  width: '100%',
  backgroundColor: theme.colors.background,
  borderRadius: theme.borderRadius.large,
  padding: theme.space[4],
}));

const BadgeDoneView = styled(TouchableOpacity)(
  ({ theme: { colors, space, borderRadius } }) => ({
    marginTop: -space[8],
    marginRight: -space[4],
    width: 147,
    backgroundColor: colors.primaryVariant2,
    borderColor: colors.primary,
    borderWidth: 1,
    borderRadius: borderRadius.medium,
    borderBottomRightRadius: 0,
    paddingVertical: space[2],
    paddingHorizontal: space[3],
  }),
);

const AddButton = styled.TouchableOpacity(({ theme }) => {
  return {
    borderWidth: 1,
    borderColor: theme.colors.palette.fwdGrey[100],
    borderRadius: 9999,
    paddingVertical: 5.5,
    paddingHorizontal: theme.space[3],
    marginBottom: theme.space[1],
    backgroundColor: theme.colors.background,
  };
});
