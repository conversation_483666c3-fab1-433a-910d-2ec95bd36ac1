import { useTheme } from '@emotion/react';
import { Button } from 'cube-ui-components';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import {
  BottomSheetFooter,
  BottomSheetFooterProps,
  KEYBOARD_STATE,
  useBottomSheetInternal,
} from '@gorhom/bottom-sheet';
import Animated, { useAnimatedStyle } from 'react-native-reanimated';
import { ButtonVariant } from 'cube-ui-components/dist/cjs/components/Button/types';

export interface Props extends BottomSheetFooterProps {
  disabled?: boolean;
  visible?: boolean;
  onPress: () => void;
  keyboardShown?: boolean;
  buttonTitle?: string;
  loading?: boolean;
  subtext?: string;
  secondaryButtonTitle?: string;
  onSecondaryPress?: () => void;
}

export default function FnaConfirmFooter({
  disabled,
  onPress,
  buttonTitle,
  visible,
  loading,
  subtext,
  secondaryButtonTitle,
  onSecondaryPress,
  ...props
}: Props) {
  const { space, colors } = useTheme();
  const { bottom: bottomInset } = useSafeAreaInsets();
  const { t } = useTranslation(['fna']);

  const bottomSheetInternal = useBottomSheetInternal(true);

  const animatedFooterHeight = bottomSheetInternal?.animatedFooterHeight;
  const animatedKeyboardState = bottomSheetInternal?.animatedKeyboardState;

  const [footerHeight, setFooterHeight] = useState(
    Math.max(animatedFooterHeight?.value || 0, 0),
  );

  const animatedStyle = useAnimatedStyle(
    () => ({
      transform: [
        {
          translateY:
            animatedKeyboardState?.value === KEYBOARD_STATE.SHOWN
              ? footerHeight
              : 0,
        },
      ],
    }),
    [animatedKeyboardState?.value, footerHeight],
  );

  const { isNarrowScreen, isWideScreen } = useWindowAdaptationHelpers();

  if (!visible) {
    return null;
  }

  return (
    <BottomSheetFooter {...props}>
      <Animated.View
        onLayout={e => setFooterHeight(e.nativeEvent.layout.height)}
        style={[
          animatedStyle,
          {
            paddingHorizontal: space[isNarrowScreen ? 3 : 4],
            paddingTop: space[4],
            paddingBottom: space[4] + bottomInset,
            backgroundColor: colors.background,
            borderTopWidth: 1,
            borderColor: colors.palette.fwdGrey[100],
            flexDirection: 'row',
            justifyContent: 'center',
            gap: space[4],
          },
        ]}>
        {secondaryButtonTitle && (
          <Button
            text={secondaryButtonTitle}
            onPress={onSecondaryPress}
            style={{ maxWidth: isWideScreen ? 400 : undefined, flex: 1 }}
            variant="secondary"
          />
        )}
        <Button
          text={buttonTitle ?? t('fna:done')}
          disabled={disabled}
          onPress={onPress}
          loading={loading}
          subtext={subtext}
          style={{ maxWidth: isWideScreen ? 400 : undefined, flex: 1 }}
        />
      </Animated.View>
    </BottomSheetFooter>
  );
}
