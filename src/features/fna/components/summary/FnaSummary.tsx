import React, { useEffect, useMemo, useState } from 'react';
import { StyleSheet, useWindowDimensions } from 'react-native';
import styled from '@emotion/native';
import {
  Box,
  Row,
  CubePictogramIcon,
  Checkbox,
  Button,
  Typography,
  H5,
  Column,
} from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@emotion/react';
import SummaryOverview from './SummaryOverview';
import SummaryContent from './SummaryContent';
import { BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { format } from 'date-fns';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { useRiderList } from 'features/productSelection/hooks/useRiderList';
import { useFormatProductListParties } from 'features/productSelection/hooks/useProducts';
import { RiderListResponse } from 'api/proposalApi';
import { RiderGroup } from 'features/fna/constants/riderListConstants';

interface FnaSummaryProps {
  onClose: () => void;
  onCancel: () => void;
  onConfirm: () => void;
  onReview: () => void;
  expanded?: boolean;
  setExpanded?: (expanded: boolean) => void;
}

export default function FnaSummary({
  onClose,
  onCancel,
  onConfirm,
  onReview,
  expanded = false,
  setExpanded,
}: FnaSummaryProps) {
  const { t } = useTranslation(['fna']);
  const today = format(new Date(), 'dd/MM/yyyy');
  const { space, colors, borderRadius } = useTheme();
  const [checked, setChecked] = useState(false);
  const fnaState = useFnaStore();
  const {
    totalInsuranceCompensation,
    recommendedProductType,
    appropriateTypeofAdditionalBenefit,
  } = fnaState.getFnaSummary();

  const { insureds, proposers } = useFormatProductListParties();
  const [riders, setRiders] = useState<RiderListResponse[]>();
  const { mutateAsync: getRiderList } = useRiderList();

  useEffect(() => {
    getRiderList({
      insureds,
      proposers,
    }).then(data => {
      const filteredRiders = data.filter(rider =>
        appropriateTypeofAdditionalBenefit.includes(
          rider.riderGroup as RiderGroup,
        ),
      );
      setRiders(filteredRiders);
    });
  }, [getRiderList]);

  const { height: realScreenHeight, width: screenWidth } =
    useWindowDimensions();
  const screenHeight = expanded ? realScreenHeight : realScreenHeight * 0.9;

  // remove duplicate rider groups
  const riderGroups = useMemo(() => {
    return riders?.reduce((acc, rider) => {
      if (!acc.includes(rider.riderGroup as RiderGroup)) {
        acc.push(rider.riderGroup as RiderGroup);
      }
      return acc;
    }, [] as RiderGroup[]);
  }, [riders]);

  const items = [
    {
      key: 'compensation',
      icon: <CubePictogramIcon.Cash size={44} />,
      title: t('fna:totalInsuranceCompensation'),
      content: (
        <Row>
          <Typography.LargeBody
            color={colors.palette.black}
            style={{
              paddingRight: space[1],
              lineHeight: 20,
              marginTop: 4,
            }}>
            {t('fna:totalInsuranceCompensation.IDR')}
          </Typography.LargeBody>
          <Typography.H5 fontWeight="bold" color={colors.palette.black}>
            {t(`fna:${totalInsuranceCompensation}` as keyof typeof t)}
          </Typography.H5>
        </Row>
      ),
    },
    {
      key: 'product',
      icon: <CubePictogramIcon.ShieldWithStars size={44} />,
      title: t('fna:recommendedProductType'),
      content: (
        <Row>
          <Typography.LargeBody color={colors.palette.black}>
            {t(`fna:${recommendedProductType}` as keyof typeof t)}
          </Typography.LargeBody>
        </Row>
      ),
    },
    {
      key: 'benefits',
      icon: <CubePictogramIcon.ManWithShield size={44} />,
      title: t('fna:appropriateTypeofAdditionalBenefit'),
      content: (
        <Row>
          <Column>
            {!riderGroups || riderGroups.length === 0 ? (
              <Row>
                <Typography.LargeBody color={colors.palette.black}>
                  {t('fna:appropriateTypeofAdditionalBenefit.no')}
                </Typography.LargeBody>
              </Row>
            ) : (
              riderGroups?.map((group, index) => (
                <Row key={index}>
                  <Typography.LargeBody color={colors.palette.black}>
                    {`${index + 1}. ${group}`}
                  </Typography.LargeBody>
                </Row>
              ))
            )}
          </Column>
        </Row>
      ),
    },
  ];

  return (
    <Container width={screenWidth} height={screenHeight}>
      <InnerContainer paddingY={space[6]} paddingX={space[6]}>
        <Header>
          <H5 fontWeight="bold">{t('fna:fnaSummaryResult')}</H5>
        </Header>
        <BottomSheetScrollView style={{ flex: 1, marginBottom: FOOTER_HEIGHT }}>
          <SummaryOverview items={items} />
          <Header>
            <H5 fontWeight="bold">{t('fna:clientAcknowledgement.heading')}</H5>
          </Header>
          <SummaryContent
            content={t('fna:fnaSummaryContent')}
            expanded={expanded}
            setExpanded={setExpanded}
          />
        </BottomSheetScrollView>
        <Footer>
          <CheckboxRow>
            <Checkbox
              checked={checked}
              label={t('fna:fnaSummaryCheckbox')}
              labelStyle={styles.checkboxLabel}
              style={styles.checkbox}
              onChange={() => setChecked(prev => !prev)}
            />
            <CheckboxLabel>
              {t('fna:fnaSummaryCheckboxAcceptedOn', {
                date: today,
              })}
            </CheckboxLabel>
          </CheckboxRow>
          <Actions>
            <Button
              variant="secondary"
              onPress={onReview}
              text={t('fna:reviewPdf')}
              style={[{ minWidth: 200, minHeight: 52 }]}
            />
            <Button
              variant="primary"
              onPress={onConfirm}
              disabled={!checked}
              text={t('fna:confirm')}
              style={[{ minWidth: 200, minHeight: 52 }]}
            />
          </Actions>
        </Footer>
      </InnerContainer>
    </Container>
  );
}

const FOOTER_HEIGHT = 180;

const Header = styled(Row)(({ theme }) => ({
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: theme.space[5],
}));

const CheckboxRow = styled(Box)(({ theme }) => ({
  borderWidth: 1,
  borderColor: theme.colors.palette.fwdGrey[100],
  borderRadius: theme.borderRadius.medium,
  padding: theme.space[4],
  marginVertical: theme.space[4],
}));

const CheckboxLabel = styled(Typography.Label)(({ theme }) => ({
  color: theme.colors.secondaryVariant,
  marginTop: 4,
  marginLeft: theme.space[8],
}));

const Actions = styled(Row)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'row',
  justifyContent: 'flex-end',
  gap: theme.space[4],
}));

const Footer = styled(Box)(({ theme }) => ({
  height: FOOTER_HEIGHT,
  position: 'absolute',
  bottom: 0,
  left: theme.space[6],
  right: theme.space[6],
  marginBottom: theme.space[4],
  backgroundColor: theme.colors.background,
}));

const Container = styled(Box)(({ theme }) => ({
  paddingTop: theme.space[2],
  backgroundColor: theme.colors.palette.fwdOrange[100],
  borderTopLeftRadius: theme.borderRadius.large,
  borderTopRightRadius: theme.borderRadius.large,
}));

const InnerContainer = styled(Box)(({ theme }) => ({
  flex: 1,
  backgroundColor: theme.colors.background,
  borderTopLeftRadius: theme.borderRadius.large,
  borderTopRightRadius: theme.borderRadius.large,
}));

const styles = StyleSheet.create({
  checkbox: {
    alignItems: 'flex-start',
    marginTop: 4,
  },
  checkboxLabel: {
    flex: 1,
  },
});
