import React, { useMemo } from 'react';
import {
  Control,
  UseFormGetValues,
  UseFormSetValue,
  useWatch,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import ApplicationDetailsSectionContainer from '../../../common/ApplicationDetailsSectionContainer';
import { policyOwnerAddressInfoSchema } from 'features/eApp/validations/policyOwnerSchema';
import HomeAddressSVG from 'features/eApp/assets/HomeAddressSVG';
import { AddressInfo } from 'features/customerFactFind/components/personalDetails/addressInformation/AddressInformationForm';
import { useEAppStore } from 'features/eApp/utils/store/eAppStore';
import { Body, Box, Column, H6, Icon, Row } from 'cube-ui-components';
import { useTheme } from '@emotion/react';
import styled from '@emotion/native';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { View } from 'react-native';
import { formatAddress } from 'features/eApp/utils/formatAddress';

interface Props {
  control: Control<AddressInfo>;
  isOwner?: boolean;
}

const AddressInformation = (props: Props) => {
  const { t } = useTranslation(['eApp']);
  const { space, colors } = useTheme();
  const addressValues = useWatch({
    name: [
      'correspondenceAddress',
      'correspondenceAddressLine1',
      'correspondenceAddressLine2',
      'correspondenceAddressLine3',
      'correspondencePostCode',
      'correspondenceCity',
      'correspondenceState',
      'correspondenceCountry',
      'residentialAddress',
      'residentialAddressLine1',
      'residentialAddressLine2',
      'residentialAddressLine3',
      'residentialPostCode',
      'residentialCity',
      'residentialState',
      'residentialCountry',
      'businessAddress',
      'businessAddressLine1',
      'businessAddressLine2',
      'businessAddressLine3',
      'businessPostCode',
      'businessCity',
      'businessState',
      'businessCountry',
    ],
    control: props.control,
  });

  const isDone = useMemo(() => {
    return policyOwnerAddressInfoSchema.isValidSync({
      correspondenceAddress: addressValues[0],
      correspondenceAddressLine1: addressValues[1],
      correspondenceAddressLine2: addressValues[2],
      correspondenceAddressLine3: addressValues[3],
      correspondencePostCode: addressValues[4],
      correspondenceCity: addressValues[5],
      correspondenceState: addressValues[6],
      correspondenceCountry: addressValues[7],
      residentialAddress: addressValues[8],
      residentialAddressLine1: addressValues[9],
      residentialAddressLine2: addressValues[10],
      residentialAddressLine3: addressValues[11],
      residentialPostCode: addressValues[12],
      residentialCity: addressValues[13],
      residentialState: addressValues[14],
      residentialCountry: addressValues[15],
      businessAddress: addressValues[16],
      businessAddressLine1: addressValues[17],
      businessAddressLine2: addressValues[18],
      businessAddressLine3: addressValues[19],
      businessPostCode: addressValues[20],
      businessCity: addressValues[21],
      businessState: addressValues[22],
      businessCountry: addressValues[23],
    });
  }, [addressValues]);

  const { data: optionList } = useGetOptionList();

  const ownerInfo = useEAppStore(state => state.my_policyOwnerPersonalInfo);
  const spouseInfo = useEAppStore(state => state.my_spousePersonalInfo);
  const info = props.isOwner ? ownerInfo : spouseInfo;

  const correspondenceAddress = formatAddress(
    'correspondence',
    info,
    optionList,
  );
  const residentialAddress =
    info.residentialAddress === 'correspondence'
      ? correspondenceAddress
      : formatAddress('residential', info, optionList);
  const businessAddress = formatAddress('business', info, optionList);

  const renderAddressInfo = (label: string, value: string) => {
    return (
      <>
        <Row>
          <Icon.Location fill={colors.palette.black} />
          <H6 fontWeight="bold" style={{ marginLeft: space[1] }}>
            {label}
          </H6>
        </Row>
        <Box mt={space[5]} />
        <Body color={colors.palette.fwdGreyDarker}>{label}</Body>
        <Body color={colors.palette.fwdDarkGreen[100]}>{value}</Body>
      </>
    );
  };

  return (
    <ApplicationDetailsSectionContainer
      title={t('eApp:certificate.addressInfoTitle')}
      icon={<HomeAddressSVG />}
      isDone={isDone}>
      <Content>
        <Box mt={space[5]} />
        {renderAddressInfo(
          t('eApp:certificate.title.correspondenceAddress'),
          correspondenceAddress,
        )}
        <Box mt={space[5]} />
        <Divider />
        <Box mt={space[5]} />
        {renderAddressInfo(
          t('eApp:certificate.title.residentialAddress'),
          residentialAddress,
        )}
        {info.businessAddress !== '' && (
          <>
            <Box mt={space[5]} />
            <Divider />
            <Box mt={space[5]} />
            {renderAddressInfo(
              t('eApp:certificate.title.businessAddress'),
              businessAddress,
            )}
          </>
        )}
      </Content>
    </ApplicationDetailsSectionContainer>
  );
};

const Content = styled(Column)(({ theme: { space, colors } }) => ({
  backgroundColor: colors.background,
  paddingHorizontal: space[6],
}));

export const Divider = styled(View)(({ theme }) => {
  return {
    height: 1,
    backgroundColor: theme.colors.palette.fwdGrey[100],
  };
});

export default AddressInformation;
