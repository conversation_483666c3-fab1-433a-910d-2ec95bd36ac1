import {
  EMandatePaymentStatusResponse,
  PaymentGatewayStatusResponse,
} from 'api/ipay88Api';
import { AxiosError } from 'axios';
import {
  DIRECT_PAYMENT_METHODS,
  EMANDATE_PAYMENT_METHODS,
  PaymentMethod,
} from 'features/eAppV2/ib/components/payment/form/methods/paymentMethodTypes';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCaseManually } from 'hooks/useGetCase';
import { useSubmitApplication } from 'hooks/useSubmitApplication';
import { useCallback } from 'react';
import { Case } from 'types/case';
import { CubeResponse } from 'types/response';
import { country } from 'utils/context';
import { countryModuleSellerConfig } from 'utils/config/module';
import { useForceLogout } from 'hooks/useForceLogout';
import { versionCompare } from 'hooks/useDoVersionCheck';

const DUPLICATED_SUBMISSION_MSG = 'Duplicate submission';

export const useEAppSubmitApplication = () => {
  const {
    mutateAsync: submitApplication,
    isLoading: isSubmittingApplication,
    ...rest
  } = useSubmitApplication();
  const { mutateAsync: getCase, isLoading: isGettingCase } =
    useGetCaseManually();
  const caseId = useBoundStore(state => state.case.caseId);
  const { forceLogout } = useForceLogout();

  const onSubmissionSucceeded = useCallback(
    async (onSuccess?: () => Promise<void> | void) => {
      await onSuccess?.();
    },
    [],
  );

  const onSubmissionFailed = useCallback(
    async (onFailure?: () => Promise<void> | void) => {
      await onFailure?.();
    },
    [],
  );

  const mutateAsync = useCallback(
    async ({
      onSuccess,
      onFailure,
    }: {
      onSuccess?: () => Promise<void> | void;
      onFailure?: () => Promise<void> | void;
    } = {}) => {
      try {
        if (!caseId) return;
        if (countryModuleSellerConfig.isLoggedInUntilAppKilled) {
          const shouldUpdateCurrentVersion = await versionCompare();
          if (shouldUpdateCurrentVersion) {
            await forceLogout();
          }
        }
        const result = await submitApplication(caseId);
        switch (country) {
          case 'id':
          case 'ib':
          case 'my': {
            const caseObj = await getCase(caseId);
            if (country !== 'id') validateMYPayment(caseObj);
            const result = await submitApplication(caseId);
            if (
              result.success === 'true' ||
              result.success === true ||
              Boolean(result.appStatus)
            ) {
              // success
              await onSubmissionSucceeded(onSuccess);
            } else {
              // failure
              await onSubmissionFailed(onFailure);
            }
            break;
          }
          case 'ph':
            if (
              !result.success ||
              result.resultData.responseData.length === 0 ||
              result.resultData.responseData[0].businessNo === null ||
              result.resultData.responseData[0].caseNo === null
            ) {
              // failure
              await onSubmissionFailed(onFailure);
            } else {
              // success
              await onSubmissionSucceeded(onSuccess);
            }
            break;
        }
      } catch (e) {
        if (
          e instanceof AxiosError &&
          e.response &&
          e.response.data &&
          'messageList' in e.response.data &&
          ((
            e.response.data as CubeResponse<null>
          ).messageList?.[0]?.code?.includes(DUPLICATED_SUBMISSION_MSG) ||
            (
              e.response.data as CubeResponse<null>
            ).messageList?.[0]?.content?.includes(DUPLICATED_SUBMISSION_MSG))
        ) {
          await onSubmissionSucceeded(onSuccess);
        } else {
          await onSubmissionFailed(onFailure);
        }
      }
    },
    [
      caseId,
      forceLogout,
      onSubmissionFailed,
      onSubmissionSucceeded,
      submitApplication,
      getCase,
    ],
  );

  return {
    mutateAsync,
    ...rest,
    isLoading: isSubmittingApplication || isGettingCase,
  };
};

const validateMYPayment = (caseObj: Case) => {
  const paymentMethod = caseObj.application?.paymentMethod;
  if (!paymentMethod) throw new Error('Missing payment method');

  // bypassing payment method that did not go through payment gateway
  if (!DIRECT_PAYMENT_METHODS.includes(paymentMethod as PaymentMethod)) {
    return true;
  }

  // check if payment status response exists in case
  const paymentStatusResponse = (
    caseObj.application?.initialPayment as {
      paymentResponse: PaymentGatewayStatusResponse;
    }
  )?.paymentResponse;
  if (!paymentStatusResponse) {
    throw new Error('Missing payment status response');
  }

  // if require eMandate, check for eMandate registration response
  if (EMANDATE_PAYMENT_METHODS.includes(paymentMethod as PaymentMethod)) {
    const eMandateRegistrationResponse = (
      caseObj?.application?.renewalPayment as {
        eMandatePaymentStatusResponse?: EMandatePaymentStatusResponse;
      }
    )?.eMandatePaymentStatusResponse;
    if (!eMandateRegistrationResponse)
      throw new Error('Missing eMandate registration response');
  }

  return true;
};
