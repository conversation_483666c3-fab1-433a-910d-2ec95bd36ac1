import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import SignatureBasePhone from './phone/SignatureBase.phone';
import SignatureBaseTablet from './tablet/SignatureBase.tablet';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useUpdateSignature } from '../../hooks/useUpdateSignature';
import { useGetPartyFromActiveCase, useUpdateParty } from 'hooks/useParty';
import useBoundStore from 'hooks/useBoundStore';
import { Modal } from 'react-native';
import { PortalProvider } from '@gorhom/portal';
import PromptProvider from 'components/prompt/PromptContext';
import { LanguagesKeys } from 'utils/translation';
import { F2FLevel } from '../../types/remoteSellingTypes';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useUpdateCase } from 'hooks/useUpdateCase';
import useToggle from 'hooks/useToggle';
import { BuildCountry } from 'types';
import { formatSignatureDate } from 'utils/helper/formatUtil';
import { TextFieldRef } from 'cube-ui-components';
import { useTranslation } from 'react-i18next';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { country } from 'utils/context';
import { countryModuleEAppConfig, moduleConfigs } from 'utils/config/module';

export type SignatureTerms = Partial<
  Record<LanguagesKeys, { short: string; full: string }>
> & {
  mixed?: boolean;
  skipSignatureTerm?: boolean;
  autoShowSignatureTerm?: boolean;
};

const EMPTY_TERMS: SignatureTerms = {
  en: { short: '', full: '' },
  [country]: { short: '', full: '' },
};

export interface SignatureTab {
  role: string;
  signatureRole: 'agent' | 'insureds' | 'proposers';
  consentSignatureRole?: 'insureds' | 'proposers';
  declarationRole: string;
  consentDeclarationRole?: string;
  name: string;
  partyId: string;
  terms: SignatureTerms;
  customerSeq: string;
  shouldAllowRemoteSellingToggle?: boolean; // for rendering remote selling toggle
  overridingRemoteSelling?: boolean; // to override remoteSelling
  customDrawPad?: React.FC<{
    disabled?: boolean;
    strokes: string[];
    onChange: (strokes: string[]) => void;
    onClearPress: () => void;
  }>;
  shouldByPassVerifySignature?: boolean;
}

interface Props {
  visible: boolean;
  startingIndex: number;
  tabs: SignatureTab[];
  validateSignature: (strokes: string[]) => boolean;
  f2fLevel?: F2FLevel;
  reviewTermsRequired?: boolean;
  defaultPlaceOfSigning?: string;
  placeOfSigningOptions?: { label: string; value: string }[];
  onDismiss?: () => void;
  onDismissByNext?: () => void;
  onFinish?: () => void;
  children?: React.ReactNode;
}

export interface InternalSignatureBaseProps
  extends Pick<
    Props,
    'tabs' | 'reviewTermsRequired' | 'placeOfSigningOptions' | 'onDismiss'
  > {
  activeIndex: number;
  isF2F: boolean;
  isRemoteSelling: boolean;
  activeTab: SignatureTab;
  languages: {
    value: string;
    label: string;
  }[];
  selectedLanguage: string;
  setSelectedLanguage: (lang: string) => void;
  selectedTerms?: {
    short: string;
    full: string;
  };
  onConfirmReviewedTerms: () => void;
  reviewedTerm: boolean;
  isDisplayBothVersions: boolean;
  secondaryShortenDeclarationContent?: string;
  signatureTermVisible: boolean;
  showSignatureTerm: () => void;
  strokes: string[];
  setStrokes: (strokes: string[]) => void;
  onClearPress: () => void;
  drawPadRef: React.MutableRefObject<
    | {
        capture: () => Promise<string | undefined>;
      }
    | undefined
  >;
  placeOfSigning: string;
  setPlaceOfSigning: (place: string) => void;
  placeOfSigningFieldRef: React.MutableRefObject<TextFieldRef | null>;
  formattedSignDate: string;
  isUpdatingRemoteSelling: boolean;
  onToggleRemoteSelling: (isRemoteSelling: boolean) => void;
  isLoading: boolean;
  disabled: boolean;
  onContinue: () => Promise<void>;
}

function _SignatureBase(props: Props) {
  const { t } = useTranslation(['eApp']);
  const { isTabletMode } = useLayoutAdoptionCheck();
  const [activeIndex, setActiveIndex] = useState(props.startingIndex || 0);
  useEffect(() => {
    if (props.visible) {
      setActiveIndex(props.startingIndex || 0);
    }
  }, [props.startingIndex, props.visible]);

  const [reviewedTerms, setReviewedTerms] = useState<boolean[]>([]);
  useEffect(() => {
    if (props.visible) {
      setReviewedTerms(props.tabs.map(() => false));
    }
  }, [props.tabs, props.visible]);

  const onTermReviewed = useCallback(() => {
    setReviewedTerms(reviewedTerms => {
      reviewedTerms[activeIndex] = true;
      return [...reviewedTerms];
    });
  }, [activeIndex]);

  const {
    updateSignature,
    removeSignature,
    updateConsentSignature,
    removeConsentSignature,
  } = useUpdateSignature();
  const caseId = useBoundStore(state => state.case.caseId);
  const { caseObj } = useGetActiveCase();
  const party = useGetPartyFromActiveCase(props.tabs[activeIndex].partyId);
  const { mutateAsync: updateParty } = useUpdateParty();
  const { mutateAsync: updateCase } = useUpdateCase();

  const isRemoteSelling =
    props.tabs[activeIndex]?.overridingRemoteSelling !== undefined
      ? Boolean(props.tabs[activeIndex].overridingRemoteSelling)
      : props?.f2fLevel === 'party'
      ? Boolean(party?.isRemoteSelling)
      : Boolean(caseObj?.isRemoteSelling);

  const onUpdateRemoteSelling = useCallback(
    async (isRemoteSelling: boolean) => {
      if (caseId && party) {
        if (props.f2fLevel === 'party') {
          await updateParty({
            caseId,
            party: {
              ...party,
              isRemoteSelling,
            },
          });
        } else {
          await updateCase({
            caseId,
            isRemoteSelling,
          });
        }
      }
    },
    [caseId, party, props.f2fLevel, updateCase, updateParty],
  );

  const onNext = useCallback(
    async (imageBase64 = '', placeOfSigning: string, signedTime: Date) => {
      if (!props.tabs[activeIndex].shouldByPassVerifySignature && caseId) {
        imageBase64 = imageBase64.replace(/(\r\n|\n|\r)/gm, '');
        const party = caseObj?.parties?.find(
          p => p.id === props.tabs[activeIndex].partyId,
        );
        if (imageBase64 || countryModuleEAppConfig.hasSignaturePlaceholder) {
          // update consent signature if it has consentSignatureRole
          if (props.tabs[activeIndex].consentSignatureRole) {
            await updateConsentSignature({
              signedTime: signedTime.getTime(),
              imageBase64,
              party: props.tabs[activeIndex].consentSignatureRole as
                | 'insureds'
                | 'proposers',
              custSeq: props.tabs[activeIndex].customerSeq,
            });
          } else {
            await updateSignature({
              signedTime: signedTime.getTime(),
              imageBase64,
              party: props.tabs[activeIndex].signatureRole as
                | 'agent'
                | 'insureds'
                | 'proposers',
              custSeq: props.tabs[activeIndex].customerSeq,
              placeOfSignature: placeOfSigning,
            });
          }
        } else {
          if (props.tabs[activeIndex].consentSignatureRole) {
            await removeConsentSignature({
              party: props.tabs[activeIndex].consentSignatureRole as
                | 'insureds'
                | 'proposers',
              custSeq: props.tabs[activeIndex].customerSeq,
            });
          } else {
            await removeSignature({
              party: props.tabs[activeIndex].signatureRole as
                | 'agent'
                | 'insureds'
                | 'proposers',
              custSeq: props.tabs[activeIndex].customerSeq,
            });
          }
        }
        // update party's remote selling in case of f2fLevel = 'case'
        if (party) {
          await updateParty({
            caseId,
            party: {
              ...party,
              isRemoteSelling,
            },
          });
        }
      }
      if (activeIndex === props.tabs.length - 1) {
        if (props?.onDismissByNext) {
          await props?.onDismissByNext();
        } else {
          await props.onDismiss?.();
        }
        await props.onFinish?.();
      } else {
        setActiveIndex(activeIndex + 1);
      }
    },
    [
      activeIndex,
      caseId,
      caseObj?.parties,
      isRemoteSelling,
      props,
      removeSignature,
      updateParty,
      updateSignature,
      updateConsentSignature,
      removeConsentSignature,
    ],
  );

  const activeTab = props.tabs[activeIndex];
  const activeTerms = activeTab.terms || EMPTY_TERMS;
  const isF2F = !isRemoteSelling;
  const languages: { value: string; label: string }[] = useMemo(() => {
    return (Object.keys(activeTerms) as (keyof typeof activeTerms)[])
      .filter(key => typeof activeTerms[key] === 'object')
      .map(value => ({
        value,
        label: t(`eApp:language.${value as LanguagesKeys}`),
      }));
  }, [t, activeTerms]);
  const [selectedLanguage, setSelectedLanguage] = useState(
    () => languages[0].value,
  );
  const selectedTerms = useMemo(() => {
    return activeTerms[selectedLanguage as BuildCountry];
  }, [activeTerms, selectedLanguage]);

  const isDisplayBothVersions = Boolean(activeTerms.mixed);

  const secondaryShortenDeclarationContent = useMemo(() => {
    if (!isDisplayBothVersions) return '';
    const secondLanguage = languages.find(
      e => e.value !== selectedLanguage,
    )?.value;
    const terms = activeTerms[secondLanguage as BuildCountry];
    return terms?.short;
  }, [activeTerms, isDisplayBothVersions, languages, selectedLanguage]);

  const signedDate = useState<Date>(() => new Date())[0];
  const formattedSignDate = useMemo(
    () => formatSignatureDate(signedDate, isTabletMode),
    [isTabletMode, signedDate],
  );

  const [signatureTermVisible, showSignatureTerm, hideSignatureTerm] =
    useToggle();

  useEffect(() => {
    if (!moduleConfigs[country].eAppConfig.maintainSignaturePlaceOfSigning) {
      setPlaceOfSigning(props.defaultPlaceOfSigning || '');
    }
  }, [activeIndex, props.defaultPlaceOfSigning]);

  useEffect(() => {
    if (
      props.visible &&
      props.tabs[activeIndex]?.terms?.autoShowSignatureTerm &&
      !skipAutoShowTermRef.current
    ) {
      showSignatureTerm();
    }

    skipAutoShowTermRef.current = false;
  }, [props.visible, activeIndex, props.tabs, showSignatureTerm]);
  const onConfirmReviewedTerms = useCallback(() => {
    hideSignatureTerm();
    onTermReviewed();
  }, [hideSignatureTerm, onTermReviewed]);
  const skipAutoShowTermRef = useRef(false);
  const drawPadRef = useRef<
    { capture: () => Promise<string | undefined> } | undefined
  >();
  const [strokes, setStrokes] = useState<string[]>([]);
  const [placeOfSigning, setPlaceOfSigning] = useState<string>(
    props.defaultPlaceOfSigning || '',
  );
  const placeOfSigningFieldRef = useRef<TextFieldRef>(null);
  const onClearPress = useCallback(() => {
    setStrokes([]);
  }, []);

  const validateSignature = props.validateSignature;
  const disabled = useMemo(() => {
    if (isRemoteSelling || activeTab.shouldByPassVerifySignature) return false;
    if (!placeOfSigning) return true;

    return !validateSignature(strokes);
  }, [
    isRemoteSelling,
    activeTab.shouldByPassVerifySignature,
    placeOfSigning,
    validateSignature,
    strokes,
  ]);

  const [isLoading, showLoading, hideLoading] = useToggle();
  const onContinue = useCallback(async () => {
    showLoading();
    if (activeIndex === props.tabs.length - 1) {
      skipAutoShowTermRef.current = true;
    }
    try {
      const imgBase64 = (await drawPadRef.current?.capture()) || '';
      console.log('=>(SignatureBase.tsx:307) signedDate', signedDate);
      console.log('=>(SignatureBase.tsx:308) placeOfSigning', placeOfSigning);
      await onNext(imgBase64, placeOfSigning, signedDate);
      if (activeIndex !== props.tabs.length - 1) {
        setStrokes([]);
      }
      placeOfSigningFieldRef?.current?.blur();
    } finally {
      hideLoading();
    }
  }, [
    activeIndex,
    hideLoading,
    onNext,
    placeOfSigning,
    props.tabs.length,
    showLoading,
    signedDate,
  ]);

  const [
    isUpdatingRemoteSelling,
    showUpdatingRemoteSelling,
    hideUpdatingRemoteSelling,
  ] = useToggle();
  const onToggleRemoteSelling = useCallback(
    async (remoteSelling: boolean) => {
      showUpdatingRemoteSelling();
      try {
        await onUpdateRemoteSelling?.(remoteSelling);
      } finally {
        hideUpdatingRemoteSelling();
      }
    },
    [
      hideUpdatingRemoteSelling,
      onUpdateRemoteSelling,
      showUpdatingRemoteSelling,
    ],
  );

  const Component = isTabletMode ? SignatureBaseTablet : SignatureBasePhone;
  return (
    <Component
      tabs={props.tabs}
      activeIndex={activeIndex}
      isF2F={isF2F}
      isRemoteSelling={isRemoteSelling}
      activeTab={activeTab}
      languages={languages}
      selectedLanguage={selectedLanguage}
      setSelectedLanguage={setSelectedLanguage}
      selectedTerms={selectedTerms}
      onConfirmReviewedTerms={onConfirmReviewedTerms}
      reviewTermsRequired={props.reviewTermsRequired}
      reviewedTerm={reviewedTerms[activeIndex]}
      isDisplayBothVersions={isDisplayBothVersions}
      secondaryShortenDeclarationContent={secondaryShortenDeclarationContent}
      signatureTermVisible={signatureTermVisible}
      showSignatureTerm={showSignatureTerm}
      strokes={strokes}
      setStrokes={setStrokes}
      onClearPress={onClearPress}
      drawPadRef={drawPadRef}
      placeOfSigningOptions={props.placeOfSigningOptions}
      placeOfSigning={placeOfSigning}
      setPlaceOfSigning={setPlaceOfSigning}
      placeOfSigningFieldRef={placeOfSigningFieldRef}
      formattedSignDate={formattedSignDate}
      isUpdatingRemoteSelling={isUpdatingRemoteSelling}
      onToggleRemoteSelling={onToggleRemoteSelling}
      isLoading={isLoading}
      disabled={disabled}
      onContinue={onContinue}
      onDismiss={props.onDismiss}
    />
  );
}

export default function SignatureBase({ children, ...props }: Props) {
  return (
    <Modal
      supportedOrientations={['landscape']}
      visible={props.visible}
      transparent
      statusBarTranslucent
      animationType="slide">
      <SafeAreaProvider>
        <PortalProvider>
          <PromptProvider>
            <_SignatureBase {...props} />
            {children}
          </PromptProvider>
        </PortalProvider>
      </SafeAreaProvider>
    </Modal>
  );
}
