import { KeyboardAvoidingView, Platform, ScrollView } from 'react-native';
import { InternalSignatureBaseProps } from '../SignatureBase';
import {
  SafeAreaView,
  SafeAreaViewProps,
  useSafeAreaFrame,
} from 'react-native-safe-area-context';
import styled from '@emotion/native';
import {
  Body,
  Box,
  Button,
  Icon,
  LargeBody,
  Picker,
  Row,
  Switch,
  TextField,
  Typography,
} from 'cube-ui-components';
import Animated, { FadeIn, FadeOut } from 'react-native-reanimated';
import { StyleSheet } from 'react-native';
import Dots from '../Dots';
import IconSignature from './IconSignature';
import { useTranslation } from 'react-i18next';
import React from 'react';
import { useTheme } from '@emotion/react';
import DrawPad from './DrawPad';
import SignatureTermAndConditionModal from './SignatureTermAndConditionModal';
import AutocompletePopup from 'components/AutocompletePopup';
import { getOptionListLabel, getOptionListValue } from 'constants/optionList';
import MarkdownText from 'components/MarkdownText';
import { useSignatureTermStyle } from '../useSignatureTermStyle';

export default function SignatureBaseTablet({
  tabs,
  activeIndex,
  isF2F,
  isRemoteSelling,
  activeTab,
  languages,
  selectedLanguage,
  setSelectedLanguage,
  selectedTerms,
  onConfirmReviewedTerms,
  reviewTermsRequired,
  reviewedTerm,
  isDisplayBothVersions,
  secondaryShortenDeclarationContent,
  signatureTermVisible,
  showSignatureTerm,
  strokes,
  setStrokes,
  onClearPress,
  drawPadRef,
  placeOfSigningOptions,
  placeOfSigning,
  setPlaceOfSigning,
  placeOfSigningFieldRef,
  formattedSignDate,
  isUpdatingRemoteSelling,
  onToggleRemoteSelling,
  isLoading,
  disabled,
  onContinue,
  onDismiss,
}: InternalSignatureBaseProps) {
  const { t } = useTranslation(['eApp']);
  const { colors, space, borderRadius } = useTheme();
  const { height: screenHeight } = useSafeAreaFrame();
  const { fullTextStyle, shortTextStyle, secondaryShortTextStyle } =
    useSignatureTermStyle();

  const modelTitle = activeTab?.consentDeclarationRole
    ? t('eApp:consentSignature.declaration', {
        role: activeTab.declarationRole,
      })
    : t('eApp:signature.declaration', {
        role: activeTab.declarationRole,
      });

  return (
    <Backdrop
      renderToHardwareTextureAndroid
      entering={FadeIn}
      exiting={FadeOut}>
      <KeyboardAvoidingViewContainer
        behavior={Platform.select({
          android: undefined,
          ios: 'position',
        })}>
        <Container height={screenHeight}>
          <Content>
            {isF2F && (
              <DotContainer>
                <Dots total={tabs.length} activeIndex={activeIndex} />
              </DotContainer>
            )}

            <HeaderContainer>
              <IconSignature />

              <Header>
                <HeaderTitle numberOfLines={1} fontWeight="bold">
                  {t('eApp:signature.title', {
                    name: activeTab.name,
                    role: activeTab.role,
                  })}
                </HeaderTitle>

                {isRemoteSelling && languages.length > 1 && (
                  <RemoteSellingLanguagePicker
                    value={selectedLanguage}
                    onChange={setSelectedLanguage}
                    type="chip"
                    items={languages}
                  />
                )}
              </Header>
            </HeaderContainer>

            {isRemoteSelling ? (
              <>
                <Box
                  borderRadius={borderRadius.small}
                  py={14}
                  px={space[4]}
                  mb={space[4]}
                  bgColor={colors.primaryVariant3}>
                  <LargeBody color={colors.primary}>
                    {t('eApp:signature.remoteSellingNote')}
                  </LargeBody>
                </Box>
                <ScrollView>
                  <MarkdownText style={fullTextStyle}>
                    {selectedTerms?.full || ''}
                  </MarkdownText>
                </ScrollView>
              </>
            ) : (
              <>
                <MarkdownText style={shortTextStyle}>
                  {selectedTerms?.short || ''}
                </MarkdownText>
                {isDisplayBothVersions && secondaryShortenDeclarationContent ? (
                  <MarkdownText style={secondaryShortTextStyle}>
                    {secondaryShortenDeclarationContent}
                  </MarkdownText>
                ) : (
                  !activeTab.terms?.skipSignatureTerm && (
                    <NoticeContainer onPress={showSignatureTerm}>
                      <DetailText fontWeight="bold">
                        {t('eApp:signature.viewTerms')}
                      </DetailText>

                      <Icon.ChevronRight size={20} fill={colors.primary} />
                    </NoticeContainer>
                  )
                )}

                {activeTab.customDrawPad ? (
                  React.createElement(activeTab.customDrawPad, {
                    disabled: reviewTermsRequired && !reviewedTerm,
                    strokes,
                    onChange: setStrokes,
                    onClearPress,
                  })
                ) : (
                  <DrawPad
                    ref={drawPadRef}
                    strokes={strokes}
                    onChange={setStrokes}
                    onClearPress={onClearPress}
                    disabled={reviewTermsRequired && !reviewedTerm}
                  />
                )}

                <PlaceAndDateContainer pointerEvents="box-none">
                  {placeOfSigningOptions ? (
                    <AutocompletePopup
                      searchable
                      label={t('eApp:signature.placeOfSigning')}
                      style={styles.placeOfSigning}
                      value={placeOfSigning}
                      onChange={setPlaceOfSigning}
                      highlight={!placeOfSigning}
                      data={placeOfSigningOptions}
                      getItemLabel={getOptionListLabel}
                      getItemValue={getOptionListValue}
                    />
                  ) : (
                    <TextField
                      ref={placeOfSigningFieldRef}
                      label={t('eApp:signature.placeOfSigning')}
                      style={styles.placeOfSigning}
                      value={placeOfSigning}
                      onChange={setPlaceOfSigning}
                      disableFullscreenUI
                      highlight={!placeOfSigning}
                    />
                  )}

                  <NameDate>
                    {t('eApp:signature.signatureDate', {
                      role: activeTab.role,
                      name: activeTab.name,
                      signDate: formattedSignDate,
                    })}
                  </NameDate>
                </PlaceAndDateContainer>
              </>
            )}

            <Footer>
              {activeTab.shouldAllowRemoteSellingToggle && (
                <Row gap={space[2] + 2} alignItems="center">
                  <RemoteText>{t('eApp:signature.remoteSignature')}</RemoteText>
                  <Switch
                    label={
                      isRemoteSelling
                        ? t('eApp:signature.remoteSignature.on')
                        : t('eApp:signature.remoteSignature.off')
                    }
                    disabled={isUpdatingRemoteSelling}
                    checked={isRemoteSelling}
                    onChange={onToggleRemoteSelling}
                    labelStyle={[
                      styles.label,
                      {
                        color: isRemoteSelling
                          ? colors.primary
                          : colors.onBackground,
                      },
                    ]}
                  />
                </Row>
              )}
              <Box flex={1} />
              <Row gap={space[4]}>
                <CancelButton
                  text={t('eApp:cancel')}
                  variant="secondary"
                  onPress={onDismiss}
                  disabled={isLoading}
                />
                <NextButton
                  isRemoteSelling={isRemoteSelling}
                  disabled={disabled}
                  text={
                    isRemoteSelling
                      ? t('eApp:signature.agreeAndContinue')
                      : t('eApp:signature.next')
                  }
                  onPress={onContinue}
                  loading={isLoading}
                />
              </Row>
            </Footer>
          </Content>
        </Container>
      </KeyboardAvoidingViewContainer>
      <SignatureTermAndConditionModal
        visible={signatureTermVisible}
        title={modelTitle}
        content={selectedTerms?.full || ''}
        onConfirm={onConfirmReviewedTerms}
        languages={languages}
        language={selectedLanguage}
        onChangeLanguage={setSelectedLanguage}
      />
    </Backdrop>
  );
}

const Container = styled.View<SafeAreaViewProps & { height: number }>(
  ({ theme, height }) => ({
    backgroundColor: theme.colors.primary,
    paddingTop: 6,
    borderTopLeftRadius: theme.borderRadius.large,
    borderTopRightRadius: theme.borderRadius.large,
    flex: 1,
    marginTop: theme.space[17],
    height: height - theme.space[17],
  }),
);

const HeaderContainer = styled.View(({ theme }) => ({
  flexDirection: 'row',
  alignItems: 'center',
  marginBottom: theme.space[4],
}));

const Header = styled.View(({ theme }) => ({
  flex: 1,
  alignItems: 'center',
  justifyContent: 'space-between',
  flexDirection: 'row',
  marginLeft: theme.space[3],
}));

const HeaderTitle = styled(Typography.Text)(({ theme }) => ({
  color: theme.colors.onBackground,
  fontSize: 24,
  lineHeight: 30.36,
}));

const NoticeContainer = styled.TouchableOpacity(({ theme }) => ({
  marginTop: theme.space[2],
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'flex-start',
}));

const DetailText = styled(Typography.Body)(({ theme }) => ({
  color: theme.colors.primary,
}));

const Footer = styled.View(({ theme }) => ({
  marginTop: theme.space[2],
  marginBottom: theme.space[6],
  flexDirection: 'row',
  alignItems: 'center',
}));

const Backdrop = styled(Animated.View)(() => ({
  ...StyleSheet.absoluteFillObject,
  backgroundColor: 'rgba(0,0,0,0.5)',
  justifyContent: 'center',
  alignItems: 'center',
}));

const DotContainer = styled.View(({ theme }) => ({
  justifyContent: 'center',
  marginTop: theme.space[4],
  marginBottom: theme.space[6],
}));

const Content = styled.View(({ theme }) => {
  const { width } = useSafeAreaFrame();
  return {
    backgroundColor: theme.colors.background,
    flex: 1,
    paddingHorizontal: theme.space[10],
    paddingTop: theme.space[4],
    borderTopLeftRadius: theme.borderRadius.large,
    borderTopRightRadius: theme.borderRadius.large,
    width,
  };
});

const KeyboardAvoidingViewContainer = styled(KeyboardAvoidingView)(() => ({
  flex: 1,
}));

const PlaceAndDateContainer = styled.View(({ theme }) => ({
  height: 66,
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'space-between',
  bottom: theme.space[6],
  left: 0,
  right: 0,
}));

const NameDate = styled(Typography.Body)(({ theme }) => ({
  color: theme.colors.palette.fwdDarkGreen[100],
}));

const RemoteSellingLanguagePicker = styled(Picker)({
  justifyContent: 'flex-end',
});

const CancelButton = styled(Button)({
  width: 100,
});

const NextButton = styled(Button)<{ isRemoteSelling?: boolean }>(
  ({ isRemoteSelling }) => ({
    width: isRemoteSelling ? 171 : 162,
  }),
);

const RemoteText = styled(Body)(({ theme, disabled }) => ({
  color: theme.colors.secondaryVariant,
  opacity: disabled ? 0.5 : 1,
}));

const styles = StyleSheet.create({
  label: {
    fontFamily: 'FWDCircularTT-Bold',
    fontSize: 14,
    lineHeight: 14 * 1.25,
    marginLeft: 6,
  },
  placeOfSigning: {
    width: 340,
    height: 56,
  },
});
