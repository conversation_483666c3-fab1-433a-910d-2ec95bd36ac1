import React, {
  forwardRef,
  useContext,
  useImperativeHandle,
  useMemo,
} from 'react';

type OcrInputListenerProps = {
  onChange?: () => void;
  children?: React.ReactNode;
};

type OcrInputListenerContextType = {
  onChange?: () => void;
};

const OcrInputListenerContext =
  React.createContext<OcrInputListenerContextType>({});

const OcrInputListener = forwardRef(
  (
    { children, ...props }: OcrInputListenerProps,
    ref: React.ForwardedRef<unknown>, // ref is not consumed by radio group, passing ref to remove warning
  ) => {
    const context = useMemo(() => props, [props]);
    useImperativeHandle(ref, () => null);

    return (
      <OcrInputListenerContext.Provider value={context}>
        {children}
      </OcrInputListenerContext.Provider>
    );
  },
);

const useOcrInputListenerContext = () => useContext(OcrInputListenerContext);

export default OcrInputListener;
export { useOcrInputListenerContext };
