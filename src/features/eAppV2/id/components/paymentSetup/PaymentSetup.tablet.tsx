import { useTheme } from '@emotion/react';
import { Box, Row } from 'cube-ui-components';
import TabletSectionsV2, {
  TabletSectionsProps,
} from 'features/eAppV2/common/components/TabletSectionsV2';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import { useSaveApplicationProgress } from 'hooks/useApplicationProgress';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import RenewalPaymentSetupTablet from './RenewalPaymentSetup.tablet';
import WithdrawalPaymentTablet from './WithdrawalPayment.tablet';

enum ActivePath {
  RENEWAL_PREMIUM_PAYMENT = 'renewalPremiumPayment',
  WITHDRAWAL_PAYMENT = 'withdrawalPayment',
}

export default function PaymentSetupTablet() {
  const { t } = useTranslation(['eApp']);
  const [activePath, setActivePath] = useState<string>(
    ActivePath.RENEWAL_PREMIUM_PAYMENT,
  );
  const { colors, space } = useTheme();

  const sections = useMemo(
    () =>
      [
        {
          name: ActivePath.RENEWAL_PREMIUM_PAYMENT,
          title: t('eApp:paymentSetup.renewalPremiumPayment'),
        },
        {
          name: ActivePath.WITHDRAWAL_PAYMENT,
          title: t('eApp:paymentSetup.withdrawalPayment'),
        },
      ].filter(Boolean) as TabletSectionsProps['items'],
    [t],
  );

  const nextGroup = useEAppProgressBarStore(state => state.nextGroup);
  const updateProgress = useSaveApplicationProgress();

  return (
    <Box flex={1}>
      <Row flex={1} pl={space[8]} bgColor={colors.surface} gap={space[6]}>
        <TabletSectionsV2
          activePath={activePath}
          setActivePath={setActivePath}
          items={sections}
        />
        {activePath === ActivePath.RENEWAL_PREMIUM_PAYMENT && (
          <RenewalPaymentSetupTablet
            onNext={() => setActivePath(ActivePath.WITHDRAWAL_PAYMENT)}
          />
        )}
        {activePath === ActivePath.WITHDRAWAL_PAYMENT && (
          <WithdrawalPaymentTablet onNext={() => nextGroup(true)} />
        )}
      </Row>
    </Box>
  );
}
