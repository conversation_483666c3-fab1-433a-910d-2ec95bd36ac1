import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import MarkdownText from 'components/MarkdownText';
import { Column, H6 } from 'cube-ui-components';
import EAppFooterTablet from 'features/eAppV2/common/components/footer/EAppFooter.tablet';
import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import { useOcrImage } from 'features/eAppV2/common/hooks/useOcrImage';
import { useSaveOcrImage } from 'features/eAppV2/common/hooks/useSaveOcrImage';
import useOcrLogic, { OCRForm } from 'features/eAppV2/id/hooks/useOcrLogic';
import {
  partyToPolicyInsuredInfo,
  PolicyInsuredInfoForm,
  policyInsuredInfoSchema,
} from 'features/eAppV2/id/validations/applicationDetails/policyInsured/policyInsuredValidation';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useGetPopulateCase } from 'hooks/useGetPopulateCase';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import {
  FormProvider,
  useForm,
  UseFormGetValues,
  UseFormSetValue,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { Case } from 'types/case';
import { PartyRole } from 'types/party';
import { useYupResolver } from 'utils/validation/useYupResolver';
import { usePartySubmission } from '../../../hooks/usePartySubmission';
import DukcapilValidationPopup from '../../DukcapilValidationPopup';
import OcrInputListener from '../../OcrInputListener';
import ApplicationsModal from '../sections/PopulatableApplications/ApplicationsModal';
import AddressInfo from './addressInfo/AddressInfo.tablet';
import ContactDetailsTablet from './contactDetails/ContactDetails.tablet';
import IdentityVerificationTablet from './identityVerification/IdentityVerification.tablet';
import OccupationDetailsTablet from './occupationDetails/OccupationDetails.tablet';
import PersonalDetails from './personalDetails/PersonalDetails.tablet';

interface PolicyInsuredFormProps {
  onNext: () => void;
}

// create the form here
export default function PolicyInsuredForm({ onNext }: PolicyInsuredFormProps) {
  const { space } = useTheme();
  const { t } = useTranslation('eApp');
  const [appsModalVisible, setAppsModalVisible] = useState(false);

  const [populatableApplications] = useState<Case[]>([]);

  const { data: optionList, isLoading: optionListIsLoading } =
    useGetOptionList<'id'>();
  const { data: agentProfile, isLoading: agentProfileIsLoading } =
    useGetAgentProfile();
  const { caseObj } = useGetActiveCase();
  const policyInsuredParty = useMemo(
    () => caseObj?.parties?.find(p => p.roles.includes(PartyRole.INSURED)),
    [caseObj?.parties],
  );
  // Initialize React Hook Form
  const resolver = useYupResolver(policyInsuredInfoSchema);
  const useFormMethods = useForm<PolicyInsuredInfoForm>({
    mode: 'onBlur',
    defaultValues: useMemo(
      () => partyToPolicyInsuredInfo(policyInsuredParty, optionList),
      [], //eslint-disable-line react-hooks/exhaustive-deps
    ),
    resolver,
    context: {
      optionList: optionList,
      agentId: agentProfile?.person?.idNumber,
      agentMobileNumber: agentProfile?.contact?.mobilePhone,
      agentEmail: agentProfile?.contact?.email,
    },
  });
  const {
    control,
    watch,
    handleSubmit,
    trigger,
    setValue,
    getValues,
    setError,
    formState: { isValid, isSubmitting },
  } = useFormMethods;

  const { isGettingPopulatableApplications, setPopulateCaseId } =
    useGetPopulateCase({
      ownerParty: policyInsuredParty,
      formTrigger: trigger,
    });
  const isInitiating =
    optionListIsLoading ||
    agentProfileIsLoading ||
    isGettingPopulatableApplications;
  // Handle form submission
  const { onSave, dukcapilResult, resetDukcapilResult } = usePartySubmission({
    role: PartyRole.INSURED,
    setError,
  });

  const { saveOcrImage } = useSaveOcrImage();

  const onSubmit = async () => {
    await handleSubmit(async data => {
      const id = await onSave({
        values: data,
        isOcrSuccess: owbOcrFlag,
        isLivenessCheckVerified: isLivenessCheckVerified,
        isFaceMatched: isFaceMatched,
      });

      if (id) {
        if (isOCREnabled) await saveOcrImage(id, ocrFile, PartyRole.INSURED);
        onNext();
      }
    })();
  };

  const scrollRef = useRef<KeyboardAwareScrollView>(null);
  const { focusOnNextIncompleteField, totalIncompleteRequiredFields } =
    useIncompleteFields({
      control: control,
      schema: policyInsuredInfoSchema,
      watch: watch,
      scrollRef,
      scrollTo: option =>
        scrollRef.current?.scrollToPosition?.(0, option.y || 0, true),
    });
  useEffect(() => {
    return () => {
      // warn the user the form is not yet saved
    };
  }, []);
  const [isFaceMatched, setIsFaceMatched] = useState<boolean | undefined>(
    undefined,
  );
  const [isLivenessCheckVerified, setIsLivenessCheckVerified] = useState<
    boolean | undefined
  >(undefined);
  const ocrImage = useOcrImage(policyInsuredParty?.id);
  const { ocrLogic, isOCREnabled, ocrFile, owbOcrFlag, hasMismatchFields } =
    useOcrLogic({
      role: PartyRole.INSURED,
      getValues: getValues as unknown as UseFormGetValues<OCRForm>,
      setValue: setValue as unknown as UseFormSetValue<OCRForm>,
      ocrImage,
      defaultIsOcrSuccess: policyInsuredParty?.person?.isOcrSuccess,
    });

  return (
    <FormProvider {...useFormMethods}>
      <OcrInputListener onChange={ocrLogic.onOcrFieldUpdate}>
        <ScrollViewContainer
          ref={scrollRef}
          contentContainerStyle={{ paddingBottom: space[30] }}>
          <Column gap={space[4]}>
            <H6 fontWeight="bold">{t('insuredInfo')}</H6>
            <MarkdownText>{t('insuredInfo.declaration')}</MarkdownText>
          </Column>
          {isOCREnabled && (
            <IdentityVerificationTablet
              role={PartyRole.PROPOSER}
              isFaceMatched={isFaceMatched}
              onSetIsFaceMatched={setIsFaceMatched}
              isLivenessCheckVerified={isLivenessCheckVerified}
              onSetIsLivenessCheckVerified={setIsLivenessCheckVerified}
              ocrLogic={ocrLogic}
            />
          )}
          <PersonalDetails />
          <OccupationDetailsTablet />
          <ContactDetailsTablet />
          <AddressInfo role="main-party" enableCorrespondenceSelector={true} />
        </ScrollViewContainer>
        <EAppFooterTablet
          progressLock="appDetail-policyOwner"
          primaryDisabled={
            isOCREnabled
              ? !(
                  isValid &&
                  !hasMismatchFields &&
                  ocrLogic.isOcrSuccess !== undefined
                )
              : !isValid
          }
          primaryLabel={t('next')}
          onPrimaryPress={onSubmit}
          primaryLoading={isSubmitting}
          focusOnIncompleteField={focusOnNextIncompleteField}
          totalIncompleteRequiredFields={totalIncompleteRequiredFields}
          isAbsolutePositioned
        />
        <ApplicationsModal
          isVisible={appsModalVisible}
          onClose={() => setAppsModalVisible(false)}
          onConfirm={setPopulateCaseId}
          applications={populatableApplications}
          owner={policyInsuredParty}
        />
        <DukcapilValidationPopup
          visible={dukcapilResult?.code === 'NOT_ALLOW_PROCEED'}
          onClose={resetDukcapilResult}
          result={dukcapilResult}
        />
      </OcrInputListener>
    </FormProvider>
  );
}

const ScrollViewContainer = styled(KeyboardAwareScrollView)(
  ({ theme: { space, colors } }) => ({
    flex: 1,
    paddingRight: space[8],
    paddingTop: space[6],
    backgroundColor: colors.surface,
  }),
);
