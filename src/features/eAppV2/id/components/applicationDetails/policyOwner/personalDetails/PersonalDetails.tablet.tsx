import { useTheme } from '@emotion/react';
import Autocomplete from 'components/Autocomplete';
import IdN<PERSON>ber<PERSON>ield from 'components/IdNumberField';
import Input from 'components/Input';
import {
  getOptionListLabel,
  getOptionListValue,
  ID_COUNTRY,
  IDN_ID_TYPE,
} from 'constants/optionList';
import { Box, PictogramIcon, TextField } from 'cube-ui-components';
import ReadOnlyField from 'features/eApp/components/tablet/common/ReadOnlyField';
import ApplicationDetailsTabletSectionContainer from 'features/eAppV2/common/components/ApplicationDetailsTabletSectionContainer';
import { eAppCommonStyles } from 'features/eAppV2/common/constants/eAppCommonStyles';
import useSchemaValid from 'features/eAppV2/common/hooks/useSchemaValid';
import { MINOR_AGE_THRESHOLD } from 'features/eAppV2/id/constants/partyConfig';
import {
  mainPartyPersonalDetailsDefaultValue,
  mainPartyPersonalDetailsSchema,
} from 'features/eAppV2/id/validations/applicationDetails/sections/mainPartyPersonalDetails';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import { useGetLeadByLeadId } from 'hooks/useGetLeads';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useEffect, useMemo } from 'react';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  Country,
  MaritalStatus,
  Nationality,
  Religion,
} from 'types/optionList';
import { PartyRole } from 'types/party';
import { calculateAge } from 'utils/helper/calculateAge';
import { dateFormatUtil } from 'utils/helper/formatUtil';
import { InputContainerTablet } from '../../../InputContainerTablet';
import { useOcrInputListenerContext } from '../../../OcrInputListener';

export default function PersonalDetails() {
  const { t } = useTranslation('eApp');
  const { space } = useTheme();
  const { data: optionList } = useGetOptionList<'id'>();

  const { control, watch, setValue } = useFormContext();
  const role = watch('personalDetailsRole');
  const isDone = useSchemaValid(
    control,
    mainPartyPersonalDetailsDefaultValue,
    mainPartyPersonalDetailsSchema,
  );

  const formValues = watch();
  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId || '');
  const { data: lead } = useGetLeadByLeadId(
    caseObj?.parties?.find(p => p.roles.includes(PartyRole.PROPOSER))?.leadId,
  );
  const leadRecordFromAffiliate = lead?.leads.filter(
    leadRecord => leadRecord.sourceId === 'Affiliate',
  );
  const campaignCode = leadRecordFromAffiliate?.[0]?.campaignCode;
  const affiliateCode = leadRecordFromAffiliate?.[0]?.extra?.['affiliate_code'];

  const ocrInputListener = useOcrInputListenerContext();
  useEffect(() => {
    if (campaignCode) {
      setValue('campaignCode', campaignCode);
    }
    if (affiliateCode) {
      setValue('affiliateCode', affiliateCode);
    }
  }, [setValue, campaignCode, affiliateCode]);

  const primaryIdType = watch('primaryIdType');
  const age = calculateAge(formValues.dob);

  const genderLabel = useMemo(
    () =>
      optionList?.GENDER.options.find(o => o.value === formValues.gender)
        ?.label,
    [formValues.gender, optionList?.GENDER.options],
  );

  const titleLabel = useMemo(
    () =>
      optionList?.CUBE_TITLE.options.find(o => o.value === formValues.title)
        ?.label,
    [formValues.title, optionList?.CUBE_TITLE.options],
  );

  const idTypeLabel = useMemo(
    () =>
      optionList?.IDTYPE.options.find(o => o.value === primaryIdType)?.label,
    [primaryIdType, optionList?.IDTYPE.options],
  );

  useEffect(() => {
    if (formValues.nationality === ID_COUNTRY) {
      if (role === 'insured' && age < MINOR_AGE_THRESHOLD) {
        setValue('primaryIdType', IDN_ID_TYPE.BirthCertificate);
      } else setValue('primaryIdType', IDN_ID_TYPE.KTP);
    } else {
      setValue('primaryIdType', IDN_ID_TYPE.Passport);
    }
  }, [setValue, formValues.nationality, role, age]);

  const idLabel =
    primaryIdType === IDN_ID_TYPE.KTP
      ? t('idNumber')
      : primaryIdType === IDN_ID_TYPE.BirthCertificate
      ? t('birthCertificateNumber')
      : t('passportNumber');

  return (
    <ApplicationDetailsTabletSectionContainer
      title={t('applicationDetails.personalDetails')}
      icon={<PictogramIcon.ManWithShield size={40} />}
      isDone={isDone}>
      <Box paddingX={space[4]} flexWrap="wrap" flexDirection="row">
        <InputContainerTablet readOnly>
          <ReadOnlyField label={t('title')} value={titleLabel} />
        </InputContainerTablet>
        <InputContainerTablet readOnly>
          <ReadOnlyField label={t('fullName')} value={formValues.fullName} />
        </InputContainerTablet>
        <InputContainerTablet readOnly>
          <ReadOnlyField
            label={t('dateOfBirth')}
            value={
              formValues.dob
                ? `${dateFormatUtil(formValues.dob)} (${age} y.o.)`
                : ''
            }
          />
        </InputContainerTablet>
        <InputContainerTablet readOnly>
          <ReadOnlyField label={t('gender')} value={genderLabel} />
        </InputContainerTablet>
        <InputContainerTablet>
          <Input
            control={control}
            as={Autocomplete<MaritalStatus, string>}
            name="maritalStatus"
            label={t('maritalStatus')}
            data={optionList?.MARITAL_STATUS?.options || []}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            shouldHighlightOnUntouched={Input.defaultHighlightCheck}
            style={eAppCommonStyles.tabletTextField}
            onChange={() => ocrInputListener?.onChange?.()} // on OCR field update
          />
        </InputContainerTablet>
        <InputContainerTablet>
          <Input
            control={control}
            as={Autocomplete<Religion, string>}
            name="religion"
            data={optionList?.RELIGION?.options || []}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            label={t('religion')}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheck}
            onChange={() => ocrInputListener?.onChange?.()} // on OCR field update
          />
        </InputContainerTablet>
        {role === 'owner' && (
          <InputContainerTablet>
            <Input
              control={control}
              as={TextField}
              name="motherMaidenName"
              label={t('motherMaidenName')}
              shouldHighlightOnUntouched={Input.defaultHighlightCheck}
              style={eAppCommonStyles.tabletTextField}
            />
          </InputContainerTablet>
        )}
        <InputContainerTablet>
          <Input
            control={control}
            as={Autocomplete<Nationality, string>}
            name="nationality"
            data={optionList?.COUNTRY?.options || []}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            label={t('nationality')}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheck}
            onChange={() => ocrInputListener?.onChange?.()} // on OCR field update
          />
        </InputContainerTablet>
        <InputContainerTablet>
          <ReadOnlyField label={t('idType')} value={idTypeLabel} />
        </InputContainerTablet>
        <InputContainerTablet>
          <Input
            control={control}
            as={IdNumberField}
            name="primaryId"
            idType={formValues.primaryIdType}
            label={idLabel}
            shouldHighlightOnUntouched={Input.defaultHighlightCheck}
            style={eAppCommonStyles.tabletTextField}
            onChange={() => ocrInputListener?.onChange?.()} // on OCR field update
          />
        </InputContainerTablet>
        <InputContainerTablet>
          <Input
            control={control}
            as={Autocomplete<Country, string>}
            name="countryOfBirth"
            data={optionList?.COUNTRY?.options || []}
            getItemLabel={getOptionListLabel}
            getItemValue={getOptionListValue}
            label={t('countryOfBirth')}
            style={eAppCommonStyles.tabletTextField}
            shouldHighlightOnUntouched={Input.defaultHighlightCheck}
          />
        </InputContainerTablet>
        <InputContainerTablet>
          <Input
            control={control}
            as={TextField}
            name="placeOfBirth"
            label={t('placeOfBirth')}
            shouldHighlightOnUntouched={Input.defaultHighlightCheck}
            style={eAppCommonStyles.tabletTextField}
            onChange={() => ocrInputListener?.onChange?.()} // on OCR field update
          />
        </InputContainerTablet>
        <InputContainerTablet>
          <Input
            control={control}
            as={TextField}
            name="taxIdNumber"
            label={t('taxIdNumber')}
            style={{ flex: 1 }}
          />
        </InputContainerTablet>
        <InputContainerTablet>
          {affiliateCode && role === 'owner' && (
            <ReadOnlyField label={t('affiliateCode')} value={affiliateCode} />
          )}
        </InputContainerTablet>
        <InputContainerTablet>
          {campaignCode && role === 'owner' && (
            <ReadOnlyField label={t('campaignCode')} value={campaignCode} />
          )}
        </InputContainerTablet>
      </Box>
    </ApplicationDetailsTabletSectionContainer>
  );
}
