import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import AutocompletePopup from 'components/AutocompletePopup';
import Input from 'components/Input';
import {
  ID_COUNTRY,
  ID_OPTION_LIST,
  MY_OPTION_LIST,
  NEW_ADDRESS_OPTION,
  SAME_AS_HOME_ADDRESS_OPTION,
} from 'constants/optionList';
import {
  Box,
  Checkbox,
  Column,
  H6,
  Icon,
  Picker,
  PictogramIcon,
  RadioButton,
  Row,
  TextField,
} from 'cube-ui-components';
import ApplicationDetailsTabletSectionContainer from 'features/eAppV2/common/components/ApplicationDetailsTabletSectionContainer';
import { eAppCommonStyles } from 'features/eAppV2/common/constants/eAppCommonStyles';
import { useCheckEntity } from 'features/eAppV2/common/hooks/useCheckEntity';
import useSchemaValid from 'features/eAppV2/common/hooks/useSchemaValid';
import { useAddressInformationOptions } from 'features/eAppV2/id/hooks/useAddressInformationOptions';
import {
  mainPartyAddressInformationDefaultValue,
  mainPartyAddressInformationSchema,
} from 'features/eAppV2/id/validations/applicationDetails/sections/addressInformation';
import { MYAddressType } from 'features/eAppV2/my/validations/commonSchema';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { TFuncKey } from 'i18next';
import { Fragment, useCallback, useEffect, useMemo, useState } from 'react';
import { useController, useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { Country } from 'types/optionList';
import { Address, PartyRole } from 'types/party';
import { useOcrInputListenerContext } from '../../../OcrInputListener';

interface Props {
  role: 'main-party' | 'others';
  enableCorrespondenceSelector?: boolean;
}
export default function AddressInfo({
  role,
  enableCorrespondenceSelector,
}: Props) {
  const { t } = useTranslation('eApp');
  const { space } = useTheme();
  const { control } = useFormContext();
  const isDone = useSchemaValid(
    control,
    mainPartyAddressInformationDefaultValue,
    mainPartyAddressInformationSchema,
  );
  const addressTypes = useMemo<MYAddressType[]>(
    () =>
      role === 'main-party'
        ? ['correspondence', 'residential', 'business']
        : ['correspondence', 'residential'],
    [role],
  );

  return (
    <ApplicationDetailsTabletSectionContainer
      title={t('applicationDetails.addressInformation')}
      icon={<PictogramIcon.Home2 size={40} />}
      isDone={isDone}>
      <Box mt={space[5]}>
        <AddressInformationForm
          addressTypes={addressTypes}
          shouldHighlight
          enableCorrespondenceSelector={enableCorrespondenceSelector}
          // enableHomeAddress={}
        />
      </Box>
    </ApplicationDetailsTabletSectionContainer>
  );
}
interface AddressInformationFormProps {
  addressTypes: MYAddressType[];
  shouldHighlight?: boolean;
  initialHighlight?: boolean;
  enableCorrespondenceSelector?: boolean;
  hideLegend?: boolean;
}
export const AddressInformationForm = ({
  addressTypes,
  shouldHighlight,
  initialHighlight = true,
  enableCorrespondenceSelector,
  hideLegend,
}: AddressInformationFormProps) => {
  const { t } = useTranslation('eApp');
  const { space, colors } = useTheme();
  const [enableHomeAddress, setEnableHomeAddress] = useState(false);
  const { control, setValue, getValues } = useFormContext();
  const role = getValues('addressRole');

  const [communicationLaneOptions, setCommunicationLaneOptions] = useState([
    ID_OPTION_LIST.COMMUNICATION_LANE[0],
  ]);
  const localeCommunicationLaneOptions = useMemo(
    () =>
      communicationLaneOptions.map(option => ({
        value: option.value,
        label: t(option.label as TFuncKey<'eApp'>),
      })),
    [t, communicationLaneOptions],
  );
  const addCommunicationLaneOptions = useCallback(
    (option: { value: string; label: string }) => {
      setCommunicationLaneOptions(prev => {
        return [...prev, option];
      });
    },
    [],
  );
  const removeCommunicationLaneOptions = useCallback(
    (option: { value: string; label: string }) => {
      setCommunicationLaneOptions(prev => {
        return prev.filter(item => item.value !== option.value);
      });
    },
    [],
  );
  return (
    <Column backgroundColor={colors.background} px={space[6]}>
      {addressTypes.map(type => (
        <Fragment key={type}>
          <AddressSection
            type={type}
            shouldHighlight={shouldHighlight}
            initialHighlight={initialHighlight}
            noAddressOption={
              type === 'correspondence' && !enableCorrespondenceSelector
            }
            hideLegend={hideLegend}
            enableCorrespondenceSelector={enableCorrespondenceSelector}
            enableHomeAddress={enableHomeAddress}
            setEnableHomeAddress={setEnableHomeAddress}
            addCommunicationLaneOptions={addCommunicationLaneOptions}
            removeCommunicationLaneOptions={removeCommunicationLaneOptions}
          />
          {role !== 'owner' && <Box h={space[5]} />}
          {role === 'owner' && <Line />}
        </Fragment>
      ))}
      {role === 'owner' && (
        <Box gap={space[5]}>
          <Row gap={space[1]} alignItems="center">
            <Icon.CallText size={20} fill={colors.secondary} />
            <H6 fontWeight="bold">{t('selectCommunicationLane')}</H6>
          </Row>
          <Input
            control={control}
            as={Picker}
            name="communicationLane"
            type="chip"
            label={t('communicationLane')}
            items={localeCommunicationLaneOptions}
            shouldHighlightOnUntouched={Input.defaultHighlightCheck}
            labelStyle={eAppCommonStyles.pickerLabel}
            containerStyle={eAppCommonStyles.pickerContainer}
            size="large"
          />
        </Box>
      )}
    </Column>
  );
};
export const AddressSection = ({
  type,
  initialHighlight,
  noAddressOption,
  enableCorrespondenceSelector,
  enableHomeAddress,
  setEnableHomeAddress,
  hideLegend,
  addCommunicationLaneOptions,
  removeCommunicationLaneOptions,
}: Omit<AddressInformationFormProps, 'addressTypes' | 'isOwner'> & {
  type: MYAddressType;
  noAddressOption?: boolean;
  enableCorrespondenceSelector?: boolean;
  enableHomeAddress?: boolean;
  setEnableHomeAddress?: (flag: boolean) => void;
  addCommunicationLaneOptions: (option: {
    value: string;
    label: string;
  }) => void;
  removeCommunicationLaneOptions: (option: {
    value: string;
    label: string;
  }) => void;
}) => {
  const { colors, space } = useTheme();
  const { t } = useTranslation(['eApp']);
  const { data: optionList } = useGetOptionList<'id'>();
  const { control, setValue, getValues, watch, trigger } = useFormContext();
  const role = watch('addressRole');
  const partyRole = role === 'owner' ? PartyRole.PROPOSER : PartyRole.INSURED;
  const { stateList, onCountryChange } = useAddressInformationOptions(
    type,
    optionList,
    control,
    setValue,
    getValues,
    trigger,
  );

  const ocrInputListener = useOcrInputListenerContext();

  const isEntity = useCheckEntity();
  const addressOptions = useMemo(() => {
    let addressOptions = MY_OPTION_LIST.IB_CORRESPONDENCE_ADDRESSES;
    if (type === 'residential') {
      addressOptions = MY_OPTION_LIST.ID_RESIDENTIAL_ADDRESSES;
    } else if (type === 'business' && !isEntity) {
      addressOptions = MY_OPTION_LIST.ID_BUSINESS_ADDRESSES;
    } else if (type === 'business' && isEntity) {
      addressOptions = MY_OPTION_LIST.IB_ENTITY_BUSINESS_ADDRESSES;
    }
    return addressOptions;
  }, [type, isEntity]);
  const {
    field: { value: selectedAddressOption, onChange: setAddressOption },
  } = useController({
    name: `${type}Address`,
    control: control,
  });
  const isNewAddress = selectedAddressOption === NEW_ADDRESS_OPTION;

  const selectedCommunicationLaneOption = getValues('communicationLane');
  // update the communication lane options by the address type
  useEffect(() => {
    if (type === 'residential' && isNewAddress) {
      addCommunicationLaneOptions(ID_OPTION_LIST.COMMUNICATION_LANE[1]);
    }
    if (type === 'business' && isNewAddress) {
      addCommunicationLaneOptions(ID_OPTION_LIST.COMMUNICATION_LANE[2]);
    }
  }, [type, isNewAddress, addCommunicationLaneOptions]);
  const { caseObj } = useGetActiveCase();

  const companyParty = useMemo(
    () => caseObj?.parties?.find(p => p.roles.includes(partyRole)),
    [caseObj?.parties, partyRole],
  );

  // for insured to get the po address
  const policyOwnerParty = useMemo(
    () => caseObj?.parties?.find(p => p.roles.includes(PartyRole.PROPOSER)),
    [caseObj?.parties],
  );

  const saveAddress = useCallback(
    (res: Address) => {
      const {
        street,
        subDistrict,
        district,
        additionalAddress,
        countryCode,
        zipCode,
        city,
        province,
      } = res;
      setValue(`${type}AddressLine1`, street);
      setValue(`${type}AddressLine2`, subDistrict);
      setValue(`${type}AddressLine3`, district);
      setValue(`${type}AddressLine4`, additionalAddress);
      setValue(`${type}PostCode`, zipCode);
      setValue(`${type}City`, city);
      setValue(`${type}State`, province);
      setValue(`${type}Country`, countryCode);
    },
    [setValue, type],
  );
  useEffect(() => {
    const isCheckedBusinessAddressForEntity =
      type === 'business' && !isNewAddress && companyParty && isEntity;
    if (isCheckedBusinessAddressForEntity) {
      const res = (companyParty?.addresses || []).find(
        el => el.addressType === 'WORK',
      );
      if (res) {
        saveAddress(res);
      }
    }
  }, [isNewAddress, type, companyParty, setValue, isEntity, saveAddress]);
  useEffect(() => {
    const isCheckedCorrespondenceAddressForIndividual =
      type === 'correspondence' &&
      !isNewAddress &&
      companyParty &&
      enableCorrespondenceSelector;
    if (isCheckedCorrespondenceAddressForIndividual) {
      const res = (companyParty?.addresses || []).find(
        el => el.addressType === 'MAIN',
      );
      if (res) {
        saveAddress(res);
      }
    }
  }, [
    isNewAddress,
    type,
    companyParty,
    setValue,
    isEntity,
    enableCorrespondenceSelector,
    saveAddress,
  ]);
  const onSelectAddressOption = useCallback(
    (value: string) => {
      setAddressOption(value);
      if (value === NEW_ADDRESS_OPTION) {
        setValue(`${type}AddressLine1`, '');
        setValue(`${type}AddressLine2`, '');
        setValue(`${type}AddressLine3`, '');
        setValue(`${type}AddressLine4`, '');
        setValue(`${type}City`, '');
        setValue(`${type}State`, '');
        setValue(`${type}Country`, '');
        setValue(`${type}PostCode`, '');
      }
      if (value === SAME_AS_HOME_ADDRESS_OPTION) {
        setValue(`${type}AddressLine1`, getValues('residentialAddressLine1'));
        setValue(`${type}AddressLine2`, getValues('residentialAddressLine2'));
        setValue(`${type}AddressLine3`, getValues('residentialAddressLine3'));
        setValue(`${type}AddressLine4`, getValues('residentialAddressLine4'));
        setValue(`${type}City`, getValues('residentialCity'));
        setValue(`${type}State`, getValues('residentialState'));
        setValue(`${type}Country`, getValues('residentialCountry'));
        setValue(`${type}PostCode`, getValues('residentialPostCode'));
      }
    },
    [setAddressOption, setValue, type, getValues],
  );
  const country = watch(`${type}Country`);
  const occGroup = watch('occupationGroup');
  const correspondenceAddressSameAsPo = watch('correspondenceAddressSameAsPo');
  const residentialAddressSameAsPo = watch('residentialAddressSameAsPo');
  const shouldDisableInput =
    (type === 'correspondence' && correspondenceAddressSameAsPo) ||
    (type === 'residential' && residentialAddressSameAsPo);

  // populate the po address for insured
  useEffect(() => {
    if (role === 'insured') {
      const setAddressFields = (prefix: string, address?: Address) => {
        setValue(`${prefix}AddressLine1`, address?.street || '');
        setValue(`${prefix}AddressLine2`, address?.subDistrict || '');
        setValue(`${prefix}AddressLine3`, address?.district || '');
        setValue(`${prefix}AddressLine4`, address?.additionalAddress || '');
        setValue(`${prefix}PostCode`, address?.zipCode || '');
        setValue(`${prefix}City`, address?.city || '');
        setValue(`${prefix}State`, address?.province || '');
        setValue(`${prefix}Country`, address?.countryCode || '');
      };
      if (type === 'correspondence' && correspondenceAddressSameAsPo) {
        const policyOwnerCorrespondenceAddress =
          policyOwnerParty?.addresses?.find(a => a.addressType === 'MAIN');
        setAddressFields('correspondence', policyOwnerCorrespondenceAddress);
      }
      if (type === 'residential' && residentialAddressSameAsPo) {
        const policyOwnerResidentialAddress = policyOwnerParty?.addresses?.find(
          a => a.addressType === 'HOME',
        );
        setAddressFields('residential', policyOwnerResidentialAddress);
      }
    }
  }, [
    role,
    type,
    correspondenceAddressSameAsPo,
    residentialAddressSameAsPo,
    policyOwnerParty?.addresses,
    setValue,
  ]);
  return (
    // <Column backgroundColor={colors.background} px={space[6]}>
    <Fragment>
      {!hideLegend && (
        <Row gap={space[1]}>
          <Icon.Location fill={colors.palette.black} />
          <H6 fontWeight="bold">{t(`eApp:${type}Address`)}</H6>
        </Row>
      )}
      {type === 'correspondence' && role === 'insured' && (
        <>
          <Box h={7} />
          <Input
            control={control}
            as={Checkbox}
            name="correspondenceAddressSameAsPo"
            label={t('eApp:infoSameAsPO')}
          />
        </>
      )}
      {type === 'residential' && role === 'insured' && (
        <>
          <Box h={7} />
          <Input
            control={control}
            as={Checkbox}
            name="residentialAddressSameAsPo"
            label={t('eApp:infoSameAsPO')}
          />
        </>
      )}
      {!noAddressOption && addressOptions.length > 1 ? (
        <Row gap={space[6]}>
          {type === 'residential' && !residentialAddressSameAsPo && (
            <>
              <AddressOptionButton
                label={t(
                  `eApp:${addressOptions[0].label}` as TFuncKey<['eApp']>,
                )}
                value={addressOptions[0].value}
                selected={selectedAddressOption === addressOptions[0].value}
                onSelect={() => {
                  onSelectAddressOption(addressOptions[0].value);
                  setEnableHomeAddress?.(false);
                  const removedOption = ID_OPTION_LIST.COMMUNICATION_LANE[1];
                  removeCommunicationLaneOptions(removedOption);
                  if (selectedCommunicationLaneOption === removedOption.value) {
                    setValue(
                      'communicationLane',
                      ID_OPTION_LIST.COMMUNICATION_LANE[0].value,
                    );
                  }
                }}
              />
              <AddressOptionButton
                label={t(
                  `eApp:${addressOptions[1].label}` as TFuncKey<['eApp']>,
                )}
                value={addressOptions[1].value}
                selected={selectedAddressOption === addressOptions[1].value}
                onSelect={() => {
                  onSelectAddressOption(addressOptions[1].value);
                  setEnableHomeAddress?.(true);
                }}
              />
            </>
          )}
          {type === 'business' && (
            <>
              <AddressOptionButton
                label={t(
                  `eApp:${addressOptions[0].label}` as TFuncKey<['eApp']>,
                )}
                value={addressOptions[0].value}
                selected={selectedAddressOption === addressOptions[0].value}
                onSelect={() => {
                  onSelectAddressOption(addressOptions[0].value);
                  const removedOption = ID_OPTION_LIST.COMMUNICATION_LANE[2];
                  removeCommunicationLaneOptions(removedOption);
                  if (selectedCommunicationLaneOption === removedOption.value) {
                    setValue(
                      'communicationLane',
                      ID_OPTION_LIST.COMMUNICATION_LANE[0].value,
                    );
                  }
                }}
              />
              <AddressOptionButton
                label={t(
                  `eApp:${addressOptions[1].label}` as TFuncKey<['eApp']>,
                )}
                value={addressOptions[1].value}
                selected={selectedAddressOption === addressOptions[1].value}
                onSelect={() => {
                  onSelectAddressOption(addressOptions[1].value);
                  const removedOption = ID_OPTION_LIST.COMMUNICATION_LANE[2];
                  removeCommunicationLaneOptions(removedOption);
                  if (selectedCommunicationLaneOption === removedOption.value) {
                    setValue(
                      'communicationLane',
                      ID_OPTION_LIST.COMMUNICATION_LANE[0].value,
                    );
                  }
                }}
                disabled={!enableHomeAddress}
              />
              <AddressOptionButton
                label={t(
                  `eApp:${addressOptions[2].label}` as TFuncKey<['eApp']>,
                )}
                value={addressOptions[2]?.value || ''}
                selected={selectedAddressOption === addressOptions[2]?.value}
                onSelect={() => onSelectAddressOption(addressOptions[2]?.value)}
              />
            </>
          )}
        </Row>
      ) : (
        <Box h={7} />
      )}
      {(isNewAddress || shouldDisableInput) && (
        <Box
          gap={space[5]}
          mt={space[hideLegend ? 0 : 5]}
          key={type === 'business' ? occGroup : type}>
          <Input
            control={control}
            as={TextField}
            name={`${type}AddressLine1`}
            label={t('eApp:addressLine1')}
            hint={t('eApp:addressLine1.hint')}
            shouldHighlightOnUntouched={Input.defaultHighlightCheck}
            initialHighlight={initialHighlight}
            shouldUnregister={true}
            disabled={shouldDisableInput}
            onChange={() => ocrInputListener?.onChange?.()}
          />
          <Input
            control={control}
            as={TextField}
            name={`${type}AddressLine2`}
            label={t('eApp:addressLine2')}
            hint={t('eApp:addressLine2.hint')}
            style={eAppCommonStyles.tabletTextField}
            disabled={shouldDisableInput}
            onChange={() => ocrInputListener?.onChange?.()}
          />
          <Input
            control={control}
            as={TextField}
            name={`${type}AddressLine3`}
            label={t('eApp:addressLine3')}
            style={eAppCommonStyles.tabletTextField}
            disabled={shouldDisableInput}
            onChange={() => ocrInputListener?.onChange?.()}
          />
          <Input
            control={control}
            as={TextField}
            name={`${type}AddressLine4`}
            label={t('eApp:addressLine4')}
            style={eAppCommonStyles.tabletTextField}
            disabled={shouldDisableInput}
            onChange={() => ocrInputListener?.onChange?.()}
          />
          <Row gap={space[6]}>
            <Input
              control={control}
              as={AutocompletePopup<Country, string>}
              name={`${type}Country`}
              label={t('eApp:country')}
              data={optionList?.COUNTRY.options ?? []}
              getItemLabel={item => item.label}
              getItemValue={item => String(item.value)}
              style={eAppCommonStyles.tabletTextField}
              onChange={onCountryChange}
              searchable
              shouldHighlightOnUntouched={Input.defaultHighlightCheck}
              initialHighlight={initialHighlight}
              disabled={shouldDisableInput}
            />
            {country === ID_COUNTRY && (
              <Input
                control={control}
                as={AutocompletePopup<{ value: string; label: string }, string>}
                name={`${type}State`}
                label={t('eApp:state')}
                data={stateList}
                getItemLabel={item => item.label}
                getItemValue={item => String(item.value)}
                style={eAppCommonStyles.tabletTextField}
                searchable
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={initialHighlight}
                disabled={shouldDisableInput}
              />
            )}
            {country !== ID_COUNTRY && (
              <Input
                control={control}
                as={TextField}
                name={`${type}State`}
                label={t('eApp:state')}
                style={eAppCommonStyles.tabletTextField}
                shouldHighlightOnUntouched={Input.defaultHighlightCheck}
                initialHighlight={initialHighlight}
                disabled={shouldDisableInput}
              />
            )}
          </Row>
          <Row gap={space[6]}>
            <Input
              control={control}
              as={TextField}
              name={`${type}City`}
              label={t('eApp:city')}
              style={eAppCommonStyles.tabletTextField}
              maxLength={60}
              shouldHighlightOnUntouched={Input.defaultHighlightCheck}
              initialHighlight={initialHighlight}
              disabled={shouldDisableInput}
            />
            <Input
              control={control}
              as={TextField}
              name={`${type}PostCode`}
              label={t('eApp:postcode')}
              style={eAppCommonStyles.tabletTextField}
              maxLength={10}
              shouldHighlightOnUntouched={Input.defaultHighlightCheck}
              initialHighlight={initialHighlight}
              disabled={shouldDisableInput}
            />
          </Row>
        </Box>
      )}
    </Fragment>
  );
};
const AddressOptionButton = styled(RadioButton)(({ theme }) => ({
  marginTop: theme.space[3],
}));
const Line = styled.View(({ theme: { space, colors } }) => ({
  backgroundColor: colors.palette.fwdGrey[100],
  height: 1,
  marginVertical: space[5],
}));
