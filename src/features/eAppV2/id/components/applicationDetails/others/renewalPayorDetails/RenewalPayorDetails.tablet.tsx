import { useTheme } from '@emotion/react';
import { Box, Column, LargeBody, Row, Switch } from 'cube-ui-components';
import EAppFooterTablet from 'features/eAppV2/common/components/footer/EAppFooter.tablet';
import { useOcrImage } from 'features/eAppV2/common/hooks/useOcrImage';
import { useSaveOcrImage } from 'features/eAppV2/common/hooks/useSaveOcrImage';
import useOcrLogic, { OCRForm } from 'features/eAppV2/id/hooks/useOcrLogic';
import { PayorInfoForm } from 'features/eAppV2/id/validations/applicationDetails/payor/payorValidation';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import React, { useEffect, useMemo, useState } from 'react';
import {
  FormProvider,
  UseFormGetValues,
  UseFormSetValue,
} from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { PartyRole } from 'types/party';
import DukcapilValidationPopup from '../../../DukcapilValidationPopup';
import OcrInputListener from '../../../OcrInputListener';
import IdentityVerificationTablet from '../../policyOwner/identityVerification/IdentityVerification.tablet';
import AddressInfo from '../payorDetails/addressInformation/AddressInformation.tablet';
import ContactDetails from '../payorDetails/contactDetails/ContactDetails.tablet';
import OccupationDetails from '../payorDetails/occupationDetails/OccupationDetails.tablet';
import PersonalDetails from '../payorDetails/personalDetails/PersonalDetails.tablet';
import { usePayorDetails } from '../payorDetails/usePayorDetails';

export default function RenewalPayorDetailsTablet({
  onNext,
}: {
  onNext: () => void;
}) {
  const {
    formMethods,
    scrollRef,
    isValid,
    isLoading,
    focusOnNextIncompleteField,
    totalIncompleteRequiredFields,
    onSave,
    hasRenewalPayor,
    setHasRenewalPayor,
    dukcapilResult,
    resetDukcapilResult,
  } = usePayorDetails({
    payorRole: PartyRole.RENEWAL_PAYER,
  });

  const { space } = useTheme();
  const { t } = useTranslation(['eApp']);
  const { caseObj } = useGetActiveCase();
  const [hasPolicyControlAuthority, setHasPolicyControlAuthority] = useState(
    Boolean(caseObj?.havePolicyControlAuthority),
  );
  const [hasPayorRole, setHasPayorRole] = useState(
    Boolean(caseObj?.havePayorRole),
  );

  const renewalPayerParty = useMemo(
    () =>
      caseObj?.parties?.find(p => p.roles.includes(PartyRole.RENEWAL_PAYER)),
    [caseObj?.parties],
  );

  const { saveOcrImage } = useSaveOcrImage();

  const onSubmit = async () => {
    const id = await onSave({
      isOcrSuccess: owbOcrFlag,
      isLivenessCheckVerified,
      isFaceMatched,
    });

    if (id) {
      if (isOCREnabled)
        await saveOcrImage(id, ocrFile, PartyRole.RENEWAL_PAYER);
      onNext();
    }
  };

  const [isFaceMatched, setIsFaceMatched] = useState<boolean | undefined>(
    undefined,
  );
  const [isLivenessCheckVerified, setIsLivenessCheckVerified] = useState<
    boolean | undefined
  >(undefined);

  const ocrImage = useOcrImage(renewalPayerParty?.id);
  const { ocrLogic, isOCREnabled, ocrFile, owbOcrFlag, hasMismatchFields } =
    useOcrLogic({
      role: PartyRole.RENEWAL_PAYER,
      getValues: formMethods.getValues as unknown as UseFormGetValues<OCRForm>,
      setValue: formMethods.setValue as unknown as UseFormSetValue<OCRForm>,
      ocrImage,
      defaultIsOcrSuccess: renewalPayerParty?.person?.isOcrSuccess,
    });

  useEffect(() => {
    if (hasRenewalPayor) {
      const values = formMethods.getValues();

      const filledFieldNames = Object.entries(values)
        .filter(
          ([_, value]) => value !== '' && value !== null && value !== undefined,
        )
        .map(([key]) => key);

      formMethods.trigger(filledFieldNames as Array<keyof PayorInfoForm>);
    }
  }, [hasRenewalPayor]);

  let primaryDisabled = false;
  if (hasRenewalPayor) {
    if (isOCREnabled) {
      primaryDisabled = !(
        isValid &&
        !hasMismatchFields &&
        ocrLogic.isOcrSuccess !== undefined
      );
    } else {
      primaryDisabled = !isValid;
    }
  }

  return (
    <FormProvider {...formMethods}>
      <OcrInputListener onChange={ocrLogic.onOcrFieldUpdate}>
        <KeyboardAwareScrollView
          ref={scrollRef}
          keyboardDismissMode="interactive"
          keyboardShouldPersistTaps="handled"
          extraScrollHeight={20}
          keyboardOpeningTime={Number.MAX_SAFE_INTEGER}
          enableAutomaticScroll={true}
          contentContainerStyle={{ paddingBottom: space[30] }}>
          <Box pl={0} p={space[6]}>
            <Row
              alignItems="center"
              pb={space[4]}
              display="flex"
              justifyContent="space-between">
              <LargeBody
                fontWeight="medium"
                children={t(
                  'eApp:other.renewalBeneficialOwnerDetails.sameAsPolicyHolder.question',
                )}
              />
              <Switch
                label={!hasRenewalPayor ? t('eApp:yes') : t('eApp:no')}
                checked={!hasRenewalPayor}
                onChange={value => setHasRenewalPayor(!value)}
              />
            </Row>
            <Row
              alignItems="center"
              pb={space[4]}
              display="flex"
              justifyContent="space-between">
              <LargeBody
                fontWeight="medium"
                children={t(
                  'eApp:other.renewalBeneficialOwnerDetails.authorityInControllingPolicyTransaction.question',
                )}
              />
              <Switch
                label={hasPolicyControlAuthority ? t('eApp:yes') : t('eApp:no')}
                checked={hasPolicyControlAuthority}
                onChange={setHasPolicyControlAuthority}
              />
            </Row>
            <Row
              alignItems="center"
              pb={space[4]}
              display="flex"
              justifyContent="space-between">
              <LargeBody
                fontWeight="medium"
                children={t(
                  'eApp:other.renewalBeneficialOwnerDetails.becomePolicyPayor.question',
                )}
              />
              <Switch
                label={hasPayorRole ? t('eApp:yes') : t('eApp:no')}
                checked={hasPayorRole}
                onChange={setHasPayorRole}
              />
            </Row>
            {hasRenewalPayor && (
              <Column>
                {/* renewal payor should always render the ocr section */}
                <IdentityVerificationTablet
                  role={PartyRole.RENEWAL_PAYER}
                  isFaceMatched={isFaceMatched}
                  onSetIsFaceMatched={setIsFaceMatched}
                  isLivenessCheckVerified={isLivenessCheckVerified}
                  onSetIsLivenessCheckVerified={setIsLivenessCheckVerified}
                  ocrLogic={ocrLogic}
                  disableFaceRecognition
                />
                <PersonalDetails />
                <OccupationDetails />
                <ContactDetails />
                <AddressInfo enableCorrespondenceSelector={true} />
              </Column>
            )}
          </Box>
        </KeyboardAwareScrollView>
        <EAppFooterTablet
          progressLock="appDetail-others"
          primaryDisabled={primaryDisabled}
          primaryLabel={t('next')}
          onPrimaryPress={onSubmit}
          primaryLoading={isLoading}
          focusOnIncompleteField={focusOnNextIncompleteField}
          totalIncompleteRequiredFields={
            hasRenewalPayor ? totalIncompleteRequiredFields : 0
          }
          isAbsolutePositioned
        />

        <DukcapilValidationPopup
          visible={dukcapilResult?.code === 'NOT_ALLOW_PROCEED'}
          onClose={resetDukcapilResult}
          result={dukcapilResult}
        />
      </OcrInputListener>
    </FormProvider>
  );
}
