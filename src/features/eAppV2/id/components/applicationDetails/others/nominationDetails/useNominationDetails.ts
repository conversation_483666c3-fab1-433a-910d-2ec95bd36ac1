import { getPartiesByRole } from 'features/eAppV2/common/utils/partyUtils';
import { useEAppProgressBarStore } from 'features/eAppV2/common/utils/store/eAppProgressBarStore';
import {
  NominationInfoForm,
  nominationInfoSchema,
  nominationInfoToParties,
  partiesToNominationInfo,
} from 'features/eAppV2/id/validations/applicationDetails/nomination/nominationInfoValidation';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useDeletePartiesByRole, useSaveParty } from 'hooks/useParty';
import { useCallback, useMemo } from 'react';
import { useFieldArray, useForm } from 'react-hook-form';
import { PartyRole } from 'types/party';
import { useYupResolver } from 'utils/validation/useYupResolver';
import { useSyncDocumentUpload } from './useSyncDocumentUpload';

export const useNominationDetails = () => {
  const { caseObj } = useGetActiveCase();
  const { data: optionList } = useGetOptionList<'id'>();
  const { saveParty, isLoading } = useSaveParty();

  const { deletePartiesByRole, isDeletingParties } = useDeletePartiesByRole();
  const { handleSyncDocumentUpload } = useSyncDocumentUpload();

  const insuredPrimaryId = useMemo(
    () =>
      caseObj?.parties
        ?.find(({ roles }) => roles.includes(PartyRole.INSURED))
        ?.person?.registrations?.find(r => r.type === 'DEFAULT'),
    [caseObj?.parties],
  );

  const witnessPrimaryId = useMemo(
    () =>
      getPartiesByRole(
        caseObj,
        PartyRole.WITNESS,
      )?.[0]?.person?.registrations?.find(r => r.type === 'DEFAULT'),
    [caseObj],
  );

  const form = useForm<NominationInfoForm>({
    mode: 'onBlur',
    defaultValues: useMemo(() => {
      return partiesToNominationInfo(
        getPartiesByRole(caseObj, PartyRole.BENEFICIARY),
        optionList,
      );
    }, []), //eslint-disable-line react-hooks/exhaustive-deps
    resolver: useYupResolver(nominationInfoSchema),
    context: {
      optionList: optionList,
      insuredIdNumber: insuredPrimaryId?.id,
      insuredIdType: insuredPrimaryId?.idType,
      witnessIdNumber: witnessPrimaryId?.id,
      witnessIdType: witnessPrimaryId?.idType,
    },
  });

  const {
    fields: nominees,
    append,
    remove,
    update,
  } = useFieldArray({
    name: 'nominees',
    control: form.control,
    keyName: 'nomineeKey',
  });

  const markCompletedByKeys = useEAppProgressBarStore(
    state => state.markCompletedByKeys,
  );
  const saveNomination = useCallback(async () => {
    if (!caseObj) throw new Error('missing case data while saving nomination');

    return form.handleSubmit(async value => {
      await handleSyncDocumentUpload();
      const nominees = nominationInfoToParties(value);
      for (let i = 0; i < nominees.length; i++) {
        const id = await saveParty(nominees[i]);
        form.setValue(`nominees.${i}.id`, id);
      }
    })();
  }, [
    caseObj,
    deletePartiesByRole,
    form.handleSubmit,
    markCompletedByKeys,
    saveParty,
  ]);

  return {
    form,
    nominees,
    saveNomination,
    isLoading,
    isDeletingParties,
    append,
    remove,
    update,
  };
};
