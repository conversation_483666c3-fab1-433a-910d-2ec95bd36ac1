import { memo } from 'react';
import PaymentFailed from './gateway/PaymentStatus';
import PaymentFormTablet from './form/PaymentForm.tablet';
import usePayment from './hooks/usePayment';
import usePaymentSubmission from './hooks/usePaymentSubmission';

const PaymentTablet = memo(function Payment() {
  const {
    selectedPaymentMethod,
    setSelectedPaymentMethod,
    paymentResponse,
    onBackPress,
    proceedOfflineSubmission,
    setProceedOfflineSubmission,
  } = usePayment();

  usePaymentSubmission({
    paymentMethod: selectedPaymentMethod,
    paymentResponse,
    proceedOfflineSubmission,
  });
  if (paymentResponse && paymentResponse.transaction.status !== 'SUCCESS') {
    return (
      <PaymentFailed
        info={paymentResponse}
        paymentMethod={selectedPaymentMethod}
        onBackPress={onBackPress}
      />
    );
  }

  // render default payment form if no payment method + transaction id
  return (
    <PaymentFormTablet
      selectedMethod={selectedPaymentMethod}
      onSelectPaymentMethod={setSelectedPaymentMethod}
      onProceedOfflineSubmission={setProceedOfflineSubmission}
    />
  );
});

export default PaymentTablet;
