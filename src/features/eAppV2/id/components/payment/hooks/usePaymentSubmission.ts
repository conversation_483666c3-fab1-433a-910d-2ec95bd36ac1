import { useEAppSubmitApplication } from 'features/eAppV2/common/hooks/useEAppSubmitApplication';
import { useRootStackNavigation } from 'hooks/useRootStack';
import useBoundStore from 'hooks/useBoundStore';
import { useGetCase } from 'hooks/useGetCase';
import { useAlert } from 'hooks/useAlert';
import { PaymentResultResponse } from 'api/dokuApi';
import { useCreateApplication } from 'hooks/useCreateApplication';
import { useEffect, useRef, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { PaymentMethod } from '../form/methods/paymentMethodTypes';

interface PaymentSuccessProps {
  paymentResponse?: PaymentResultResponse | null;
  paymentMethod?: PaymentMethod | null;
  proceedOfflineSubmission: boolean;
}

export default function usePaymentSubmission({
  paymentMethod,
  paymentResponse,
  proceedOfflineSubmission,
}: PaymentSuccessProps) {
  const { t } = useTranslation(['eApp', 'common']);
  const { alertError } = useAlert();
  const navigation = useRootStackNavigation();
  const caseId = useBoundStore(state => state.case.caseId);
  const { data: caseObj } = useGetCase(caseId);
  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);

  const { mutateAsync: saveApplication, isLoading: isSavingApplication } =
    useCreateApplication();
  const { mutateAsync: submitApplication, isLoading: isSubmittingApplication } =
    useEAppSubmitApplication();

  const isOfflinePayment = paymentMethod === PaymentMethod.OFFLINE;

  const saveApplicationWithPayment = useCallback(async () => {
    if (!caseId || !caseObj) return;

    const initialPayment = isOfflinePayment
      ? undefined
      : {
          ...(caseObj.application?.initialPayment as PaymentResultResponse),
          paymentMethod: paymentMethod,
          paymentResponse: paymentResponse,
          paymentDate:
            paymentResponse?.transaction?.status === 'SUCCESS'
              ? (
                  caseObj.application?.initialPayment as {
                    paymentDate?: string | null;
                  }
                )?.paymentDate || new Date().toISOString()
              : null,
        };

    await saveApplication({
      caseId,
      data: {
        ...caseObj.application,
        paymentMethod: isOfflinePayment ? paymentMethod : undefined,
        initialPayment,
      },
    });
  }, [
    caseId,
    caseObj,
    paymentMethod,
    paymentResponse,
    saveApplication,
    isOfflinePayment,
  ]);

  const canStartInitialSave = isOfflinePayment
    ? proceedOfflineSubmission
    : Boolean(paymentResponse);

  const canStartSubmission = isOfflinePayment
    ? proceedOfflineSubmission
    : Boolean(paymentResponse) &&
      paymentResponse?.transaction?.status === 'SUCCESS';

  // Initial save effect - runs once when paymentResponse and case are ready
  const hasDoneInitialSaving = useRef(false);
  useEffect(() => {
    if (
      !canStartInitialSave ||
      !caseId ||
      !caseObj ||
      hasDoneInitialSaving.current
    )
      return;

    saveApplicationWithPayment()
      .then(() => {
        hasDoneInitialSaving.current = true;
      })
      .catch(() => {
        alertError(t('eApp:failedToSaveData'));
      });
  }, [
    caseId,
    caseObj,
    saveApplicationWithPayment,
    alertError,
    t,
    canStartInitialSave,
  ]);

  useEffect(() => {
    if (isSavingApplication || isSubmittingApplication) {
      setAppLoading();
    } else {
      setAppIdle();
    }
    return () => {
      setAppIdle();
    };
  }, [isSavingApplication, isSubmittingApplication, setAppIdle, setAppLoading]);

  // Submit application effect
  const hasSubmitted = useRef(false);
  useEffect(() => {
    const onSubmit = async () => {
      if (!caseId || !caseObj) return;
      try {
        hasSubmitted.current = true;
        await saveApplicationWithPayment();
      } catch {
        alertError(t('eApp:failedToSaveData'));
        return;
      }
      await submitApplication({
        onSuccess: () => {
          navigation.navigate('Submission');
        },
        onFailure: () => {
          alertError(t('eApp:submission.failed'));
        },
      });
    };
    if (canStartSubmission && !hasSubmitted.current) {
      onSubmit();
    }
  }, [
    caseId,
    caseObj,
    saveApplicationWithPayment,
    submitApplication,
    navigation,
    alertError,
    t,
    canStartSubmission,
  ]);
}
