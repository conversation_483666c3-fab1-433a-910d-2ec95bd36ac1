import SignatureBase, {
  SignatureTab,
} from 'features/eAppV2/common/components/signature/SignatureBase';
import { getPartiesByRole } from 'features/eAppV2/common/utils/partyUtils';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { PartyRole, PartyType } from 'types/party';
import {
  AGENT_SIGNATURE_TERMS,
  CONSENT_PO_SIGNATURE_TERMS,
  CONSENT_PI_SIGNATURE_TERMS,
  PI_SIGNATURE_TERMS,
  PO_SIGNATURE_TERMS,
} from '../../constants/signatureTermsAndCondition';

export default function Signature({
  visible,
  onDismiss,
  onFinish,
  startingIndex,
}: {
  visible: boolean;
  onDismiss: () => void;
  onFinish: () => void;
  startingIndex: number;
}) {
  const { t } = useTranslation(['eApp']);
  const { caseObj } = useGetActiveCase();
  const { data: agentInfo } = useGetAgentProfile();

  const tabs = useMemo<SignatureTab[]>(() => {
    if (!caseObj || !agentInfo) return [];
    const policyOwner = getPartiesByRole(caseObj, PartyRole.PROPOSER)?.[0];
    const insured = getPartiesByRole(caseObj, PartyRole.INSURED)?.[0];
    const isEntity = policyOwner?.clientType === PartyType.ENTITY;

    return [
      policyOwner && {
        role: isEntity
          ? t('eApp:representativeInfo.authorizedSignatory')
          : t('eApp:signature.policyOwner'),
        signatureRole: 'proposers',
        consentSignatureRole: 'proposers',
        declarationRole: isEntity
          ? t('eApp:representativeInfo.authorizedSignatory')
          : t('eApp:signature.policyOwner'),
        consentDeclarationRole: isEntity
          ? t('eApp:representativeInfo.authorizedSignatory')
          : t('eApp:signature.policyOwner'),
        name: isEntity
          ? policyOwner.entity?.representative?.name?.fullName?.trim()
          : policyOwner.person?.name.fullName?.trim(),
        partyId: policyOwner.id,
        terms: CONSENT_PO_SIGNATURE_TERMS,
        customerSeq: '1',
        shouldAllowRemoteSellingToggle: false,
      },
      policyOwner && {
        role: isEntity
          ? t('eApp:representativeInfo.authorizedSignatory')
          : t('eApp:signature.policyOwner'),
        signatureRole: 'proposers',
        declarationRole: isEntity
          ? t('eApp:representativeInfo.authorizedSignatory')
          : t('eApp:signature.policyOwner'),
        name: isEntity
          ? policyOwner.entity?.representative?.name?.fullName?.trim()
          : policyOwner.person?.name.fullName?.trim(),
        partyId: policyOwner.id,
        terms: PO_SIGNATURE_TERMS,
        customerSeq: '1',
        shouldAllowRemoteSellingToggle: false,
      },
      insured &&
        policyOwner?.id !== insured?.id && {
          role: t('eApp:signature.insured'),
          signatureRole: 'insureds',
          consentSignatureRole: 'insureds',
          declarationRole: t('eApp:signature.insured'),
          consentDeclarationRole: t('eApp:signature.insured'),
          name: insured.person?.name.fullName?.trim(),
          partyId: insured.id,
          terms: CONSENT_PI_SIGNATURE_TERMS,
          customerSeq: '1',
          shouldAllowRemoteSellingToggle: false,
        },
      insured &&
        policyOwner?.id !== insured?.id && {
          role: t('eApp:signature.insured'),
          signatureRole: 'insureds',
          declarationRole: t('eApp:signature.insured'),
          name: insured.person?.name.fullName?.trim(),
          partyId: insured.id,
          terms: PI_SIGNATURE_TERMS,
          customerSeq: '1',
          shouldAllowRemoteSellingToggle: false,
        },
      {
        role: t('eApp:signature.agent'),
        signatureRole: 'agent',
        declarationRole: t('eApp:signature.agent'),
        name: agentInfo.person?.fullName?.trim(),
        partyId: '',
        terms: AGENT_SIGNATURE_TERMS,
        customerSeq: '1',
        overridingRemoteSelling: false,
        shouldAllowRemoteSellingToggle: false,
      },
    ].filter(Boolean) as SignatureTab[];
  }, [agentInfo, caseObj, t]);

  return (
    <SignatureBase
      visible={visible}
      startingIndex={startingIndex}
      tabs={tabs}
      validateSignature={validateSignature}
      onDismiss={onDismiss}
      onFinish={onFinish}
    />
  );
}

const validateSignature = (strokes: string[]) => {
  // disable if no stroke
  if (strokes.length <= 0) {
    return false;
  }
  return true;
};
