import { withdrawalPaymentSetupFormToWithdrawalPayStore } from './../validations/renewalPaymentSetupValidation';
import { useCallback, useRef } from 'react';
import { useForm } from 'react-hook-form';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import {
  owbPaymentSetupRetrieveToWithdrawalPaymentSetupForm,
  WithdrawalPaymentSetupForm,
  withdrawalPaymentSetupSchema,
} from '../validations/renewalPaymentSetupValidation';
import { useEAppValidationResolver } from 'features/eAppV2/common/hooks/useEAppValidationResolver';
import { useIncompleteFields } from 'features/eAppV2/common/hooks/useIncompleteFields';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useWithdrawalPayStore } from './useWithdrawalPayStore';
import { useGetPaymentSetup } from './useGetPaymentSetup';

const useWithdrawalPaymentSetupLogic = () => {
  const { caseObj } = useGetActiveCase();
  const scrollRef = useRef<KeyboardAwareScrollView>(null);
  const { paymentSetupData } = useGetPaymentSetup();
  const { mutateAsync: saveWithdrawalPayStore } = useWithdrawalPayStore(
    caseObj?.id,
  );

  const formMethods = useForm<WithdrawalPaymentSetupForm>({
    mode: 'onBlur',
    defaultValues:
      owbPaymentSetupRetrieveToWithdrawalPaymentSetupForm(paymentSetupData),
    resolver: useEAppValidationResolver(withdrawalPaymentSetupSchema),
  });

  const { watch, control, handleSubmit } = formMethods;

  const { focusOnNextIncompleteField, totalIncompleteRequiredFields } =
    useIncompleteFields({
      schema: withdrawalPaymentSetupSchema,
      watch: watch,
      control: control,
      scrollRef,
      scrollTo: ({ y }) => scrollRef.current?.scrollToPosition(0, y || 0, true),
    });

  const onSave = useCallback(() => {
    return handleSubmit(async data => {
      if (caseObj) {
        await saveWithdrawalPayStore(
          withdrawalPaymentSetupFormToWithdrawalPayStore(data, caseObj),
        );
      }
    })();
  }, [handleSubmit, caseObj, saveWithdrawalPayStore]);

  return {
    onSave,
    scrollRef,
    formMethods,
    focusOnNextIncompleteField,
    totalIncompleteRequiredFields,
  };
};

export default useWithdrawalPaymentSetupLogic;
