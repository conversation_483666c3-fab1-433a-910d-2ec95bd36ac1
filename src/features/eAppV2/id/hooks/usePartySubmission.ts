import { NavigationProp, useNavigation } from '@react-navigation/native';
import { usePromptContext } from 'components/prompt/PromptContext';
import {
  toDukcapilPayload,
  useDukcapilValidation,
} from 'features/eAppV2/id/hooks/useDukcapilValidation';
import {
  PolicyInsuredInfoForm,
  policyInsuredInfoToParty,
} from 'features/eAppV2/id/validations/applicationDetails/policyInsured/policyInsuredValidation';
import {
  PolicyOwnerInfoForm,
  policyOwnerInfoToParty,
} from 'features/eAppV2/id/validations/applicationDetails/policyOwner/policyOwnerInfoValidation';
import { useAlert } from 'hooks/useAlert';
import { useCreateApplication } from 'hooks/useCreateApplication';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useSaveParty } from 'hooks/useParty';
import { useCallback, useMemo } from 'react';
import { UseFormSetError } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { RootStackParamList } from 'types';
import { PartyRole } from 'types/party';
import GATracking from 'utils/helper/gaTracking';
import { useBlacklistChecking } from './useBlacklistChecking';

export const usePartySubmission = ({
  role,
  setError,
}: {
  role: PartyRole;
  setError: UseFormSetError<PolicyOwnerInfoForm | PolicyInsuredInfoForm>;
}) => {
  const { t } = useTranslation(['eApp']);
  const { data: optionList } = useGetOptionList<'id'>();
  const { caseObj } = useGetActiveCase();

  const party = useMemo(
    () => caseObj?.parties?.find(p => p.roles.includes(role)),
    [caseObj?.parties, role],
  );
  const { alertError } = useAlert();
  const { prompt } = usePromptContext();

  const { saveParty, isLoading: isSavingParty } = useSaveParty();
  const {
    mutateAsync: validateDukcapil,
    isLoading: isDukcapilValidating,
    data: dukcapilResult,
    reset: resetDukcapilResult,
  } = useDukcapilValidation();

  const { mutateAsync: checkBlackList } = useBlacklistChecking();

  const navigation = useNavigation<NavigationProp<RootStackParamList>>();

  const { mutateAsync: saveApplication, isLoading: isSavingApplication } =
    useCreateApplication();
  const onSave = useCallback(
    async ({
      values,
      isOcrSuccess,
      isLivenessCheckVerified,
      isFaceMatched,
    }: {
      values: PolicyOwnerInfoForm | PolicyInsuredInfoForm;
      isOcrSuccess?: boolean | null;
      isLivenessCheckVerified?: boolean;
      isFaceMatched?: boolean;
    }) => {
      if (!caseObj) throw new Error('missing case data while saving PO');
      if (!party) throw new Error('missing PO party data while saving PO');
      if (!optionList) throw new Error('missing option list while saving PO');
      try {
        let newParty;
        let updatedApplication;

        // const blackListResult = await checkBlackList({
        //   idNumber: values.primaryId,
        // });

        // if (blackListResult?.code === 'NOT_ALLOW_PROCEED') {
        //   await prompt({
        //     title: t('eApp:dukcapilValidationPopup.cannotProceed'),
        //     description: '',
        //     actions: getAlertActions,
        //     config: {
        //       accept: t('eApp:dukcapilValidationPopup.backToHome'),
        //     },
        //   });

        //   navigation.reset({
        //     index: 0,
        //     routes: [{ name: 'Main' }],
        //   });
        //   return;
        // }

        const dukcapilPayload = toDukcapilPayload(values);

        const result = dukcapilPayload
          ? await validateDukcapil({ ...dukcapilPayload, caseId: caseObj.id })
          : null;

        if (result?.code === 'NOT_ALLOW_PROCEED') {
          setError('primaryId', {
            message: t('eApp:dataMismatch'),
          });
          return;
        }

        if (role === PartyRole.INSURED) {
          newParty = policyInsuredInfoToParty({
            form: values as PolicyInsuredInfoForm,
            roles: party.roles,
            optionList,
            isOcrSuccess,
            isLivenessCheckVerified,
            isFaceMatched,
            dukcapilServiceStatus: result?.flag,
          });
        } else {
          newParty = policyOwnerInfoToParty({
            form: values as PolicyOwnerInfoForm,
            roles: party.roles,
            optionList,
            isOcrSuccess,
            isLivenessCheckVerified,
            isFaceMatched,
            dukcapilServiceStatus: result?.flag,
          });
          // Save premium payment data to application
          const paymentMethod = (values as PolicyOwnerInfoForm)?.paymentMethod;
          updatedApplication = {
            ...caseObj.application,
            paymentMethod: paymentMethod === 'N' ? undefined : paymentMethod, // if not paying with credit card, remove payment method
            creditCardIssuedCountry:
              paymentMethod === 'N'
                ? undefined
                : (values as PolicyOwnerInfoForm)?.issuedCountry,
          };
        }

        if (updatedApplication) {
          await saveApplication({
            caseId: caseObj.id,
            data: updatedApplication,
          });
        }
        const id = await saveParty(newParty, {
          overridingRoles: false,
          preventCreatingParty: true,
        });
        // saving secondary agent
        GATracking.logCustomEvent('application', {
          action_type: 'eapp_submit_details',
          application_type: 'F2F',
        });

        return id;
      } catch {
        alertError(t('eApp:failedToSaveData'));
      }
    },
    [
      alertError,
      caseObj,
      optionList,
      party,
      saveParty,
      saveApplication,
      t,
      role,
    ],
  );
  return {
    onSave,
    isLoading: isSavingParty || isSavingApplication || isDukcapilValidating,
    dukcapilResult,
    resetDukcapilResult,
  };
};
