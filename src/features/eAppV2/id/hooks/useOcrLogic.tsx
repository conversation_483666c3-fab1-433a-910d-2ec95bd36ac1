import { useTheme } from '@emotion/react';
import { NavigationProp, useNavigation } from '@react-navigation/native';
import { OcrRef } from 'components/Ocr/Ocr';
import { ID_COUNTRY } from 'constants/optionList';
import { LargeLabel } from 'cube-ui-components';
import OcrValidationErrorDialog from 'features/eAppV2/common/components/ocr/OcrValidationErrorDialog';
import OcrValidationErrorDialogButton from 'features/eAppV2/common/components/ocr/OcrValidationErrorDialogButton';
import {
  MismatchFields,
  OcrValidationResult,
  validateOcr,
} from 'features/eAppV2/common/utils/validateOcr';
import useBoundStore from 'hooks/useBoundStore';
import { useMemo, useRef, useState } from 'react';
import { UseFormGetValues, UseFormSetValue } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { TouchableOpacity, View } from 'react-native';
import { RootStackParamList } from 'types';
import { OcrResult } from 'types/ocr';
import { PartyRole } from 'types/party';
import { Gender } from 'types/person';
import { calculateAge } from 'utils/helper/calculateAge';
import { MINOR_AGE_THRESHOLD } from '../constants/partyConfig';
import { MainPartyAddressInformationForm } from '../validations/applicationDetails/sections/addressInformation';
import { MainPartyPersonalDetailsForm } from '../validations/applicationDetails/sections/mainPartyPersonalDetails';
import useOcrPopulatingLogic from './useOcrPopulatingLogic';

export type OCRForm = Pick<
  MainPartyPersonalDetailsForm,
  | 'primaryId'
  | 'maritalStatus'
  | 'fullName'
  | 'dob'
  | 'gender'
  | 'placeOfBirth'
  | 'nationality'
  | 'religion'
> &
  Pick<
    MainPartyAddressInformationForm,
    | 'correspondenceAddressLine1'
    | 'correspondenceAddressLine2'
    | 'correspondenceAddressLine3'
    | 'correspondenceAddressLine4'
  >;

type OcrFile = {
  base64: string;
  name: string;
  thumbnail?: string;
};

export interface OcrLogicProps {
  role:
    | PartyRole.PROPOSER
    | PartyRole.INSURED
    | PartyRole.PAYER
    | PartyRole.RENEWAL_PAYER;
  setValue: UseFormSetValue<OCRForm>;
  getValues: UseFormGetValues<OCRForm>;
  ocrImage: {
    base64?: string;
    name?: string;
    thumbnail?: string;
  };
  isPIEqualPO?: boolean;
  onClose?: () => void;
  defaultIsOcrSuccess?: boolean | null;
}

const useOcrLogic = ({
  role,
  setValue,
  getValues,
  ocrImage,
  isPIEqualPO,
  onClose,
  defaultIsOcrSuccess,
}: OcrLogicProps) => {
  const { t } = useTranslation(['eApp']);
  const ocrRef = useRef<OcrRef>(null);
  const { space, colors } = useTheme();
  const [ocrFile, setOcrFile] = useState<OcrFile>({
    base64: '',
    name: '',
    thumbnail: '',
  });
  // isOcrSuccess === undefined: OCR not yet scanned or invalid (e.g., blurry, blank, or unsupported ID type)
  // isOcrSuccess === true: OCR scanned and data matches, or mismatched but user chose to skip
  // isOcrSuccess === false: OCR scanned and data does not match
  const [isOcrSuccess, setIsOcrSuccess] = useState(
    defaultIsOcrSuccess ?? undefined,
  );
  const [ocrValidationMismatchFields, setOcrValidationMismatchFields] =
    useState<MismatchFields>({});

  const [hasOcrUpdated, setHasOcrUpdated] = useState(false);

  const dob = getValues('dob');
  const nationality = getValues('nationality');

  const age = calculateAge(dob ?? new Date());

  const [ocrValidationResult, setOcrValidationResult] =
    useState<OcrValidationResult>(OcrValidationResult.Match);

  const ocrScanned = useRef(Boolean(ocrImage?.name));

  const { populateOcrData } = useOcrPopulatingLogic(
    role,
    ocrImage,
    setValue,
    getValues,
  );

  const { reset } = useNavigation<NavigationProp<RootStackParamList>>();
  const clearActiveCase = useBoundStore(
    state => state.caseActions.clearActiveCase,
  );

  const [ocrValidationErrorDialogVisible, setOcrValidationErrorDialogVisible] =
    useState(false);

  const [lastOcrResult, setLastOcrResult] = useState<{
    data: OcrResult['extract'];
    documentType: string;
    image: { base64: string; name: string; thumbnail?: string };
  }>();

  const isShowCreateNewQuoteButtonForValidationErrorDialog =
    ((isPIEqualPO && role === PartyRole.PROPOSER) ||
      role === PartyRole.INSURED) &&
    ocrValidationResult === OcrValidationResult.DobOrGenderMismatch;

  const onRetakeForValidationErrorDialog = () => {
    ocrRef?.current?.resetAndOpen();
    setOcrFile({
      base64: '',
      name: '',
      thumbnail: '',
    });
    setLastOcrResult(undefined);
    setIsOcrSuccess(undefined);
    setOcrValidationErrorDialogVisible(false);
  };

  const onSkipForValidationErrorDialog = async () => {
    ocrRef?.current?.reset();
    setOcrValidationErrorDialogVisible(false);
    setOcrValidationMismatchFields({});
    if (lastOcrResult) {
      populateOcrData({
        data: lastOcrResult.data,
        documentType: lastOcrResult.documentType,
        image: lastOcrResult.image,
      });
    }
  };
  const renderValidationDialog = () => (
    <OcrValidationErrorDialog
      visible={ocrValidationErrorDialogVisible}
      fields={ocrValidationMismatchFields}>
      <OcrValidationErrorDialogButton
        text={t('eApp:ocr.error.retake')}
        onPress={onRetakeForValidationErrorDialog}
      />
      <OcrValidationErrorDialogButton
        text={t('eApp:ocr.error.skip')}
        style={{
          marginTop: space[3],
        }}
        variant="text"
        onPress={onSkipForValidationErrorDialog}
      />
      {isShowCreateNewQuoteButtonForValidationErrorDialog && (
        <TouchableOpacity
          onPress={() => {
            setOcrValidationErrorDialogVisible(false);
            onClose?.();
            clearActiveCase();
            reset({
              index: 0,
              routes: [{ name: 'Main' }, { name: 'CoverageDetailsScreen' }],
            });
          }}>
          <LargeLabel
            style={{ marginTop: space[6] }}
            fontWeight="bold"
            color={colors.primary}>
            {t('eApp:ocr.error.newApplication')}
          </LargeLabel>
        </TouchableOpacity>
      )}
    </OcrValidationErrorDialog>
  );
  const onFinish = async (
    data: OcrResult['extract'],
    documentType: string,
    image: {
      base64: string;
      name: string;
      thumbnail?: string;
    },
  ) => {
    ocrScanned.current = true;
    setOcrFile({
      base64: image.base64,
      name: image.name,
      thumbnail: image.thumbnail,
    });
    setLastOcrResult({ data, documentType, image });
    if (role === PartyRole.PROPOSER || role === PartyRole.INSURED) {
      const [result, mismatchFields] = validateOcr(
        {
          firstName: '',
          lastName: '',
          fullName: data.fullName,
          dateOfBirth: data.dateOfBirth,
          gender: data.gender,
        },
        {
          firstName: '',
          lastName: '',
          fullName: getValues('fullName'),
          dateOfBirth: getValues('dob') ?? null,
          gender: getValues('gender') as Gender,
        },
      );

      if (result === OcrValidationResult.Match) {
        setIsOcrSuccess(true);
        setOcrValidationMismatchFields({});
        populateOcrData({ data, documentType, image });
      } else {
        setIsOcrSuccess(false);
        setOcrValidationResult(result);
        setOcrValidationMismatchFields(mismatchFields);
        setOcrValidationErrorDialogVisible(true);
      }
    } else {
      setIsOcrSuccess(true); // For Payer and Renewal Payer, we assume OCR always matches
      populateOcrData({ data, documentType, image });
    }
  };

  const hasMismatchFields = Object.keys(ocrValidationMismatchFields).length > 0;

  const isOCREnabled = useMemo(() => {
    if (nationality !== ID_COUNTRY) return false;
    return age >= MINOR_AGE_THRESHOLD;
  }, [age, nationality]);

  const owbOcrFlag = useMemo(() => {
    return isOCREnabled
      ? !hasMismatchFields && !!isOcrSuccess && !hasOcrUpdated
      : null;
  }, [isOcrSuccess, hasMismatchFields, hasOcrUpdated]);

  // if ocr captured fields are updated manually, then the ocr flag is false
  const onOcrFieldUpdate = () => {
    if (isOCREnabled) {
      setHasOcrUpdated(true);
    }
  };

  return {
    ocrLogic: {
      ocrImage,
      ocrScanned,
      onFinish,
      ocrRef,
      isOcrSuccess,
      isVerifyingCustomer: false,
      renderConfirmationDialog: renderValidationDialog,
      renderLADialog: () => <View />,
      renderValidationDialog: () => <View />,
      ocrValidationMismatchFields: ocrValidationMismatchFields,
      onOcrFieldUpdate,
    },
    isOCREnabled,
    ocrFile,
    owbOcrFlag,
    hasMismatchFields,
  };
};

export default useOcrLogic;
