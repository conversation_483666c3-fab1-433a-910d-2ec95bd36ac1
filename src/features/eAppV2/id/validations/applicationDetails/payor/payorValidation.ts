import { OptionList } from 'types/optionList';
import { Party, PartyRole, PartyType } from 'types/party';
import { InferType } from 'yup';
import {
  othersAddressInformationDefaultValue,
  othersAddressInformationSchema,
  othersAddressInformationToParty,
  partyToOthersAddressInformation,
} from '../sections/addressInformation';
import {
  contactDetailsDefaultValue,
  contactDetailsSchema,
  contactDetailsToParty,
  partyToContactDetails,
} from '../sections/contactDetails';
import {
  occupationDetailsDefaultValue,
  occupationDetailsSchema,
  occupationDetailsToParty,
  partyToOccupationDetails,
} from '../sections/payorOccupationDetails';
import {
  partyToPayorPersonalDetails,
  payorPersonalDetailsDefaultValue,
  payorPersonalDetailsSchema,
  payorPersonalDetailsToParty,
} from '../sections/payorPersonalDetails';

export const payorInfoSchema = payorPersonalDetailsSchema
  .concat(occupationDetailsSchema)
  .concat(contactDetailsSchema)
  .concat(othersAddressInformationSchema);

export type PayorInfoForm = InferType<typeof payorInfoSchema>;

export const payorInfoDefaultValue: PayorInfoForm = {
  ...payorPersonalDetailsDefaultValue,
  ...occupationDetailsDefaultValue,
  ...contactDetailsDefaultValue,
  ...othersAddressInformationDefaultValue,
  contactRole: 'others',
  addressRole: 'others',
};

export const partyToPayorInfo = (
  party?: Party,
  hasPayer?: boolean,
  optionList?: OptionList<string, 'id'>,
): PayorInfoForm => {
  if (!party || !hasPayer) return payorInfoDefaultValue;
  return {
    ...partyToPayorPersonalDetails(party),
    ...partyToOccupationDetails(party, optionList),
    ...partyToContactDetails(party, optionList),
    ...partyToOthersAddressInformation(party),
    contactRole: 'others',
    addressRole: 'others',
  };
};

interface PayorInfoToPartyParams {
  form: PayorInfoForm;
  optionList: OptionList<string, 'id'>;
  role: PartyRole;
  isOcrSuccess?: boolean | null;
  isLivenessCheckVerified?: boolean;
  isFaceMatched?: boolean;
  dukcapilServiceStatus?: string | null;
}

export const payorInfoToParty = ({
  form,
  optionList,
  role,
  isOcrSuccess,
  isLivenessCheckVerified,
  isFaceMatched,
  dukcapilServiceStatus,
}: PayorInfoToPartyParams): Party => {
  const { person: personForPersonalDetails, ...restOfPersonalDetails } =
    payorPersonalDetailsToParty(form);
  const { person: personForOccupationDetails, ...restOfOccupationDetails } =
    occupationDetailsToParty(form, optionList);
  const contactDetails = contactDetailsToParty(form);
  const addressInformation = othersAddressInformationToParty(form);
  return {
    clientType: PartyType.INDIVIDUAL,
    roles: [role],
    ...restOfPersonalDetails,
    ...restOfOccupationDetails,
    ...contactDetails,
    ...addressInformation,
    person: {
      ...personForPersonalDetails,
      ...personForOccupationDetails,
      isOcrSuccess,
      livenessCheck:
        isLivenessCheckVerified == null && isFaceMatched == null
          ? null
          : {
              isLivenessSuccess: isLivenessCheckVerified ?? null,
              isFaceMatched: isFaceMatched ?? null,
            },
      dukcapilServiceStatus,
    } as Party['person'],
  };
};
