import { Application } from 'types/case';
import { OptionList } from 'types/optionList';
import { Party, PartyRole, PartyType } from 'types/party';
import { InferType } from 'yup';
import {
  additionalDetailsDefaultValue,
  additionalDetailsSchema,
  additionalDetailsToParty,
  partyToAdditionalDetails,
} from '../sections/additionalDetails';
import {
  mainPartyAddressInformationDefaultValue,
  mainPartyAddressInformationSchema,
  mainPartyAddressInformationToParty,
  partyToMainPartyAddressInformation,
} from '../sections/addressInformation';
import {
  contactDetailsDefaultValue,
  contactDetailsSchema,
  contactDetailsToParty,
  partyToContactDetails,
} from '../sections/contactDetails';
import {
  mainPartyPersonalDetailsDefaultValue,
  mainPartyPersonalDetailsSchema,
  mainPartyPersonalDetailsToParty,
  partyToMainPartyPersonalDetails,
} from '../sections/mainPartyPersonalDetails';
import {
  occupationDetailsDefaultValue,
  occupationDetailsSchema,
  occupationDetailsToParty,
  partyToOccupationDetails,
} from '../sections/occupationDetails';
import {
  applicationToPremiumPayment,
  premiumPaymentDefaultValue,
  premiumPaymentSchema,
} from '../sections/premiumPayment';

import {
  partyToPassionSurvey,
  passionSurveyDefaultValue,
  passionSurveySchema,
  passionSurveyToParty,
} from '../passionSurveyValidation';

export const policyOwnerInfoSchema = mainPartyPersonalDetailsSchema
  .concat(occupationDetailsSchema)
  .concat(contactDetailsSchema)
  .concat(mainPartyAddressInformationSchema)
  .concat(additionalDetailsSchema)
  .concat(premiumPaymentSchema)
  .concat(passionSurveySchema);

export type PolicyOwnerInfoForm = InferType<typeof policyOwnerInfoSchema>;

export const policyOwnerInfoDefaultValue = {
  ...mainPartyPersonalDetailsDefaultValue,
  ...occupationDetailsDefaultValue,
  ...contactDetailsDefaultValue,
  ...mainPartyAddressInformationDefaultValue,
  ...additionalDetailsDefaultValue,
  ...premiumPaymentDefaultValue,
  ...passionSurveyDefaultValue,
} as PolicyOwnerInfoForm;

export const partyToPolicyOwnerInfo = (
  party?: Party,
  application?: Application,
  optionList?: OptionList<string, 'id'>,
): PolicyOwnerInfoForm => {
  if (!party) return policyOwnerInfoDefaultValue;

  return {
    ...partyToMainPartyPersonalDetails(party, optionList),
    ...partyToOccupationDetails(party, optionList),
    ...partyToContactDetails(party, optionList),
    ...partyToMainPartyAddressInformation(party),
    ...partyToAdditionalDetails(party),
    ...applicationToPremiumPayment(application),
    ...partyToPassionSurvey(party),
    personalDetailsRole: 'owner',
    contactRole: 'owner',
    addressRole: 'owner',
    occupationRole: 'mainParty',
  };
};

interface PolicyOwnerInfoToPartyParams {
  form: PolicyOwnerInfoForm;
  roles: PartyRole[];
  optionList: OptionList<string, 'id'>;
  isOcrSuccess?: boolean | null;
  isLivenessCheckVerified?: boolean;
  isFaceMatched?: boolean;
  dukcapilServiceStatus?: string | null;
}

export const policyOwnerInfoToParty = ({
  form,
  roles,
  optionList,
  isOcrSuccess,
  isLivenessCheckVerified,
  isFaceMatched,
  dukcapilServiceStatus,
}: PolicyOwnerInfoToPartyParams): Party => {
  const { person: personForPersonalDetails, ...restOfPersonalDetails } =
    mainPartyPersonalDetailsToParty(form);
  const { person: personForOccupationDetails, ...restOfOccupationDetails } =
    occupationDetailsToParty(form, optionList);
  const contactDetails = contactDetailsToParty(form);
  const addressInformation = mainPartyAddressInformationToParty(form);
  const { person: personAdditionalDetails } = additionalDetailsToParty(form);
  const passionSurvey = passionSurveyToParty(form);
  return {
    clientType: PartyType.INDIVIDUAL,
    roles,
    ...restOfPersonalDetails,
    ...restOfOccupationDetails,
    ...contactDetails,
    ...addressInformation,
    ...passionSurvey,
    person: {
      ...personForPersonalDetails,
      ...personForOccupationDetails,
      ...personAdditionalDetails,
      livenessCheck:
        isLivenessCheckVerified == null && isFaceMatched == null
          ? null
          : {
              isLivenessSuccess: isLivenessCheckVerified ?? null,
              isFaceMatched: isFaceMatched ?? null,
            },
      isOcrSuccess,
      dukcapilServiceStatus,
    } as Party['person'],
  };
};
