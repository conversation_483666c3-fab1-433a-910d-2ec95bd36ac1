import { ID_OPTION_LIST, RENEWAL_PAYMENT_METHOD } from 'constants/optionList';
import {
  isFuture,
  setMonth,
  setYear,
  startOfDay,
  startOfMonth,
} from 'date-fns';
import { Case } from 'types/case';
import {
  RenewalPayStoreRequest,
  WithdrawalPayStoreRequest,
} from 'types/paymentSetup';
import * as yup from 'yup';
import {
  invalidAccountNumber,
  invalidBankAccountNumberLength,
  invalidCreditCardFormat,
  invalidCreditCardLength,
  invalidDateMessageExpired,
  invalidFormatMessage,
  maxLength60Message,
  requiredMessage,
} from '../../common/constants/eAppErrorMessages';
import { PaymentSetupRetrieveResponse } from './../hooks/usePaymentSetupRetrieve';
import { maxNameLength } from './applicationDetails/sections/personalDetailsValidation';

export enum MAX_LENGTH_BY_BANK_CODE {
  MANDIRI = 13,
  BCA = 10,
  BRI = 15,
}

export enum BANK_CODE {
  MANDIRI = 'Mandiri',
  BCA = 'BCA',
  BRI = 'BRI',
}

const BANK_LENGTH_MAP: Record<BANK_CODE, number> = {
  [BANK_CODE.MANDIRI]: MAX_LENGTH_BY_BANK_CODE.MANDIRI,
  [BANK_CODE.BCA]: MAX_LENGTH_BY_BANK_CODE.BCA,
  [BANK_CODE.BRI]: MAX_LENGTH_BY_BANK_CODE.BRI,
};

export const prefixCreditCardACA = '515603';
export const creditBankACA = 'ACA';

const creditExpiryDateRegex = /^[0-9]{2}\/[0-9]{2}$/;
const numericRegex = /^[0-9]*$/;
const withdrawalAccountNumberLength = 10;
const creditCardLength = 16;

export const renewalPaymentSetupSchema = yup.object({
  paymentMethod: yup
    .string()
    .oneOf(ID_OPTION_LIST.RENEWAL_PAYMENT_METHODS)
    .required(requiredMessage),
  cardHolder: yup.string().when('paymentMethod', {
    is: RENEWAL_PAYMENT_METHOD.CREDIT_CARD,
    then: schema =>
      schema
        .required(requiredMessage)
        .max(maxNameLength, maxLength60Message)
        .validateName(invalidFormatMessage),
    otherwise: schema => schema,
  }),
  cardNumber: yup.string().when('paymentMethod', {
    is: RENEWAL_PAYMENT_METHOD.CREDIT_CARD,
    then: schema =>
      schema
        .required(requiredMessage)
        .test(
          'credit-card-number',
          invalidCreditCardFormat,
          function (value, ctx) {
            if (!value) {
              return true;
            }
            if (!value.match(numericRegex)) {
              return ctx.createError({
                path: 'cardNumber',
                message: invalidFormatMessage,
              });
            }

            if (value.length !== creditCardLength) {
              return ctx.createError({
                path: 'cardNumber',
                message: invalidCreditCardLength,
                params: { length: creditCardLength },
              });
            }

            return true;
          },
        ),
    otherwise: schema => schema,
  }),
  cardExpiryDate: yup.string().when('paymentMethod', {
    is: RENEWAL_PAYMENT_METHOD.CREDIT_CARD,
    then: schema =>
      schema.required(requiredMessage).test({
        name: 'creditExpiryDateValidation',
        test: value => {
          if (!value.match(creditExpiryDateRegex)) {
            return false;
          }
          const [month, year] = value.split('/').map(value => Number(value));
          if (month < 1 || month > 12) {
            return false;
          }
          let expiryDate = startOfDay(new Date());
          expiryDate = startOfMonth(expiryDate);
          expiryDate = setMonth(expiryDate, month - 1);
          expiryDate = setYear(expiryDate, 2000 + year);
          if (!isFuture(expiryDate)) {
            return false;
          }
          return true;
        },
        message: invalidDateMessageExpired,
      }),
    otherwise: schema => schema,
  }),
  bankNameForRenewal: yup.string().when('paymentMethod', {
    is: RENEWAL_PAYMENT_METHOD.AUTO_DEBIT,
    then: schema => schema.required(requiredMessage),
    otherwise: schema => schema,
  }),
  accountHolderForRenewal: yup.string().when('paymentMethod', {
    is: RENEWAL_PAYMENT_METHOD.AUTO_DEBIT,
    then: schema =>
      schema
        .required(requiredMessage)
        .max(maxNameLength, maxLength60Message)
        .validateName(invalidFormatMessage),
    otherwise: schema => schema,
  }),
  accountNumberForRenewal: yup
    .string()
    .when(['paymentMethod', 'bankNameForRenewal'], {
      is: (paymentMethod: string, bankName: string) =>
        paymentMethod === RENEWAL_PAYMENT_METHOD.AUTO_DEBIT && bankName,
      then: schema =>
        schema
          .required(requiredMessage)
          .test('account-number', invalidAccountNumber, function (value, ctx) {
            const bankName: BANK_CODE = ctx.resolve(
              yup.ref('bankNameForRenewal'),
            );

            if (!value.match(numericRegex)) {
              return ctx.createError({
                path: 'accountNumberForRenewal',
                message: invalidFormatMessage,
              });
            }

            const requiredLength = BANK_LENGTH_MAP[bankName];
            if (requiredLength && value.length !== requiredLength) {
              return ctx.createError({
                path: 'accountNumberForRenewal',
                message: invalidBankAccountNumberLength,
                params: { length: requiredLength },
              });
            }

            return true;
          }),
      otherwise: schema => schema,
    }),
});

export type RenewalPaymentSetupForm = yup.InferType<
  typeof renewalPaymentSetupSchema
>;

export const renewalPaymentSetupDefaultValue: RenewalPaymentSetupForm = {
  paymentMethod: RENEWAL_PAYMENT_METHOD.NA,
  cardHolder: '',
  cardNumber: '',
  cardExpiryDate: '',
  bankNameForRenewal: '',
  accountHolderForRenewal: '',
  accountNumberForRenewal: '',
};

export const paymentSetupDefaultValue: RenewalPaymentSetupForm = {
  paymentMethod: RENEWAL_PAYMENT_METHOD.NA,
};

export const owbPaymentSetupRetrieveToRenewalPaymentSetupForm = (
  response?: PaymentSetupRetrieveResponse,
): RenewalPaymentSetupForm => {
  const directCredit = response?.application.directCredit;
  switch (directCredit?.paymentMethod) {
    case RENEWAL_PAYMENT_METHOD.CREDIT_CARD:
      return {
        ...paymentSetupDefaultValue,
        paymentMethod: directCredit?.paymentMethod,
        cardHolder: directCredit?.cardHolder,
        cardNumber: directCredit?.cardNumber,
        cardExpiryDate: directCredit?.cardExpiryDate,
      };
    case RENEWAL_PAYMENT_METHOD.AUTO_DEBIT:
      return {
        ...paymentSetupDefaultValue,
        paymentMethod: directCredit?.paymentMethod,
        bankNameForRenewal: directCredit?.bankNameForRenewal,
        accountHolderForRenewal: directCredit?.accountHolderForRenewal,
        accountNumberForRenewal: directCredit?.accountNumberForRenewal,
      };
    default:
      return {
        ...paymentSetupDefaultValue,
        paymentMethod: directCredit?.paymentMethod || RENEWAL_PAYMENT_METHOD.NA,
      };
  }
};

export const renewalPaymentSetupFormToRenewalPayStore = (
  form: RenewalPaymentSetupForm,
  caseObj: Case,
): RenewalPayStoreRequest => {
  let directCredit = { ...form };

  switch (directCredit.paymentMethod) {
    case RENEWAL_PAYMENT_METHOD.CREDIT_CARD:
      directCredit = {
        paymentMethod: form.paymentMethod,
        cardHolder: form?.cardHolder || '',
        cardNumber: form?.cardNumber || '',
        cardExpiryDate: form?.cardExpiryDate || '',
      };
      break;
    case RENEWAL_PAYMENT_METHOD.AUTO_DEBIT:
      directCredit = {
        paymentMethod: form.paymentMethod,
        bankNameForRenewal: form?.bankNameForRenewal || '',
        accountHolderForRenewal: form?.accountHolderForRenewal || '',
        accountNumberForRenewal: form?.accountNumberForRenewal || '',
      };
      break;
    default:
      directCredit = { paymentMethod: form.paymentMethod };
  }

  return {
    application: {
      agent: {
        agentCode: caseObj?.application?.agent?.agentCode || '',
      },
      applicationNum: caseObj?.application?.applicationNum || '',
      policyNum: caseObj?.application?.policyNum || '',
      directCredit: directCredit,
    },
  };
};

export const withdrawalPaymentSetupSchema = yup
  .object({
    bankName: yup.string(),
    accountHolderName: yup
      .string()
      .max(maxNameLength, maxLength60Message)
      .validateName(invalidFormatMessage),
    accountNumber: yup
      .string()
      .test('account-number', invalidFormatMessage, function (value, ctx) {
        // Only run validation if value exists
        if (!value) return true;

        if (!value.match(numericRegex)) {
          return ctx.createError({
            path: 'accountNumber',
            message: invalidFormatMessage,
          });
        }

        if (value.length !== withdrawalAccountNumberLength) {
          return ctx.createError({
            path: 'accountNumber',
            message: invalidAccountNumber,
          });
        }

        return true;
      }),
  })
  .test('withdrawal-form-validation', '', function (values) {
    const { bankName, accountHolderName, accountNumber } = values;

    // Check if any field has a value
    const hasAnyValue = Object.values(values).some(
      value => value && value.trim() !== '',
    );

    // If no fields have values, the form is valid (optional)
    if (!hasAnyValue) {
      return true;
    }

    // If any field has a value, all fields are required
    const errors: yup.ValidationError[] = [];

    if (!bankName || bankName.trim() === '') {
      errors.push(
        this.createError({
          path: 'bankName',
          message: requiredMessage,
        }),
      );
    }

    if (!accountHolderName || accountHolderName.trim() === '') {
      errors.push(
        this.createError({
          path: 'accountHolderName',
          message: requiredMessage,
        }),
      );
    }

    if (!accountNumber || accountNumber.trim() === '') {
      errors.push(
        this.createError({
          path: 'accountNumber',
          message: requiredMessage,
        }),
      );
    }

    if (errors.length > 0) {
      throw new yup.ValidationError(errors);
    }

    return true;
  });

export type WithdrawalPaymentSetupForm = yup.InferType<
  typeof withdrawalPaymentSetupSchema
>;

export const owbPaymentSetupRetrieveToWithdrawalPaymentSetupForm = (
  response?: PaymentSetupRetrieveResponse,
): WithdrawalPaymentSetupForm => {
  const directCredit = response?.application.directCredit;
  return {
    bankName: directCredit?.bankNameForWithdrawal || '',
    accountHolderName: directCredit?.accountHolderForWithdrawal || '',
    accountNumber: directCredit?.accountNumberForWithdrawal || '',
  };
};

export const withdrawalPaymentSetupFormToWithdrawalPayStore = (
  form: WithdrawalPaymentSetupForm,
  caseObj: Case,
): WithdrawalPayStoreRequest => {
  return {
    application: {
      agent: {
        agentCode: caseObj?.application?.agent?.agentCode || '',
      },
      applicationNum: caseObj?.application?.applicationNum || '',
      policyNum: caseObj?.application?.policyNum || '',
      directCredit: {
        bankNameForWithdrawal: form?.bankName || '',
        accountNumberForWithdrawal: form?.accountNumber || '',
        accountHolderForWithdrawal: form?.accountHolderName || '',
      },
    },
  };
};
