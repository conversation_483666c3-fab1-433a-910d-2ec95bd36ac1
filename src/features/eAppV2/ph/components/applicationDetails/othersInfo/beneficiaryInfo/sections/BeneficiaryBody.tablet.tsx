import { useTheme } from '@emotion/react';
import { Body, Box, LargeBody } from 'cube-ui-components';
import { EAppState } from 'features/eAppV2/common/utils/store/eAppStore';
import React, {
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import styled from '@emotion/native';
import { BeneficiaryFormTablet } from './BeneficiaryForm.tablet';
import { BeneficiaryPrimaryItemProps } from '../beneficiaryAllocation/BenefitPercentage';
import useLatest from 'hooks/useLatest';

interface BeneficiaryBodyTabletProps {
  beneficiaryType?: 'PB' | 'SB' | '';
  beneficiariesPersonalInfo: EAppState['beneficiariesPersonalInfo'];
  benefitAllocationBeneficiaries: BeneficiaryPrimaryItemProps[];
  setTabletActionDisabled: (disabled: boolean) => void;
  autoAssignPB?: boolean;
}

export const BeneficiaryBodyTablet = React.forwardRef(
  (props: BeneficiaryBodyTabletProps, ref) => {
    const { space, colors } = useTheme();
    const {
      beneficiaryType = 'PB',
      beneficiariesPersonalInfo,
      benefitAllocationBeneficiaries,
      setTabletActionDisabled,
      autoAssignPB,
    } = props;
    const currentBenefitExpandedIndex = useMemo(() => {
      return (beneficiariesPersonalInfo?.length ?? 0) - 1;
    }, [beneficiariesPersonalInfo]);

    const refList = useRef<
      Array<
        React.RefObject<{
          submit: (
            isSaved?: boolean,
          ) => Promise<EAppState['beneficiariesPersonalInfo'][0] | undefined>;
          isReadyToSave: () => boolean;
        } | null>
      >
    >([]);

    useEffect(() => {
      refList.current = [];
    }, [beneficiariesPersonalInfo.length]);

    useImperativeHandle(ref, () => ({
      submit: async (isSaved?: boolean) => {
        const allBeneficiaries: EAppState['beneficiariesPersonalInfo'] = [];
        for (const ref of refList.current) {
          if (ref.current?.submit) {
            const result = await ref.current.submit(isSaved);
            if (!result) {
              throw new Error('Invalid contact');
            }
            allBeneficiaries.push(result);
          } else {
            continue;
          }
        }
        return allBeneficiaries;
      },
      isReadyToSave: () => {
        for (const ref of refList.current) {
          if (ref.current?.isReadyToSave) {
            const ready = ref.current.isReadyToSave();
            if (!ready) {
              return false;
            }
          }
        }
        return true;
      },
    }));

    const validList = useRef<(boolean | undefined)[]>([]);

    const latestSetTabletActionDisabled = useLatest(setTabletActionDisabled);

    const onChangeValid = useCallback((index: number, valid: boolean) => {
      validList.current[index] = valid;
      const isValid = !validList.current.includes(false);
      latestSetTabletActionDisabled.current(!isValid);
    }, []);

    const onDeleteValid = useCallback((index: number) => {
      validList.current[index] = undefined;
      validList.current = validList.current.filter(
        i => typeof i !== 'undefined',
      );
      const isValid = !validList.current.includes(false);
      latestSetTabletActionDisabled.current(!isValid);
    }, []);

    return (
      <Box>
        {beneficiaryType === 'SB' && (
          <Box mt={space[4]}>
            <LargeBody color={colors.placeholder}>
              Secondary beneficiary is an optional setup.
            </LargeBody>
          </Box>
        )}
        {beneficiariesPersonalInfo.length > 0 &&
          benefitAllocationBeneficiaries.length > 0 && (
            <FormContainer>
              {beneficiariesPersonalInfo.map((beneficiary, position) => {
                const ref =
                  refList.current[position] ?? React.createRef<null>();
                refList.current[position] = ref;
                return (
                  <React.Fragment key={`${beneficiary.index}`}>
                    <BeneficiaryFormTablet
                      data={beneficiary}
                      position={position}
                      latestPosition={
                        (beneficiariesPersonalInfo?.length ?? 0) - 1
                      }
                      isExpanded={
                        currentBenefitExpandedIndex === position ||
                        beneficiariesPersonalInfo?.length <= 1
                      }
                      isDeletable={
                        beneficiaryType !== 'PB' ||
                        beneficiariesPersonalInfo.length > 1
                      }
                      ref={ref}
                      onChangeValid={onChangeValid}
                      onDeleteValid={onDeleteValid}
                      autoAssignPB={autoAssignPB}
                    />
                  </React.Fragment>
                );
              })}
            </FormContainer>
          )}
      </Box>
    );
  },
);

const FormContainer = styled.View(() => {
  return {
    overflow: 'hidden',
  };
});
