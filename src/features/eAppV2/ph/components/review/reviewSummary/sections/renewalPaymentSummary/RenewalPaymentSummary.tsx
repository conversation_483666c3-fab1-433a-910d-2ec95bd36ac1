import { memo } from 'react';
import ReviewSection from '../../../../../../common/components/review/ReviewSection';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { Typography } from 'cube-ui-components';
import LabelView from '../../../../../../common/components/review/common/CommonLabelView';
import { useTheme } from '@emotion/react';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import { extraFormMethods } from 'features/eAppV2/ph/components/renewalPaymentSetup/RenewalPaymentSetup.phone';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useSelectedQuotation } from 'hooks/useSelectedQuotation';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import DeviceBasedRendering from 'components/DeviceBasedRendering';

const Container = styled.View(({ theme }) => {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return {
    paddingVertical: theme.space[isTabletMode ? 5 : 4],
  };
});
const MethodLabel = styled(Typography.Label)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
  marginBottom: theme.space[1],
}));

const MethodLabelTablet = styled(Typography.SmallLabel)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarker,
  marginBottom: theme.space[1],
}));
export const Title = styled(Typography.LargeLabel)(({ theme }) => ({
  color: theme.colors.onBackground,
}));

export const Divider = styled.View(({ theme }) => ({
  marginVertical: theme.space[4],
  height: 1,
  backgroundColor: '#D9D9D9',
}));

const LabelViewContainer = styled.View(({ theme }) => {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return {
    paddingTop: theme.space[1],
    flexDirection: isTabletMode ? 'row' : 'column',
    flexWrap: isTabletMode ? 'wrap' : 'nowrap',
  };
});

export const RenewalPaymentSummary = memo(function () {
  const { t } = useTranslation(['eApp']);

  const renewalPaymentSetup = useEAppStore(state => state.renewalPaymentSetup);
  const hasBankDetails = useEAppStore(state => state.hasBankDetails);
  const bankDetails = useEAppStore(state => state.bankDetails);
  const paymentMethod = renewalPaymentSetup.paymentMethod;
  const quotation = useSelectedQuotation();
  const { data: optionList } = useGetOptionList();
  const bankDetailsBankList =
    quotation?.basicInfo.currency === 'USD'
      ? optionList?.BANK_NAME_USD_LIST.options
      : optionList?.BANK_NAME_PESO_LIST.options;

  const { space } = useTheme();

  const { isTabletMode } = useLayoutAdoptionCheck();

  return (
    <ReviewSection title={t('eApp:review.renewalPremiumPayment')}>
      <Container>
        <DeviceBasedRendering
          phone={
            <MethodLabel>
              {t('eApp:review.renewalPremiumPaymentMethod')}
            </MethodLabel>
          }
          tablet={
            <MethodLabelTablet>
              {t('eApp:review.renewalPremiumPaymentMethod')}
            </MethodLabelTablet>
          }
        />
        <Title fontWeight={isTabletMode ? 'normal' : 'bold'}>
          {paymentMethod
            ? t(`eApp:paymentSetup.paymentMethodList.${paymentMethod}`)
            : ''}
        </Title>
        {extraFormMethods.includes(paymentMethod) && (
          <>
            <Divider />
            <Title fontWeight="bold">
              {paymentMethod === 'aca' &&
                t(
                  'eApp:paymentSetup.autoCreditArrangement.creditCardHolderInfo',
                )}
              {paymentMethod === 'adda' &&
                t('eApp:paymentSetup.autoDirectDebitArrangement.accountInfo')}
            </Title>
          </>
        )}
        {paymentMethod === 'aca' && (
          <LabelViewContainer>
            <LabelView
              label={t('eApp:review.cardHolderName')}
              text={renewalPaymentSetup.creditCardHolderName || ''}
              topSpacing={space[3]}
            />
            <LabelView
              label={t('eApp:review.cardCompany')}
              text={
                renewalPaymentSetup?.creditCardCompany
                  ? optionList?.CREDIT_CARD_COMPANY_LIST?.options.find(
                      el => el.value === renewalPaymentSetup?.creditCardCompany,
                    )?.label || ''
                  : ''
              }
              topSpacing={space[3]}
            />
            <LabelView
              label={t('eApp:review.cardNumber')}
              text={renewalPaymentSetup.creditCardNumber || ''}
              topSpacing={space[3]}
            />
            <LabelView
              label={t('eApp:review.expiryDateMMYY')}
              text={renewalPaymentSetup.creditExpiryDate || ''}
              topSpacing={space[3]}
            />
            <LabelView
              label={t('eApp:review.issuedBySecurityBank')}
              text={
                renewalPaymentSetup.creditIsIssuedBySecBankCorp === 'yes'
                  ? t('eApp:yes')
                  : t('eApp:no')
              }
              topSpacing={space[3]}
            />
          </LabelViewContainer>
        )}
        {paymentMethod === 'adda' && (
          <LabelViewContainer>
            <LabelView
              label={t('eApp:paymentSetup.autoDirectDebitArrangement.bankName')}
              text={
                renewalPaymentSetup?.debitBankName
                  ? optionList?.BANK_NAME_LIST?.options.find(
                      el => el.value === renewalPaymentSetup?.debitBankName,
                    )?.label || ''
                  : ''
              }
              topSpacing={space[3]}
            />
            <LabelView
              label={t(
                'eApp:paymentSetup.autoDirectDebitArrangement.accountHolderName',
              )}
              text={renewalPaymentSetup?.debitAccountHolderName || ''}
              topSpacing={space[3]}
            />
            <LabelView
              label={t(
                'eApp:paymentSetup.autoDirectDebitArrangement.accountNumber',
              )}
              text={renewalPaymentSetup?.debitAccountNumber || ''}
              topSpacing={space[3]}
            />
          </LabelViewContainer>
        )}
        {hasBankDetails && (
          <>
            <Divider />
            <Title fontWeight="bold">
              {hasBankDetails && t('eApp:paymentSetup.bankDetails.title')}
            </Title>
          </>
        )}
        {hasBankDetails && (
          <LabelViewContainer>
            <LabelView
              label={t('eApp:paymentSetup.bankDetails.bankName')}
              text={
                bankDetailsBankList?.find(
                  bank => bank.value === bankDetails.bankName,
                )?.label || ''
              }
              topSpacing={space[3]}
            />
            <LabelView
              label={t('eApp:paymentSetup.bankDetails.branchName')}
              text={bankDetails.branchName}
              topSpacing={space[3]}
            />
            <LabelView
              label={t('eApp:paymentSetup.bankDetails.accountHolderName')}
              text={bankDetails.accountHolderName || ''}
              topSpacing={space[3]}
            />
            <LabelView
              label={t('eApp:paymentSetup.bankDetails.accountNumber')}
              text={bankDetails.accountNumber || ''}
              topSpacing={space[3]}
            />
            <LabelView
              label={t('eApp:paymentSetup.bankDetails.proofOfBank')}
              text={bankDetails.proofOfBankAccountDetails || ''}
              topSpacing={space[3]}
            />
          </LabelViewContainer>
        )}
      </Container>
    </ReviewSection>
  );
});
export default RenewalPaymentSummary;
