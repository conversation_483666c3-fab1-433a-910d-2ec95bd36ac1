import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useEAppStore } from 'features/eAppV2/common/utils/store/eAppStore';
import { ProofOfBankAccountDetails } from 'features/eAppV2/ph/types/documentTypes';
import { PartyRole, PartyType } from 'types/party';
import { DocumentCustomerType, DocumentType } from 'types/document';
import { useGetOptionList } from 'hooks/useGetOptionList';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import DocumentUploadBase, {
  DocumentUploadTab,
  DocumentUploadType,
} from 'features/eAppV2/common/components/documentUpload/DocumentUploadBase';
import { filterIrrevocableBeneficiariesFromParty } from '../../utils/partyUtils';
import { OptionList } from 'types/optionList';
import { isRole } from 'features/eAppV2/common/utils/partyUtils';
import { PHRouteItemKey } from 'features/eAppV2/common/types/progressBarTypes';
import { Case } from 'types/case';
import { TFuncKey } from 'i18next';
import { useCheckEntity } from 'features/eAppV2/common/hooks/useCheckEntity';

export default function DocumentUpload() {
  const { t } = useTranslation(['eApp']);
  const hasBankDetails = useEAppStore(state => state.hasBankDetails);

  const { caseObj } = useGetActiveCase();
  const { data: optionList } = useGetOptionList();
  const isEntity = useCheckEntity();

  const tabs = useMemo<DocumentUploadTab[]>(() => {
    const tabs: DocumentUploadTab[] = [];
    let insuredCount = 0;
    let beneficiaryCount = 0;
    let adultBeneficiaryCount = 0;
    let trusteeCount = 0;

    const owner =
      caseObj?.parties?.filter(p => p.roles.includes(PartyRole.PROPOSER)) || [];
    const insureds =
      caseObj?.parties?.filter(
        p =>
          p.roles.includes(PartyRole.INSURED) &&
          !p.roles.includes(PartyRole.PROPOSER),
      ) || [];
    const beneficialOwner =
      caseObj?.parties?.filter(p =>
        p.roles.includes(PartyRole.BENEFICIAL_OWNER),
      ) || [];
    const payor =
      caseObj?.parties?.filter(p => p.roles.includes(PartyRole.PAYER)) || [];
    const irrevocableBeneficiaries =
      filterIrrevocableBeneficiariesFromParty(
        caseObj?.parties?.filter(p => p.roles.includes(PartyRole.BENEFICIARY)),
      ) || [];

    [
      ...owner,
      ...insureds,
      ...beneficialOwner,
      ...payor,
      ...irrevocableBeneficiaries,
    ].forEach(party => {
      let customerType: DocumentCustomerType = DocumentCustomerType.NA;
      let role = '';
      let key: PHRouteItemKey | '' = '';
      let name = '';

      const isProposer = isRole(party, PartyRole.PROPOSER);
      const isInsured = isRole(party, PartyRole.INSURED);

      if (isProposer) {
        customerType = DocumentCustomerType.PO;
        role = isEntity
          ? t('eApp:documentUpload.authorizedRepresentative')
          : t('eApp:documentUpload.policyOwner');
        key = 'policyOwner';
        name = isEntity
          ? party.entity?.representative?.name?.fullName?.trim() || ''
          : party.person?.name.fullName?.trim() || '';
      } else if (isInsured) {
        insuredCount += 1;
        customerType = DocumentCustomerType.PI;
        role = t('eApp:documentUpload.insured');
        key = 'insured';
        name = party.person?.name.fullName?.trim() || '';
      } else if (isRole(party, PartyRole.BENEFICIAL_OWNER)) {
        customerType = DocumentCustomerType.BO;
        role = t('eApp:documentUpload.beneficialOwner');
        key = 'beneficialOwner';
        name = party.person?.name.fullName?.trim() || '';
      } else if (isRole(party, PartyRole.PAYER)) {
        customerType = DocumentCustomerType.PAY;
        role = t('eApp:documentUpload.partyPayor');
        key = 'payor';
        name = party.person?.name.fullName?.trim() || '';
      } else if (isRole(party, PartyRole.BENEFICIARY)) {
        beneficiaryCount += 1;
        if (party.person?.age && party.person?.age >= 18) {
          customerType = DocumentCustomerType.B;
          adultBeneficiaryCount += 1;
          role = `${t(
            'eApp:documentUpload.beneficiary',
          )} ${adultBeneficiaryCount}`;
          name = party.person?.name.fullName?.trim() || '';
        } else {
          customerType = DocumentCustomerType.TT;
          trusteeCount += 1;
          role = `${t('eApp:documentUpload.trustee')} ${trusteeCount}`;
          name = party.beneficiarySetting?.trusteeName || '';
        }
        key = `beneficiary${beneficiaryCount}`;
      }
      const renewalPaymentOption =
        caseObj?.application?.directCredit?.paymentMethod;
      const hasAdditionalRenewalPaymentDoc =
        isProposer &&
        (renewalPaymentOption === 'aca' || renewalPaymentOption === 'adda');

      const hasAdditionalBankDetailsDoc = isProposer && hasBankDetails;

      tabs.push({
        partyId: party.id,
        customerSeq: isInsured ? String(insuredCount) : '1',
        customerType,
        role,
        key,
        name,
        title:
          isRole(party, PartyRole.BENEFICIAL_OWNER) && isEntity
            ? t('eApp:documentUpload.documentForBeneficialOwner')
            : undefined,
        documents:
          isRole(party, PartyRole.BENEFICIAL_OWNER) && isEntity
            ? ([
                {
                  title: t('eApp:documentUpload.certificateOnBeneficialOwner', {
                    type:
                      findDocumentTypeLabel(
                        party.person?.registrations?.find(
                          r => r.type === 'PRIMARY',
                        )?.idType,
                        optionList,
                      ) || t('eApp:document'),
                  }),
                  type: DocumentType.CertOnBO,
                },
                party.person?.greenCard?.isGreenCardHolder
                  ? {
                      title: t('eApp:documentUpload.IRSWForm', {
                        type:
                          findDocumentTypeLabel(
                            party.person?.registrations?.find(
                              r => r.type === 'PRIMARY',
                            )?.idType,
                            optionList,
                          ) || t('eApp:document'),
                      }),
                      type: DocumentType.IRSWForm,
                    }
                  : undefined,
              ].filter(Boolean) as DocumentUploadType[])
            : ([
                {
                  title: t('eApp:documentUpload.frontOfId', {
                    type:
                      findDocumentTypeLabel(
                        party.person?.registrations?.find(
                          r => r.type === 'PRIMARY',
                        )?.idType,
                        optionList,
                      ) || (isProposer && isEntity ? 'ID' : t('eApp:document')),
                  }),
                  type: DocumentType.FrontID,
                  silentOCREnabled:
                    (isRole(party, PartyRole.PROPOSER) &&
                      party.clientType === PartyType.INDIVIDUAL) ||
                    isRole(party, PartyRole.INSURED),
                },
                {
                  title: t('eApp:documentUpload.backOfId', {
                    type:
                      findDocumentTypeLabel(
                        party.person?.registrations?.find(
                          r => r.type === 'PRIMARY',
                        )?.idType,
                        optionList,
                      ) || (isProposer && isEntity ? 'ID' : t('eApp:document')),
                  }),
                  type: DocumentType.BackID,
                },
                hasAdditionalRenewalPaymentDoc && {
                  title: t(getAdditionalDocTitle(caseObj)),
                  type: getAdditionalDocTypeForRenewalPayment(caseObj),
                  multiple: true,
                },
                hasAdditionalBankDetailsDoc && {
                  title: t(getAdditionalDocTitle(caseObj, hasBankDetails)),
                  type: getAdditionalDocTypeForBankDetails(
                    caseObj,
                    hasBankDetails,
                  ),
                  multiple: true,
                },
              ].filter(Boolean) as DocumentUploadType[]),
      });
    });
    return tabs;
  }, [caseObj, hasBankDetails, optionList, t, isEntity]);

  return <DocumentUploadBase groupedSections tabs={tabs} />;
}

export const findDocumentTypeLabel = (type?: string, optionList?: OptionList) =>
  optionList?.PRIMARY_ID_TYPE.options.find(opt => opt.value === type)?.label ||
  '';

export const getAdditionalDocTitle = (
  caseObj?: Case,
  hasBankDetails?: boolean,
): TFuncKey<['eApp']> => {
  if (hasBankDetails) {
    return 'eApp:documentUpload.bankAccount';
  }
  const renewalPaymentOption =
    caseObj?.application?.directCredit?.paymentMethod;
  switch (renewalPaymentOption) {
    case 'aca':
      return 'eApp:documentUpload.aca';
    case 'adda':
      return 'eApp:documentUpload.ada';
  }
  return '' as TFuncKey<['eApp']>;
};

const getAdditionalDocTypeForRenewalPayment = (
  caseObj?: Case,
): DocumentType => {
  const renewalPaymentOption =
    caseObj?.application?.directCredit?.paymentMethod;
  switch (renewalPaymentOption) {
    case 'aca':
      return DocumentType.ACA;
    case 'adda':
      return DocumentType.Adda;
    default:
      return DocumentType.Unknown;
  }
};

const getAdditionalDocTypeForBankDetails = (
  caseObj?: Case,
  hasBankDetails?: boolean,
): DocumentType => {
  if (
    hasBankDetails &&
    caseObj?.application?.directCredit &&
    'proof' in caseObj.application.directCredit
  ) {
    switch (caseObj?.application?.directCredit.proof) {
      case ProofOfBankAccountDetails.BANK_CERTIFICATE:
        return DocumentType.BankCertificate;
      case ProofOfBankAccountDetails.STATEMENT_ACCOUNT:
        return DocumentType.StatementOfAccount;
      case ProofOfBankAccountDetails.ATM:
        return DocumentType.ATMAccountNumber;
      case ProofOfBankAccountDetails.DEPOSIT_SLIP:
        return DocumentType.DepositSlip;
      default:
        return DocumentType.Unknown;
    }
  }
  return DocumentType.Unknown;
};
