import React, { useEffect, useRef, useState } from 'react';
import {
  Dimensions,
  LayoutChangeEvent,
  ScrollView,
  TouchableOpacity,
  TouchableOpacityProps,
} from 'react-native';
import { useTheme } from '@emotion/react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTranslation } from 'react-i18next';
import { useNavigation, NavigationProp } from '@react-navigation/native';
import {
  Button,
  Box,
  Chip,
  Column,
  H7,
  Row,
  Typography,
} from 'cube-ui-components';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import { MemoPerfChartsInSnapCarousel } from 'features/performance/components/PerfChartsInSnapCarousel';
import { MemoizedPerformanceBSC } from 'features/performance/tablet/PerformanceBSC';
import { MemorizedPerformanceMTDdetails } from 'features/performance/tablet/PerformanceMTDdetails';
import { MemoPerformanceYTDdetails } from 'features/performance/tablet/PerformanceYTDdetails';
import RecognitionPreview from '../components/RecognitionPreview';
import CollapsibleCard from 'features/policy/components/PolicyDetails/CollapsibleCard';
import CrownSVG from 'features/performance/assets/CrownSVG';
import { ColoredDotIndicator } from 'features/recognition/components/RecognitionDetailsAchievement';
import performanceLabelDataHandler from 'features/performance/utlils/performanceLabelDataHandler';
import useParsedPerformanceData from 'features/performance/hooks/useParsedPerformanceData';
import {
  useGetAllPerformance,
  useGetTeamIndividualMDRT,
  useGetTeamIndividualPerformance,
} from 'hooks/useGetPerformance';
import { useGetPerformanceTarget } from 'hooks/usePerformanceTarget';
import useBoundStore from 'hooks/useBoundStore';
import { numberToThousandsFormat } from 'utils/helper/numberToThousandsFormat';
import { countryModuleSellerConfig } from 'utils/config/module';
import { build, country } from 'utils/context';
import { RootStackParamList } from 'types';
import { MDRTTiers } from 'types/performance';
import { PerfTabsConfigKeys, perfTabsConfig } from '../config';
import Animated, {
  FadeIn,
  FadeOut,
  LinearTransition,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { usePerviewTeamBasedMdrt } from 'features/recognition/hooks/useMDRTTierCalculations';
import { useCheckAgentIsMDRT } from 'hooks/useCheckAgentIsMDRT';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';

const tabTypeArr = ['bsc', 'sales', 'recognition'] as const;
type TabType = (typeof tabTypeArr)[number];

const tabToTitle = {
  bsc: 'tab.bsc.title',
  recognition: 'tab.recognition.title',
  sales: 'tab.sales.title',
} as const;

export default function PerfCommonScreenPhone() {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation(['performance', 'common']);
  const borderPosition = useSharedValue(0);

  const windowWidth = Dimensions.get('window').width;
  const windowHeight = Dimensions.get('window').height;

  const { navigate } = useNavigation<NavigationProp<RootStackParamList>>();
  const scrollViewRef = useRef<ScrollView>(null);

  const [tabOnFocus, setTabOnFocus] = useState<TabType>('bsc');
  const [isScrollingByTouchable, setIsScrollingByTouchable] =
    useState<boolean>(false);
  const [sectionOffsets, setSectionOffsets] = useState<
    Record<TabType, number | undefined>
  >({
    bsc: undefined,
    recognition: undefined,
    sales: undefined,
  });
  const [curTab, setCurTab] = useState<PerfTabsConfigKeys>(
    perfTabsConfig[0].key,
  );

  const [isScrollTriggeredByButtons, setIsScrollTriggeredByButtons] =
    useState(false);

  const animatedBarWidth = windowWidth / (tabTypeArr?.length ?? 1);

  useEffect(() => {
    const index = tabTypeArr.indexOf(tabOnFocus);
    borderPosition.value = withTiming(index * animatedBarWidth, {
      duration: 200,
    });
  }, [tabOnFocus]);

  const animatedBorderStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: borderPosition.value }],
    };
  });
  // Function to handle dynamic layout changes and store Y-offsets
  const handleLayout = ({
    tab,
    event,
  }: {
    tab: TabType;
    event: LayoutChangeEvent;
  }) => {
    const { y } = event.nativeEvent.layout;
    const paddingTop = space[4];
    setSectionOffsets(prevOffsets => ({
      ...prevOffsets,
      [tab]: y - paddingTop,
    }));
  };

  const { bottom } = useSafeAreaInsets();
  const bottomBarHeightInZus = useBoundStore(
    state => state.bottomBarHeightInZus,
  );

  const { data: teamIndividualData } = useGetTeamIndividualPerformance();

  const { data } = useParsedPerformanceData();

  const minScrollViewSafeBottom = bottom + bottomBarHeightInZus;
  const activeColor = colors.palette.fwdAlternativeOrange[100];
  const paddingForScrollLastItemOnTop =
    (tabTypeArr?.length > 2 ? 0.5 : 0.3) * windowHeight;

  const { isLoading: isAllPerformanceLoading, data: allPerfData } =
    useGetAllPerformance();
  const { isLoading: isPerformanceTargetLoading } = useGetPerformanceTarget();
  const { isLoading: isTeamMDRTLoading, data: teamMdrtDataObj } =
    useGetTeamIndividualMDRT({
      enabled: country === 'ib',
    });
  const { data: isMDRT } = useCheckAgentIsMDRT();
  const isMdrtAvailble = isMDRT != null;
  const teamMdrtData = teamMdrtDataObj?.mdrtRanking?.find(item => {
    const currentYear = new Date().getFullYear();
    return item.year === String(currentYear);
  });
  const { isLoading, data: agentProfile } = useGetAgentProfile();

  const agentFirstName = agentProfile?.person?.firstName ?? '';
  return (
    <Column style={{ backgroundColor: colors.background, flex: 1 }}>
      <ScreenHeader showBottomSeparator={false} route={'Performance'} />
      <TopTabsToScroll
        scrollViewRef={scrollViewRef}
        setTabOnFocus={setTabOnFocus}
        sectionOffsets={sectionOffsets}
        tabOnFocus={tabOnFocus}
        animatedBarWidth={animatedBarWidth}
        animatedBorderStyle={animatedBorderStyle}
        activeColor={activeColor}
        onPressExtra={() => {
          setIsScrollingByTouchable(true);
        }}
      />

      <ScrollView
        ref={scrollViewRef}
        onMomentumScrollEnd={() => {
          setIsScrollingByTouchable(false);
        }}
        onScroll={e => {
          if (isScrollingByTouchable) {
            return;
          }

          const tabKeys = tabTypeArr;
          if (tabKeys?.length <= 1) {
            return;
          }
          const currentY = e?.nativeEvent?.contentOffset?.y ?? 0;
          const adjustFactor = -80;
          const secondItem = sectionOffsets[tabKeys[1]] ?? 0;
          const lastItem = sectionOffsets[tabKeys[tabKeys.length - 1]] ?? 0;
          if (secondItem != currentY && currentY < secondItem + adjustFactor) {
            setTabOnFocus(tabKeys[0]);
            return;
          }
          if (currentY >= lastItem + adjustFactor) {
            setTabOnFocus(tabKeys[tabKeys.length - 1]);
            return;
          }
          tabKeys.forEach((tab, idx) => {
            const nextTab = tabKeys[idx + 1];
            const currentTab = sectionOffsets[tab] ?? 0;
            const nextTabOffset = sectionOffsets[nextTab] ?? 0;
            if (
              currentY < nextTabOffset + adjustFactor &&
              currentY >= currentTab + adjustFactor
            ) {
              setTabOnFocus(tab);
            }
          });
        }}
        style={{
          backgroundColor: colors.palette.fwdGrey[50],
        }}
        contentContainerStyle={{
          paddingBottom:
            minScrollViewSafeBottom + paddingForScrollLastItemOnTop,
          padding: space[4],
          gap: space[4],
          backgroundColor: colors.palette.fwdGrey[50],
        }}>
        {/* Performance Title, and setting target */}
        <Row alignItems="center" justifyContent="space-between">
          <Typography.H6 fontWeight="bold">
            {t('performance:performance.yourPerformance')}
          </Typography.H6>
          {/* {countryModuleSellerConfig.performance?.tipYourPerformance && (
          <WalkThroughTooltip
            isVisible={showTip}
            contentStyle={{
              maxWidth: 513,
              height: 'auto',
              paddingVertical: space[5],
              paddingLeft: space[5],
              paddingRight: space[2],
              borderRadius: borderRadius['large'],
              left: 178,
            }}
            content={
              <TipYourPerformance
                numProposals={25}
                rm={600000}
                type={'ACE'}
                handleCloseTip={() => {
                  setShowTip(false);
                  if (isFirstTimeTip) {
                    updateTipYourPerformance();
                  }
                }}
                handleViewProposals={() => {
                  setShowTip(false);
                  navigate('Proposals');
                }}
              />
            }>
            <TouchableOpacity
              style={{ paddingLeft: space[1] }}
              onPress={() => {
                setShowTip(true);
              }}>
              <Icon.InfoCircle width={24} height={24} />
            </TouchableOpacity>
          </WalkThroughTooltip>
        )} */}

          {countryModuleSellerConfig.performance?.TargetSetting && (
            <Button
              size="medium"
              text={t('performance:performance.editTarget')}
              variant="secondary"
              onPress={() => navigate('PerformanceTarget')}
            />
          )}
        </Row>

        {/* -------- Card 1 -------- */}
        <Box
          onLayout={event => {
            handleLayout({ tab: 'bsc', event });
          }}>
          {countryModuleSellerConfig.performance?.isPerformanceBSCShown && (
            <MemoizedPerformanceBSC data={teamIndividualData} mode={'phone'} />
          )}
        </Box>

        {/* -------- Card 2 -------- */}
        <Animated.View
          style={{
            backgroundColor: colors.background,
            borderRadius: borderRadius['large'],
          }}
          onLayout={event => {
            handleLayout({ tab: 'sales', event });
          }}>
          <Typography.H6
            fontWeight="bold"
            style={{
              paddingTop: space[4],
              paddingHorizontal: space[3],
            }}>
            {t('performance:section.title.salesPerformance')}
          </Typography.H6>
          <Row justifyContent="space-between" p={space[4]} gap={space[3]}>
            {perfTabsConfig.map((tab, idx) => (
              <Chip
                size="medium"
                style={{ flex: 1 }}
                key={tab.key}
                focus={curTab === tab.key}
                label={t(`performance:${tab.label}`) ?? '--'}
                onPress={() => {
                  setCurTab(tab.key);
                  setIsScrollTriggeredByButtons(true);
                }}
              />
            ))}
          </Row>
          <MemoPerfChartsInSnapCarousel
            data={data}
            isDetailsButtonShown={false}
            isChevronShown={false}
            widthOffset={28}
            curTab={curTab}
            setCurTab={setCurTab}
            isScrollTriggeredByButtons={isScrollTriggeredByButtons}
            setIsScrollTriggeredByButtons={setIsScrollTriggeredByButtons}
            chartNumberInsideLabel={t('performance:performance.metric.issued', {
              metric: t(
                `performance:performance.metric.${
                  countryModuleSellerConfig?.performance?.metricTwo || 'Case'
                }`,
              ),
            })}
            chartNumberOutsideLabel={t(
              'performance:performance.metric.issued',
              {
                metric: t(
                  `performance:performance.metric.${
                    countryModuleSellerConfig?.performance?.metricOne || 'ACE'
                  }`,
                ),
              },
            )}
          />
          <Row
            style={{
              paddingHorizontal: space[4],
              paddingBottom: space[4],
              paddingTop: space[5],
              justifyContent: 'center',
            }}>
            {/* // *********** Sales APE */}
            <Animated.View
              style={{ alignItems: 'center', flex: 1, gap: space[1] }}>
              {performanceLabelDataHandler(curTab, data).salesAPE
                .isAchieved && (
                <Animated.View
                  layout={LinearTransition}
                  exiting={FadeOut}
                  entering={FadeIn}>
                  <CrownSVG fill={colors.palette.fwdYellow[100]} />
                </Animated.View>
              )}
              <Animated.View
                layout={LinearTransition}
                exiting={FadeOut}
                entering={FadeIn}
                style={{ alignItems: 'center' }}>
                <Typography.H6 fontWeight="bold">
                  {numberToThousandsFormat(
                    performanceLabelDataHandler(curTab, data).salesAPE.value,
                  )}
                </Typography.H6>
                <Row style={{ alignItems: 'center', gap: space[1] }}>
                  <ColoredDotIndicator fill={colors.primary} />
                  <Typography.H8>
                    {t(
                      `performance:performance.metric.${
                        countryModuleSellerConfig?.performance?.metricOne ||
                        'APE'
                      }`,
                    ) + ` (${t('common:currencySymbol')})`}
                  </Typography.H8>
                </Row>
              </Animated.View>
            </Animated.View>
            {/* // *********** Case Count */}
            <Animated.View
              style={{
                alignItems: 'center',
                flex: 1,
                gap: space[1],
              }}>
              {performanceLabelDataHandler(curTab, data).caseCount
                .isAchieved && (
                <Animated.View
                  layout={LinearTransition}
                  exiting={FadeOut}
                  entering={FadeIn}>
                  <CrownSVG fill={colors.palette.fwdYellow[100]} />
                </Animated.View>
              )}

              <Animated.View
                style={{ alignItems: 'center' }}
                layout={LinearTransition}
                exiting={FadeOut}
                entering={FadeIn}>
                <Typography.H6 fontWeight="bold">
                  {numberToThousandsFormat(
                    performanceLabelDataHandler(curTab, data).caseCount.value,
                  )}
                </Typography.H6>
                <Row style={{ alignItems: 'center', gap: space[1] }}>
                  <ColoredDotIndicator fill={colors.primaryVariant2} />
                  <Typography.H8>
                    {/* {t('performance:performance.caseCount')} */}
                    {t(
                      `performance:performance.metric.${
                        countryModuleSellerConfig?.performance?.metricTwo ||
                        'Case'
                      }`,
                    )}
                  </Typography.H8>
                </Row>
              </Animated.View>
            </Animated.View>
          </Row>
        </Animated.View>
        {/* -------- Card 3 -------- */}
        <PerformanceDetailsCard />

        {/* -------- Card 4 -------- */}
        {countryModuleSellerConfig.performance?.isRecognitionShown && (
          <Box
            onLayout={event => {
              handleLayout({ tab: 'recognition', event });
            }}>
            <RecognitionPreview
              agentName={agentFirstName}
              // isLoading={
              //   country == 'ib' ? isTeamMDRTLoading : isAllPerformanceLoading
              // }
              data={
                country == 'ib'
                  ? usePerviewTeamBasedMdrt(teamMdrtData)
                  : {
                      currentTier:
                        allPerfData?.mdrt?.nextTier === 'MDRT'
                          ? 'FWDElite'
                          : allPerfData?.mdrt?.nextTier === 'COT'
                          ? 'MDRT'
                          : allPerfData?.mdrt?.nextTier === 'TOT'
                          ? 'COT'
                          : 'TOT',
                      nextTier: allPerfData?.mdrt?.nextTier as MDRTTiers,
                      percent:
                        allPerfData?.mdrt?.nextTier === 'MDRT'
                          ? 100 -
                            (allPerfData?.mdrt?.shortfallToMDRTPercent ?? 0)
                          : allPerfData?.mdrt?.nextTier === 'COT'
                          ? 100 -
                            (allPerfData?.mdrt?.shortfallToCOTPercent ?? 0)
                          : allPerfData?.mdrt?.nextTier === 'TOT'
                          ? 100 -
                            (allPerfData?.mdrt?.shortfallToTOTPercent ?? 0)
                          : 0,
                    }
              }
            />
          </Box>
        )}
      </ScrollView>
    </Column>
  );
}

function TopTabsToScroll({
  scrollViewRef,
  setTabOnFocus,
  sectionOffsets,
  tabOnFocus,
  animatedBarWidth,
  animatedBorderStyle,
  activeColor,
  onPressExtra,
}: {
  scrollViewRef: React.RefObject<ScrollView>;
  setTabOnFocus: React.Dispatch<React.SetStateAction<TabType>>;
  sectionOffsets: Record<TabType, number | undefined>;
  tabOnFocus: TabType;
  animatedBarWidth: number;
  animatedBorderStyle: {
    transform: {
      translateX: number;
    }[];
  };
  activeColor: string;
  onPressExtra: TouchableOpacityProps['onPress'];
}) {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation(['performance', 'common']);
  const borderHeight = 1;
  return (
    <Row
      borderBottom={borderHeight}
      borderBottomColor={colors.palette.fwdGrey[100]}>
      {tabTypeArr.map(tab => (
        <TouchableOpacity
          key={tab}
          style={{
            flex: 1,
            minHeight: space[11],
            justifyContent: 'center',
            alignItems: 'center',
          }}
          onPress={e => {
            onPressExtra && onPressExtra(e);
            scrollViewRef.current?.scrollTo({
              y: sectionOffsets[tab],
              animated: true,
            });
            setTabOnFocus(tab);
          }}>
          <H7
            fontWeight={tab === tabOnFocus ? 'bold' : 'normal'}
            color={tab === tabOnFocus ? activeColor : undefined}>
            {t(`performance:${tabToTitle[tab]}`)}
          </H7>
        </TouchableOpacity>
      ))}
      <Animated.View
        style={[
          {
            position: 'absolute',
            bottom: borderHeight > 0 ? -borderHeight : 0,
            height: 3,
            width: animatedBarWidth,
            backgroundColor: activeColor,
          },
          animatedBorderStyle,
        ]}
      />
    </Row>
  );
}

function PerformanceDetailsCard() {
  const { colors, space, borderRadius } = useTheme();
  const { t } = useTranslation(['performance', 'common']);

  const [curTab, setCurTab] = useState<PerfTabsConfigKeys>(
    perfTabsConfig[0].key,
  );

  const { data } = useParsedPerformanceData();

  return (
    <CollapsibleCard
      style={{
        paddingVertical: space[4],
        paddingHorizontal: space[3],
      }}
      containerStyle={{
        gap: space[5],
      }}
      title={t('performance:section.title.salesPerformanceDetails')}
      TitleIcon={() => <></>}>
      <Row justifyContent="space-between" gap={space[3]}>
        {perfTabsConfig.map((tab, idx) => (
          <Chip
            size="medium"
            style={{ flex: 1 }}
            key={tab.key}
            focus={curTab === tab.key}
            label={t(`performance:${tab.label}`) + ' details'}
            onPress={() => {
              setCurTab(tab.key);
            }}
          />
        ))}
      </Row>
      {curTab == 'MTD' && <MemorizedPerformanceMTDdetails mode="phone" data={data}/>}
      {curTab == 'YTD' && <MemoPerformanceYTDdetails mode="phone" data={data} />}
    </CollapsibleCard>
  );
}
