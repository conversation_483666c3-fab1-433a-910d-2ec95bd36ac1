import { useGetListStartClaim, useStartClaim } from 'hooks/useGetPolicyList';
import React from 'react';
import { ExpressClaimPolicy } from 'types/policy';
import { addToast } from 'cube-ui-components';
import { Linking } from 'react-native';
import { startClaimUrl } from 'utils/context';

export const statusClaim = {
  ['ISSUED']: 'Inforce',
};
export const useClaim = () => {
  const { data: dataClaim, isLoading } = useGetListStartClaim();
  const { mutateAsync: getStartClaims, isLoading: isLoadingStartClaim } =
    useStartClaim();
  const [policyNumberSelected, setPolicyNumberSelected] = React.useState('')
  const [keyword, setKeywordValue] = React.useState('');
  const dataFilter = React.useMemo(() => {
    return dataClaim?.filter(item => {
      if (keyword) {
        const lowercasedKeyword = keyword.toLowerCase();
        return (
          item?.policyOwner.toLowerCase().includes(lowercasedKeyword) ||
          item?.policyNumber.toLowerCase().includes(lowercasedKeyword)
        );
      }
      return null;
    });
  }, [dataClaim, keyword]);
  const onSelectItemData = (policyNumber: string) => {
    const itemSelect = dataClaim?.find(
      item => item.policyNumber === policyNumber,
    );
    setPolicyNumberSelected(itemSelect?.policyNumber ?? '')
  };
  const setKeyword = (text: string) => {
    setKeywordValue(text);
    if (!text || keyword !== text) {
      setPolicyNumberSelected('');
    }
  };
  const onNext = () => {
    getStartClaims(policyNumberSelected, {
      onSuccess: data => {
        Linking.openURL(
          `${startClaimUrl}/loss-of-life/express/?claimId=${data?.claimId}`,
        );
      },
      onError: e => {
        addToast([
          {
            message: e?.message,
          },
        ]);
      },
    });
  };
  return {
    data: dataFilter,
    isLoading,
    keyword,
    setKeyword,
    onSelectItemData,
    onNext,
    isLoadingStartClaim,
    policyNumberSelected
  };
};
