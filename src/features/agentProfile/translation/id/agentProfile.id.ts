export default {
  //General
  'agentProfile.tablet.tabs.profile': 'Profile',
  'agentProfile.yourAchievement': 'Your achievement',
  'agentProfile.tablet.tabs.setting': 'Setting',
  'agentProfile.tablet.tabs.termsAndConditions': 'Terms and conditions',
  'agentProfile.tablet.tabs.logout': 'Logout',

  // Achievement sections
  'agentProfile.achievementCollection.title': 'Achievement collection',
  'agentProfile.achievementCollection.emptyCase':
    'You haven’t got any achievement collection yet',
  'agentProfile.eliteAgentAchievement.title': 'Elite Agent achievement',
  'agentProfile.eliteAgentAchievement.emptyCase':
    'You haven’t got any Elite Agent achievement yet',
  'agentProfile.mdrtAchievement.title': 'MDRT achievement',
  'agentProfile.mdrtAchievement.emptyCase':
    'You haven’t got any MDRT achievement yet',

  // Personal details
  'agentProfile.personalDetails.profileImage.title': 'Upload profile image by',
  'agentProfile.personalDetails.profileImage.camera': 'Camera',
  'agentProfile.personalDetails.profileImage.photoLibrary': 'Photo library',
  'agentProfile.personalDetails.agentCode': 'Agent code',
  'agentProfile.personalDetails.name': 'Name',
  'agentProfile.personalDetails.email': 'Email',
  'agentProfile.personalDetails.code': 'Code',
  'agentProfile.personalDetails.mobileNumber': 'Mobile number',
  'agentProfile.personalDetails.address': 'Address (optional)',

  // License
  'activeLicense.title': 'Active License',
  'activeLicense.licenseType': 'License type',
  'activeLicense.period': 'Period',
  'activeLicense.period.content': '{{start}} until {{end}}',
  'activeLicense.licenseCode': 'License code',
  'activeLicense.noRecords': 'No records.',

  // Setting
  'agentProfile.setting.general': 'General',
  'agentProfile.setting.language': 'Language',
  'agentProfile.setting.language.english': 'English',
  'agentProfile.setting.security': 'Security',
  'agentProfile.setting.biometricIdLogin': 'Biometric login',
  'agentProfile.setting.biometricIdLogin.reminderText':
    'By disabling this feature, you will need to setup biometric login again.',
  'agentProfile.setting.pushNotification': 'Push notifications',
  'agentProfile.setting.logout': 'Logout',
  'agentProfile.setting.versionNumber.title': 'App version',
  'agentProfile.setting.versionNumber': 'Version number ',
  'agentProfile.setting.versionNumberFormat': '{{env}}v{{appVersion}}',
  'agentProfile.setting.deniedMsg':
    'Denied! Please enable on your devices setting',

  // Buttons
  'agentProfile.showAll': 'Show all',
  'agentProfile.showLess': 'Show less',

  // Agent name card
  'agentProfile.agentNameCard.share.success': 'Shared successfully.',

  // Photo Editing
  takePhoto: 'Take Photo with Camera',
  selectFromAlbum: 'Select from Album',
  deletePhoto: 'Delete Profile Image',
  cancel: 'Cancel',
  done: 'Done',
  usePhoto: 'Use photo',
  edit: 'Edit',
  goBack: 'Back',
  yes: 'Yes',
  no: 'No',
  'saveChanges.modal.title': 'Save changes?',
  'saveChanges.modal.content': 'Are you sure to save the changes?',
  'saveChanges.success': 'Saved successfully',
  'saveChanges.fail': 'Please try again later',
  'delete.success': 'Delete successfully',
  'delete.fail': 'Please try again later',
  'deletePhoto.modal.title': 'Delete Profile Image',
  'deletePhoto.modal.content': 'Are you sure to delete the profile Image?',
};
