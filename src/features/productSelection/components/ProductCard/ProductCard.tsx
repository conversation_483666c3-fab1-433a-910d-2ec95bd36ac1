import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Box, Button, Icon, Label, Row, Typography } from 'cube-ui-components';
import { useGuruFAB } from 'features/productSelection/hooks/useGuruFAB';
import { usePressInOutCardStyle } from 'features/productSelection/hooks/usePressInOutCardStyle';
import { useProductImageQuery } from 'features/productSelection/hooks/useProducts';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { StyleProp, View, ViewStyle } from 'react-native';
import { ScrollView } from 'react-native-gesture-handler';
import Animated from 'react-native-reanimated';
import { AppEvent } from 'types/event';
import { Product } from 'types/products';
import { EventRegister } from 'utils/helper/eventRegister';
import GATracking from 'utils/helper/gaTracking';
import { renderLabelByLanguage } from 'utils/helper/translation';
import GuruFAB, { GuruFABProps } from '../GuruFAB';
import { GuruSuggestionGroup, GuruSuggestionItem } from '../GuruSuggestion';
import ProductBrochureDetails from '../ProductBrochureDetails/ProductBrochureDetails';

export const PRODUCT_CARD_WIDTH_TABLET = 257;

type CardContainerProps = { product: Product; isTabletMode: boolean };
const CardContainer = styled(Animated.View)<CardContainerProps>(
  ({
    theme: { colors, borderRadius, sizes, space },
    product,
    isTabletMode,
  }) => {
    const shouldUseGuruFAB = useGuruFAB();
    let cardContainerHeight = isTabletMode ? 312 : 304;
    if (!product.productDesc) {
      cardContainerHeight = isTabletMode ? 236 : 198;
    }

    const cardContainerStyle: ViewStyle = {
      backgroundColor: colors.background,
      borderRadius: borderRadius.medium,
      height: shouldUseGuruFAB ? 'auto' : cardContainerHeight,
    };

    return {
      ...cardContainerStyle,
      ...(isTabletMode
        ? {
            width: PRODUCT_CARD_WIDTH_TABLET,
            padding: space[4],
            gap: space[4],
          }
        : { width: sizes[40], padding: space[2], gap: space[2] }),
    };
  },
);

const ProductImage = styled.Image<{ isTabletMode: boolean }>(
  ({ theme: { borderRadius, space }, isTabletMode }) => ({
    width: '100%',
    height: isTabletMode ? 77 : 52,
    borderRadius: borderRadius.small,
    marginBottom: space[2],
  }),
);

export type ProductCardProps = Product &
  GuruFABProps & {
    style?: StyleProp<ViewStyle>;
    // action listener
    onPress: (p: Product) => void;
    isInRecommendation?: boolean;
  };

const ProductCard = ({
  style,
  popUpDirection,
  onPress,
  ...product
}: ProductCardProps) => {
  const { space, colors, components } = useTheme();

  const { t } = useTranslation(['common', 'product', 'proposal', 'aiBot']);

  const { isTabletMode } = useLayoutAdoptionCheck();

  const [contentHeight, setContentHeight] = useState(0);

  const { data: imageUrl } = useProductImageQuery(product.productThumbnailUrl);

  const { onPressIn, onPressOut, cardStyle } = usePressInOutCardStyle(
    colors.background,
    colors.primaryVariant3,
  );

  const handleProductPressed = () => {
    GATracking.logCustomEvent(GATracking.EVENTS.SELECT_PRODUCT, {
      product_name: renderLabelByLanguage(product.productName) ?? '',
      action_type: GATracking.ACTION_TYPES.PRODUCT_REC_SELECT_PRODUCT,
    });

    onPress(product);
  };

  const shouldUseGuruFAB = useGuruFAB();
  const Title = isTabletMode ? Typography.H6 : Typography.H7;
  const titleColor = shouldUseGuruFAB ? colors.palette.black : colors.primary;
  const Description = isTabletMode ? Typography.Label : Typography.SmallBody;

  return (
    <>
      <CardContainer
        product={product}
        style={[cardStyle, style]}
        isTabletMode={isTabletMode}>
        <ProductImage
          source={{ uri: imageUrl }}
          resizeMode="cover"
          isTabletMode={isTabletMode}
        />

        <ScrollView
          scrollEnabled={
            isTabletMode ? contentHeight > space[33] : contentHeight > space[48]
          }
          showsVerticalScrollIndicator={false}>
          <View onLayout={e => setContentHeight(e.nativeEvent.layout.height)}>
            <Box
              gap={space[2]}
              minH={isTabletMode ? 50 : 0}
              justifyContent={'center'}>
              {!!product?.productGroup && (
                <Label fontWeight="medium">
                  {Array.isArray(product?.productGroup)
                    ? renderLabelByLanguage(product?.productGroup?.[0])
                    : renderLabelByLanguage(product.productGroup)}
                </Label>
              )}

              <Title fontWeight="bold" color={titleColor}>
                {renderLabelByLanguage(product.productName)}
              </Title>
            </Box>

            {!shouldUseGuruFAB && (
              <Box mt={space[1]}>
                {!!product.productDesc && (
                  <Description
                    color={colors.palette.fwdDarkGreen[100]}
                    ellipsizeMode="tail">
                    {renderLabelByLanguage(product.productDesc)}
                  </Description>
                )}
              </Box>
            )}
          </View>
        </ScrollView>

        <Row alignItems="center" justifyContent="space-between">
          <ProductBrochureDetails
            brochurePdf={product.brochurePdf}
            productName={product.productName}
            riplayPdf={product?.riplayPdf}>
            <Label fontWeight="bold" color={colors.primary}>
              {t('common:details')}
            </Label>
          </ProductBrochureDetails>

          <Button
            variant="secondary"
            icon={<Icon.ArrowRight size={18} />}
            text={t('common:select')}
            rounded
            contentStyle={{
              paddingHorizontal: space[2],
              paddingVertical: space[1],
            }}
            textStyle={{
              fontSize: components.button.typography.smaller.size,
              lineHeight: components.button.typography.smaller.lineHeight,
              marginLeft: -space[1],
              marginRight: -space[2],
            }}
            onPress={handleProductPressed}
            onPressIn={onPressIn}
            onPressOut={onPressOut}
          />
        </Row>
      </CardContainer>
      {shouldUseGuruFAB && (
        <GuruFAB popUpDirection={popUpDirection}>
          <GuruSuggestionGroup>
            <GuruSuggestionItem
              label={t('aiBot:howToPitch')}
              onPress={() => {
                EventRegister.emit<AppEvent.ProductSelectionPitch>(
                  AppEvent.ProductSelectionPitch,
                  {
                    pid: product.pid,
                  },
                );
              }}
            />
            <GuruSuggestionItem
              label={t('aiBot:productSummary')}
              onPress={() => {
                EventRegister.emit<AppEvent.ProductSelectionSummary>(
                  AppEvent.ProductSelectionSummary,
                  {
                    pid: product.pid,
                  },
                );
              }}
            />
          </GuruSuggestionGroup>
        </GuruFAB>
      )}
    </>
  );
};

export default React.memo(ProductCard);
