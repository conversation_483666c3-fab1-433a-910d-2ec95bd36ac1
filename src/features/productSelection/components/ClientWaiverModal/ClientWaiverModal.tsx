import React, { memo, useState } from 'react';
import { Box } from 'cube-ui-components';
import { country } from 'utils/context';
import IBClientWaiverModal from './ib/IBClientWaiverModal';
import MYClientWaiverModal from './my/MYClientWaiverModal';
import PHClientWaiverModal from './ph/PHClientWaiverModal';
import { FnaRejectRecommendedReason } from 'types/case';

export interface ClientWaiverModalProps {
  handleClose: () => void;
  reason?: FnaRejectRecommendedReason;
  onGetFnaDoc: () => Promise<void>;
  onConfirm: () => Promise<void>;
  isGettingFnaDoc: boolean;
  isSaving: boolean;
  waiverChecked: boolean;
  onToggleWaiverChecked: (value: boolean) => void;
  providedServiceGuideChecked: boolean;
  onToggleServiceGuideChecked: (value: boolean) => void;
  isViewedDoc: boolean;
}

export interface Props {
  handleClose: () => void;
  reason: FnaRejectRecommendedReason;
  onGetFnaDoc: (acknowledge: boolean) => Promise<void>;
  onConfirm: (acknowledge: boolean) => Promise<void>;
  isGettingFnaDoc: boolean;
  isSaving: boolean;
  isViewedDoc: boolean;
}

export default memo(function ClientWaiverModal({
  handleClose,
  reason,
  onGetFnaDoc: onGetFnaDocAction,
  onConfirm: onConfirmAction,
  isGettingFnaDoc,
  isSaving,
  isViewedDoc
}: Props) {
  const [waiverChecked, setWaiverChecked] = useState(false);
  const [providedServiceGuideChecked, setProvidedServiceGuideChecked] =
    useState(false);
  let acknowledge = waiverChecked;
  if (country === 'ib') {
    acknowledge &&= providedServiceGuideChecked;
  }

  return (
    <Box>
      {ClientWaiver && (
        <ClientWaiver
          handleClose={handleClose}
          reason={reason}
          waiverChecked={waiverChecked}
          onToggleWaiverChecked={setWaiverChecked}
          providedServiceGuideChecked={providedServiceGuideChecked}
          onToggleServiceGuideChecked={setProvidedServiceGuideChecked}
          onGetFnaDoc={() => onGetFnaDocAction(acknowledge)}
          onConfirm={() => onConfirmAction(acknowledge)}
          isGettingFnaDoc={isGettingFnaDoc}
          isSaving={isSaving}
          isViewedDoc={isViewedDoc}
        />
      )}
    </Box>
  );
});

const ClientWaiver = {
  ib: IBClientWaiverModal,
  ph: PHClientWaiverModal,
  id: null,
  my: MYClientWaiverModal,
}[country];
