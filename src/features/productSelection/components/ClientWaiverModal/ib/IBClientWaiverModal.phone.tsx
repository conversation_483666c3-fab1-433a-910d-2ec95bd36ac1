import React, { memo, useCallback, useMemo, useState } from 'react';
import { useTheme } from '@emotion/react';
import {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollView,
  BottomSheetFooterProps,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import { Portal } from '@gorhom/portal';
import {
  Box,
  Checkbox,
  CheckboxProps,
  H6,
  LargeBody,
  Row,
} from 'cube-ui-components';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { ClientWaiverModalProps } from '../ClientWaiverModal';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import { useFnaSnapPoints } from 'features/fna/hooks/useFnaSnapPoint';
import ClientWaiverFooter from './ClientWaiverFooter.phone';
import { useSafeAreaFrame } from 'react-native-safe-area-context';
import { SharedValue, useAnimatedStyle } from 'react-native-reanimated';

export default memo(function IBClientWaiverModalPhone({
  handleClose,
  waiverChecked,
  onToggleWaiverChecked,
  providedServiceGuideChecked,
  onToggleServiceGuideChecked,
  onGetFnaDoc: onGetFnaDocAction,
  onConfirm: onConfirmAction,
  isGettingFnaDoc,
  isSaving,
  isViewedDoc,
}: ClientWaiverModalProps) {
  const { space, colors } = useTheme();
  const { t } = useTranslation(['product']);
  const bottomSheetProps = useBottomSheet();
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const [seeMore, setSeeMore] = useState(isViewedDoc ? false : true);
  const { height } = useSafeAreaFrame();
  const initialSnapPoints = useMemo(() => ['CONTENT_HEIGHT'], []);
  const fullHeightSnapPoints = useFnaSnapPoints();
  const {
    animatedHandleHeight,
    animatedSnapPoints,
    animatedContentHeight,
    handleContentLayout,
  } = useBottomSheetDynamicSnapPoints(initialSnapPoints);

  const onConfirm = useCallback(async () => {
    await onConfirmAction();
    bottomSheetProps.bottomSheetRef.current?.close();
  }, [bottomSheetProps.bottomSheetRef, onConfirmAction]);
  const onGetFnaDoc = useCallback(async () => {
    await onGetFnaDocAction();
  }, [onGetFnaDocAction]);

  const handleCloseBottomSheet = useCallback(async () => {
    bottomSheetProps.bottomSheetRef.current?.close();
  }, [bottomSheetProps.bottomSheetRef]);

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => {
      return (
        <ClientWaiverFooter
          {...props}
          onGetFnaDoc={onGetFnaDoc}
          handleClose={handleCloseBottomSheet}
          onConfirm={onConfirm}
          waiverChecked={waiverChecked}
          onToggleWaiverChecked={onToggleWaiverChecked}
          providedServiceGuideChecked={providedServiceGuideChecked}
          onToggleServiceGuideChecked={onToggleServiceGuideChecked}
          disabled={!waiverChecked || !providedServiceGuideChecked}
          isGettingFnaDoc={isGettingFnaDoc}
          isSaving={isSaving}
          isViewedDoc={isViewedDoc}
        />
      );
    },
    [
      onConfirm,
      waiverChecked,
      onToggleWaiverChecked,
      providedServiceGuideChecked,
      onToggleServiceGuideChecked,
      isGettingFnaDoc,
      isSaving,
      onGetFnaDoc,
      handleCloseBottomSheet,
      isViewedDoc,
    ],
  );

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        appearsOnIndex={0}
        disappearsOnIndex={-1}
      />
    ),
    [],
  );

  const disclaimer = useMemo(() => {
    return seeMore
      ? t('product:disclaimer.1')
      : t('product:disclaimer.1.short');
  }, [t, seeMore]);

  return (
    <BottomSheetModal
      {...bottomSheetProps}
      onDismiss={handleClose}
      style={{ padding: 0 }}
      backdropComponent={renderBackdrop}
      snapPoints={
        seeMore ? fullHeightSnapPoints : animatedSnapPoints as SharedValue<(string | number)[]>
      }
      handleHeight={animatedHandleHeight}
      contentHeight={animatedContentHeight}
      handleStyle={{ display: 'flex' }}
      footerComponent={renderFooter}>
      <Row
        paddingX={space[isNarrowScreen ? 3 : 4]}
        paddingY={space[4]}
        justifyContent="space-between">
        <Row alignItems="center" gap={space[3]} flex={1}>
          <H6 fontWeight="bold">{t('product:disclaimer.title')}</H6>
        </Row>
      </Row>
      <BottomSheetScrollView
        onLayout={e => {
          if (!seeMore) {
            e.nativeEvent.layout.height += 57;
            handleContentLayout(e);
          }
        }}
        keyboardDismissMode="on-drag"
        style={useAnimatedStyle(() => ({
          paddingHorizontal: space[isNarrowScreen ? 3 : 4],
          marginBottom: space[4],
          maxHeight: height - space[22] - 57 - animatedHandleHeight.value,
        }))}>
        <LargeBody>
          {disclaimer}
          {isViewedDoc && (
            <LargeBody
              suppressHighlighting
              onPress={() => {
                if (seeMore) {
                  setSeeMore(false);
                } else {
                  setSeeMore(true);
                }
              }}
              fontWeight="bold"
              color={colors.primary}>
              {seeMore
                ? ` ${t('product:disclaimer.close')}`
                : t('product:disclaimer.more')}
            </LargeBody>
          )}
        </LargeBody>
        <Box mb={space[4]}>
          {isViewedDoc && (
            <Box gap={space[4]} mt={space[4]}>
              <WaiverCheckBox
                label={t('product:disclaimer.accept')}
                value={waiverChecked}
                onChange={onToggleWaiverChecked}
                labelStyle={{
                  flex: 1,
                  marginTop: -space[1],
                }}
                checked={waiverChecked}
              />
              <WaiverCheckBox
                label={t('product:disclaimer.accept2')}
                value={providedServiceGuideChecked}
                onChange={onToggleServiceGuideChecked}
                labelStyle={{
                  flex: 1,
                }}
                checked={providedServiceGuideChecked}
              />
            </Box>
          )}
        </Box>
        <BottomSheetFooterSpace />
      </BottomSheetScrollView>
    </BottomSheetModal>
  );
});

const WaiverCheckBox = styled(Checkbox)<CheckboxProps & { checked: boolean }>(
  ({ theme, checked }) => ({
    borderWidth: 1,
    alignItems: 'flex-start',
    borderRadius: theme.space[2],
    padding: theme.space[4],
    backgroundColor: checked
      ? theme.colors.primaryVariant3
      : theme.colors.background,
    borderColor: checked
      ? theme.colors.primary
      : theme.colors.palette.fwdGrey[100],
  }),
);
