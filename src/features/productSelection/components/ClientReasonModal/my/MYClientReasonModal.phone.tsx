import { useTheme } from '@emotion/react';
import { Box, H6, Radio<PERSON>utt<PERSON>, Row, TextField } from 'cube-ui-components';
import {
  Fragment,
  useCallback,
  useMemo,
  useReducer,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import {
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetFooterProps,
  BottomSheetModal,
  BottomSheetModalProvider,
  BottomSheetScrollViewMethods,
  BottomSheetView,
  useBottomSheetDynamicSnapPoints,
} from '@gorhom/bottom-sheet';
import { Portal } from '@gorhom/portal';
import { useBottomSheet } from 'features/eApp/hooks/useBottomSheet';
import { FnaRejectRecommendedReason } from 'types/case';
import { ClientReasonModalProps } from '../ClientReasonModal';
import { reasons } from './MYClientReasonModal';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import BottomSheetFooterSpace from 'components/BottomSheetFooterSpace';
import ClientReasonFooter from './ClientReasonFooter.phone';
import { SharedValue } from 'react-native-reanimated';
import {
  useSafeAreaFrame,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import BottomSheetKeyboardAwareScrollView from 'components/BottomSheetKeyboardAwareScrollView';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import styled from '@emotion/native';
import { useCustomReason } from 'features/productSelection/hooks/useCustomReason';
import { KeyboardAwareScrollViewProps } from 'react-native-keyboard-controller';

export default function MYClientReasonModalPhone({
  reason: defaultReason,
  setCustomReason: setCustomReasonProp,
  handleClose,
  onConfirm,
  visible,
}: ClientReasonModalProps) {
  const bottomSheetProps = useBottomSheet();
  const { t } = useTranslation(['product']);
  const { space, colors, borderRadius, typography } = useTheme();
  const { height } = useSafeAreaFrame();
  const scrollViewRef = useRef<BottomSheetScrollViewMethods>(null);

  const initialSnapPoints = useMemo(
    () => [height - space[22]],
    [height, space],
  );

  const { isNarrowScreen } = useWindowAdaptationHelpers();

  const { setCustomReason, error, reason, setReason, disabled } =
    useCustomReason(defaultReason, setCustomReasonProp);

  const renderBackdrop = useCallback(
    (props: BottomSheetBackdropProps) => (
      <BottomSheetBackdrop
        {...props}
        appearsOnIndex={0}
        disappearsOnIndex={-1}
      />
    ),
    [],
  );

  const renderFooter = useCallback(
    (props: BottomSheetFooterProps) => (
      <ClientReasonFooter
        {...props}
        reason={reason}
        handleClose={handleClose}
        onConfirm={onConfirm}
        disabled={disabled}
      />
    ),
    [reason, handleClose, onConfirm, disabled],
  );

  if (!visible) return null;

  return (
    <Portal>
      <BottomSheetModalProvider>
        <BottomSheetModal
          {...bottomSheetProps}
          onDismiss={handleClose}
          style={{ padding: 0 }}
          backdropComponent={renderBackdrop}
          snapPoints={initialSnapPoints}
          handleStyle={{ display: 'flex' }}
          backgroundComponent={undefined}
          footerComponent={renderFooter}>
          <Box paddingX={space[isNarrowScreen ? 3 : 4]} paddingY={space[4]}>
            <H6 fontWeight="bold">{t('product:reason.title')}</H6>
          </Box>
          <BottomSheetKeyboardAwareScrollView
            ref={scrollViewRef}
            bottomOffset={space[8]}>
            <Box p={space[4]} pt={0}>
              {reasons.map(r => {
                const checked = reason === r;
                const isOtherReasons =
                  r === FnaRejectRecommendedReason.OTHER_REASONS;
                return (
                  <Fragment key={r}>
                    <Box
                      mb={space[3]}
                      px={space[3]}
                      py={space[4]}
                      borderWidth={1}
                      borderColor={
                        checked ? colors.primary : colors.palette.fwdGrey[100]
                      }
                      borderRadius={borderRadius.small}
                      backgroundColor={
                        checked ? colors.primaryVariant3 : colors.background
                      }>
                      <RadioButton
                        key={r}
                        value={`${r}`}
                        selected={reason === r}
                        label={t(`product:reason.${r}`)}
                        onSelect={() => {
                          setReason(r);
                          if (r === FnaRejectRecommendedReason.OTHER_REASONS) {
                            // scrollViewRef.current?.scrollToEnd();
                          }
                        }}
                        labelStyle={{
                          marginLeft: space[2],
                          fontSize: typography['largeBody'].size,
                          lineHeight: typography['largeBody'].lineHeight,
                          flex: 1,
                        }}
                      />
                    </Box>
                    {isOtherReasons && checked && (
                      <OtherReasonInput
                        autoFocus
                        placeholder={t('product:reason.G.placeholder')}
                        onChangeText={setCustomReason}
                        error={error}
                      />
                    )}
                  </Fragment>
                );
              })}
            </Box>
            <BottomSheetFooterSpace />
          </BottomSheetKeyboardAwareScrollView>
        </BottomSheetModal>
      </BottomSheetModalProvider>
    </Portal>
  );
}

const OtherReasonInput = styled(TextField)(() => ({}));
