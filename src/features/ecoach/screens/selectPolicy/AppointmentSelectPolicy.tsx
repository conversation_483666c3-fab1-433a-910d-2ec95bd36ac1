import React, { useCallback, useMemo } from 'react';
import {
  BackHandler,
  ImageBackground,
  ScrollView,
  TouchableOpacity,
  View,
} from 'react-native';
import {
  NavigationProp,
  RouteProp,
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { cafeBGMobile, cafeBGTablet } from 'features/ecoach/assets';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { AppointmentProductConfig, EcoachParamList, Personas } from 'types';
import { useTranslation } from 'react-i18next';
import { Button, H3, H5, H7, Icon, SmallLabel } from 'cube-ui-components';
import Spacer from 'features/lead/my/LeadProfile/components/Spacer';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import useBoundStore from 'hooks/useBoundStore';
import { country } from 'utils/context';
import { DifficultType } from 'features/ecoach/screens/SelectDifficulty';
import { StatusBar } from 'expo-status-bar';

const Container = styled(View)<{ isTabletMode: boolean }>(
  ({ isTabletMode }) => ({
    flexGrow: 1,
    paddingHorizontal: isTabletMode ? '8%' : sizes[4],
    backgroundColor: isTabletMode
      ? 'rgba(1, 1, 1, 0.75)'
      : 'rgba(1, 1, 1, 0.80)',
    justifyContent: 'center',
  }),
);

const PageBGImg = styled(ImageBackground)(() => ({
  flex: 1,
}));

const ProductContainer = styled(View)<{ isTabletMode: boolean }>(
  ({ isTabletMode }) => ({
    gap: sizes[4],
    backgroundColor: isTabletMode ? 'rgba(0, 0, 0, 0.30)' : 'transparent',
    borderRadius: isTabletMode ? sizes[4] : 0,
    width: isTabletMode ? '48%' : '100%',
    padding: isTabletMode ? sizes[4] : 0,
  }),
);

const ProductLayout = styled(View)<{ isTabletMode: boolean }>(
  ({ isTabletMode }) => ({
    height: isTabletMode ? 400 : '60%',
    minHeight: isTabletMode ? 400 : 438,
  }),
);

const ProductContent = styled(View)({
  flexDirection: 'row',
  width: '100%',
  gap: sizes[4],
});

const NextBtn = styled(Button)({
  width: 180,
});

const SelectAITitle = styled(H5)({
  maxWidth: 343,
});

const SelectAITitleTablet = styled(H3)({
  maxWidth: 400,
});

const HorizontalView = styled(View)({
  flexDirection: 'row',
  gap: sizes[1],
});
const TitleView = styled(View)({
  flexDirection: 'row',
  gap: sizes[1],
  width: '90%',
});

const GoBackBtn = styled(TouchableOpacity)({
  paddingHorizontal: sizes[4],
  position: 'absolute',
  top: sizes[10],
  zIndex: 1,
});

const ProductCardContainer = styled(TouchableOpacity)<{
  isTabletMode: boolean;
  isSelected: boolean;
  elevation: any;
}>(({ isTabletMode, isSelected, elevation }) => ({
  flex: 1,
  backgroundColor: 'transparent',
  borderRadius: sizes[3],
  borderWidth: 1,
  borderColor: isSelected ? colors.fwdOrange[100] : 'rgba(255, 255, 255, 0.50)',
  shadowColor: colors.black,
  paddingHorizontal: sizes[3],
  paddingVertical: isTabletMode ? sizes[15] : sizes[8],
  ...elevation[4],
}));

interface ProductCardProps {
  product: AppointmentProductConfig;
  isSelected: boolean;
  onSelect: (productCode: string) => void;
}

type PersonaInfoProps = {
  personas: Personas;
};

interface GroupedProducts {
  [key: string]: Array<AppointmentProductConfig>;
}

const PersonaInfo = ({ personas }: PersonaInfoProps) => {
  const { persona_age, persona_gender, persona_name } = personas;
  return (
    <HorizontalView>
      <SmallLabel fontWeight={'bold'} color={colors.fwdOrange[100]}>
        {persona_name} |
      </SmallLabel>
      <SmallLabel fontWeight={'bold'} color={colors.fwdOrange[100]}>
        {persona_gender} |
      </SmallLabel>
      <SmallLabel fontWeight={'bold'} color={colors.fwdOrange[100]}>
        {persona_age}
      </SmallLabel>
    </HorizontalView>
  );
};

const ProductCard: React.FC<ProductCardProps> = ({
  product,
  isSelected,
  onSelect,
}) => {
  const { elevation } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();

  const {
    product_name,
    product_code,
    product_description,
    clickable,
    personas,
  } = product;

  return (
    <ProductCardContainer
      isTabletMode={isTabletMode}
      isSelected={isSelected}
      elevation={elevation}
      disabled={!clickable}
      onPress={() => clickable && onSelect?.(product_code)}>
      <StatusBar hidden />
      <TitleView>
        <Icon.Account fill={colors.fwdOrange[100]} size={sizes[5]} />
        <H7
          fontWeight={'bold'}
          color={isSelected ? colors.fwdOrange[100] : colors.white}>
          {product_name}
        </H7>
      </TitleView>
      {isTabletMode && (
        <>
          <Spacer height={sizes[1]} />
          <PersonaInfo personas={personas} />
        </>
      )}
      <Spacer height={sizes[isTabletMode ? 4 : 2]} />
      <SmallLabel fontWeight={'normal'} color={colors.white}>
        {product_description}
      </SmallLabel>

      {!isTabletMode && (
        <>
          <Spacer height={sizes[2]} />
          <PersonaInfo personas={personas} />
        </>
      )}
    </ProductCardContainer>
  );
};

const AppointmentSelectPolicy = () => {
  const navigation = useNavigation<NavigationProp<EcoachParamList>>();
  const { params } = useRoute<RouteProp<EcoachParamList, 'SelectPolicy'>>();
  const [selectedProductCode, setSelectedProductCode] = React.useState<
    string | null
  >(null);
  const { t } = useTranslation('ecoach');
  const { isTabletMode } = useLayoutAdoptionCheck();
  const appointmentProductConfig = useBoundStore(
    state => state.ecoach.appointmentProductConfig,
  );

  const availableProducts = useMemo(() => {
    const productForCurrentCountry = appointmentProductConfig?.[country];

    if (productForCurrentCountry) {
      return [...productForCurrentCountry].sort((a, b) => a.order - b.order);
    }
    return [];
  }, [appointmentProductConfig, country]);

  // Group transactions by product_group
  const groupedProducts = availableProducts.reduce(
    (groups: GroupedProducts, product) => {
      const key = product.product_group;
      if (!groups[key]) {
        groups[key] = [];
      }
      groups[key].push(product);
      return groups;
    },
    {},
  );

  const handleNext = useCallback(() => {
    selectedProductCode &&
      navigation.navigate('GuideLinesPage', {
        productFlowType: params.productFlowType,
        productSelectionCode: selectedProductCode,
        difficultType: DifficultType.Beginner,
      });
  }, [selectedProductCode, params.productFlowType, navigation]);

  const goHome = useCallback(() => {
    navigation.navigate('EcoachHome');
  }, [navigation]);

  const goBack = useCallback(() => {
    goHome();
  }, [goHome]);

  useFocusEffect(
    React.useCallback(() => {
      const onBackPress = () => {
        goHome();
        return true;
      };
      const subscription = BackHandler.addEventListener(
        'hardwareBackPress',
        onBackPress,
      );

      return () => subscription.remove();
    }, [goHome]),
  );

  return (
    <PageBGImg
      source={isTabletMode ? cafeBGTablet : cafeBGMobile}
      resizeMode={'cover'}>
      <GoBackBtn onPress={goBack}>
        <Icon.ArrowLeft size={sizes[6]} fill={colors.white} />
      </GoBackBtn>
      <Container isTabletMode={isTabletMode}>
        {isTabletMode ? (
          <SelectAITitleTablet fontWeight={'bold'} color={colors.white}>
            {t('selectAIPersona')}
          </SelectAITitleTablet>
        ) : (
          <SelectAITitle fontWeight={'bold'} color={colors.white}>
            {t('selectAIPersona')}
          </SelectAITitle>
        )}

        {!isTabletMode && <Spacer height={sizes[6]} />}
        <ProductLayout isTabletMode={isTabletMode}>
          <ScrollView
            horizontal={isTabletMode}
            contentContainerStyle={{
              flex: 1,
              gap: sizes[7],
              alignItems: 'center',
            }}>
            {Object.keys(groupedProducts).map(product_group => (
              <ProductContainer isTabletMode={isTabletMode}>
                <H7 fontWeight={'bold'} color={colors.white}>
                  {product_group}
                </H7>
                {isTabletMode && (
                  <SmallLabel color={colors.white}>
                    {
                      groupedProducts[product_group][0]
                        .product_group_description
                    }
                  </SmallLabel>
                )}
                <ProductContent>
                  {groupedProducts[product_group].map(product => (
                    <ProductCard
                      key={product.product_code}
                      product={product}
                      isSelected={selectedProductCode === product.product_code}
                      onSelect={setSelectedProductCode}
                    />
                  ))}
                </ProductContent>
              </ProductContainer>
            ))}
          </ScrollView>
        </ProductLayout>
        {!isTabletMode && <Spacer height={sizes[6]} />}
        <NextBtn
          disabled={!selectedProductCode}
          variant={'primary'}
          onPress={handleNext}
          text={t('next')}
        />
      </Container>
    </PageBGImg>
  );
};

export default AppointmentSelectPolicy;
