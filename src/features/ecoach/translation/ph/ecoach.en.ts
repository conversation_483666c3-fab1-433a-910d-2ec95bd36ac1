export default {
  reqPermission: 'We need your permission to show the camera',
  grandPermission: 'Grant permission',
  youFail: 'You Fail',
  ooHeart: 'You’ve run out of hearts!',
  exitSS: 'Exit Session',
  reTake: "Let's retake",
  leaving: ' is leaving!',
  minuteLeft: `You have 1 minute left!`,
  secondsLeft: `You have 30 seconds left!`,
  wrapUp: `Let's wrap up the conversation`,
  points: ' points',
  saleCoPilot: 'Sales Co-Pilot',
  trainMaterials: 'Training Materials',
  yourSS: 'Your Sessions',
  selectProduct: 'Select a FWD product:',
  selectPolicy: 'Scenario',
  selectDifficulty: 'Difficulty',
  practiceText:
    'Practice how to excel in sales talks, through virtual scenarios that will test and improve your skills!',
  letGo: "Let's go!",
  home: 'Home',
  yourSummary: 'Your Summary',
  hereIsSummary: 'Here is the summary of the',
  ss: 'session',
  retrySS: 'Retry Session',
  genFullReport: 'Generate Full Report',
  hello: 'Hello,',
  getToKnowBetter: 'Let’s get to know you better!',
  getStarted: "Let's Get Started!",
  yearsOfExperience: 'Years of experience as an agent',
  experienceLevelText: 'Experience Level',
  whatWouldYouLikeToLearn: 'What would you like to learn?',
  threeMonths: '0-3 months',
  twelveMonths: '3-12 months',
  twoYears: '1-2 years',
  threePlusYears: '3+ years',
  newToFWD: "I'm new to FWD!",
  trainee: 'Trainee',
  agent: 'Agent',
  communicationSkills: 'Communication skills',
  analyticalSkills: 'Analytical Skills',
  customerRelationship: 'Customer Relationship',
  salesSkills: 'Sales Skills',
  improvePerformance: 'Improve Performance',
  wellDone: 'Well Done!',
  congratsText: 'Congratulations on completing the',
  howDoUThink: 'How did you think you did?',
  confidenceQuestion:
    'How much did this practice help you with product knowledge?',
  helpSellQuestion:
    'How much did this practice help you build confidence to meet real clients?',
  letsSeeResult: "Let's see my result!",
  isThinking: 'is thinking ...',
  next: 'Next',
  cusProfile: 'Your customer profile',
  lifeSetDes: 'Sell this plan to a customer with income protection needs',
  healthSetDes: 'Sell this plan to a customer with health protection needs',
  beginnerDes: 'Are you new or need a refresher on the basics? Start here!',
  expertDes: 'Experienced enough? Try this level to up your game!',
  introText:
    'Your Virtual Customer is getting ready, In the meantime, let’s get to know her!',
  basic: 'Basic',
  language: 'Languages',
  moreAbout: 'More about',
  wasIntroduce: 'was introduced to you through',
  veryLittle: 'Very Little',
  alot: 'A lot',
  phQuote: `“Filipinos they don’t have a budget problem, they have a priority problem.”`,
  quoteAuthor: `- Agent’s name, city`,
  helloIm: `Hello I'm`,
  tryARole: `Try a role play with me!`,
  letTestYourSale: `Let’s test your sales in selling `,
  setForLifeText: `Set for Life`,
  prepareReport: `Preparing Report`,
  yourReport: `Your Report`,
  yourReports: `Your Reports`,
  ssID: 'Session',
  dateAndTime: 'Date and Time',
  difficulty: 'Difficulty',
  duration: 'Duration',
  beginner: 'Beginner',
  expert: 'Expert',
  yourSkillSetDetail: 'Your Skillset Details',
  tryANewSS: 'Try a new session',
  ohNo: 'Oh no!',
  goToHome: 'Go to home',
  goToMyReport: 'Go to My Report',
  backToHome: 'Back to home',
  youHaveNotComplete: 'You have not completed the session',
  congrats: 'Congrats!',
  ssEnd: 'You have reached 60 hearts! Now the session ends !',
  entryNew: 'NEW',
  rolePlayTraningGuru: 'Role play training guru',
  testYourSalesSkills: 'Test your sales skills',
  letsGo: "Let's Go!",
  shapenSalesSkills: 'Sharpen sales skills with AI role-play',
  seeMoreDetails: 'See more details',
  howYouCanDoBetter: 'See how you can do better',
  overallScore: 'OVERALL SCORE',
  totalScore: 'TOTAL SCORE',
  relationshipBuilding: 'Relationship building',
  customerDiscovery: 'Customer discovery',
  productKnowledge: 'Product knowledge',
  objectionsAndClosing: 'Objections & Closing',
  speechAndAnalysis: 'Speech & Analysis',
  minutes: 'min',
  seconds: 'sec',
  thingsYouDidWell: 'Things you did well',
  thingsYouDidnotDoWell: "Things you didn't do well",
  improvements: 'Improvements',
  trySayingThisNextTime: 'Try saying this next time',
  missComplete: 'Mission completed',
  sessionComplete: 'Session completed',
  youCollected100: 'You have collected enough hearts and achieved your goal',
  youCollected100QF:
    "Great Job,\n you've earned {{score}} hearts. \n\n Try our roleplay mode and sell a product in 8 minutes.",
  timeOut: 'Time out',
  youHaveRanOut: "You have ran out of time. Remember you've only got 8 minutes",
  tryAgain: 'Try Again',
  letTryAgain: `Let's Try Again!`,
  missFail: 'Mission Ended',
  exitRole: 'Exit role playing',
  turnOnCamera: 'Can you turn on your camera?',
  toTalkWithRico:
    'To talk with Rico, we will need your permission to show the camera',
  yesIAmOkayUsingCamera: 'Yes, I am okay using camera',
  avatarNotReadyNotification:
    'Avatar is not ready, please come back and try again later!',
  tryAgainToSeeMore: 'Try again to see some feedback for this category',
  setUpNextAppointment:
    'Set up a next appointment with your virtual customer under',
  fifteenMinute: '8 minutes',
  pasSSPer: 'Past sessions performance',
  viewAllSS: 'View all sessions',
  yourPastSS: 'Your Past sessions',
  viewHistory: 'View history',
  turnOn: 'Turn on',
  goToSettings: 'Go to settings',
  microphonePermission: 'Allow {{productName}} to access your microphone',
  turnOnMicrophone: 'Can you turn on your microphone?',
  toTalkWithVirtualCustomer:
    'To talk with your virtual customer,\nplease allow access to your microphone',
  yesLetsProcess: 'Yes, let’s proceed!',
  howUsefulDidYouFindTheReport: 'How useful did you find the report?',
  howWasYourExperience: 'How was your experience?',
  howWasTheSS: 'How was the session?',
  yourReportIsReady: 'Your report is ready!',
  viewMyReport: 'View my report',
  yourMission: 'Your Mission',
  launchMission: 'Launch mission',
  // New translation keys for AppointmentSummary
  thingsToImprove: 'Things to improve',
  thingsWeNailed: 'Things we nailed',
  greatJob: 'Great job, {{name}}!',
  myReport: 'My report',
  seeFullTranscript: 'See full transcript',
  suggestedNextMove: 'Suggested next move',

  'error.connection': 'Sorry, something went wrong, please try again.',

  'feedback.overall.title': 'Feedback',

  'feedback.options.inaccurate': 'Inaccurate',
  'feedback.options.nothelpful': 'Not helpful',
  'feedback.options.unhelpful': 'Unhelpful',
  'feedback.options.slow': 'Slow',
  'feedback.options.other': 'Other',

  'feedback.screen.ratingTitle': 'How was the session?',
  'feedback.screen.commentTitle': 'Tell us more',
  'feedback.screen.comment': 'Comments',
  'feedback.screen.submit': 'Submit',
  'feedback.screen.feedbackTitle1': 'What did you dislike',
  'feedback.screen.feedbackTitle3': 'What can we improve',

  'feedback.screen.thankyouTitle': 'Thank you!',
  'feedback.screen.thankyouText':
    'You have successfully submitted your feedback.',

  'feedback.form.title': 'Why do you dislike the answer?',
  'feedback.form.submit': 'Submit',

  'modal.feedback.title': 'Why you dislike the message',
  'modal.feedback.comments': 'Comments',
  'modal.feedback.confirm': 'Confirm',

  trainingGuruIsUnderMaintenance: 'Training Guru is under maintenance',
  pleaseComeBackInAFewMinutes: 'Please come back in a few minutes',

  youHaveEarnedOutOfHearts:
    'You have earned {{heart}} out of {{total}} hearts!',
  youDidNotEarnAnyHearts:
    "You didn't earn any hearts today, but you can always try again. You can do it!",
  tryFullSessionExperience: 'Try Full Session experience',
  letsTryTheSalesRoleplay: 'Try Roleplay',
  letsTryAgain: 'Try again',
  training: 'Training',
  guru: 'Guru',
  quickfire: 'Product knowledge',
  salesRolePlay: 'Roleplay mode',
  exitQuickfire: 'Exit',
  fullExp: 'Full Experience',
  fullExpReport: 'Full Experience Report',
  rolePlayModeReport: 'Roleplay Mode Report',
  letSee: 'Answer as many product questions as you can in  ',
  enterASimulation: 'Enter a simulation and try selling a product in  ',
  quickfireTime: '2 minutes',
  twoMinutes: '2 minutes',
  twoMin: '2 min',
  ftMin: '8 min',
  quickFireHiText: 'Hi, {{name}}!',
  quickFireHiSubText: 'Get ready to sell',
  watchVideo: 'Watch a sample role play video',
  quickFireDesText:
    'Sharpen your sales skills and convince your AI customer to buy an FWD product',
  leavingSoSoon: 'Leaving so soon?',

  isSadToSeeYouLeave: '{{avatarName}} is sad to see you leave :(',

  letsTryToAnswer: 'Let’s try to answer',
  questionAboutTheProduct: '{{avatarName}}’s question about the product!',

  hereYourGoal: 'Here’s your goal:',
  hereYourGoalQF: '5 min | Quickfire mode',
  hereYourGoalPK: '5 min | Product knowledge',
  convinceAvatarToProduct: 'Convince {{avatarName}} to buy {{productName}}',
  convinceAvatarToProductQF:
    'Answer as many questions as you can to earn points!',
  rolePlayMode: 'Roleplay mode',
  productKnowledgeMode: 'Product knowledge',
  rolePlayTime: '8 minutes',
  eightMinutes: '8 minutes',
  productKnowledgeTime: '2 minutes',
  guideIntroText:
    'You’re an agent meeting {{avatarName}}, a new lead. {{agentPronounce}} a young professional referred by a friend. Take a deep breath and get ready!',
  ibBancaIntroText:
    'You’re an advisor approaching {{avatarName}}, a new lead. Your goal to understand his financial needs and effectively convince him to buy {{productName}}. Take a deep breath and get ready!',
  startChat: 'Start chat',
  startTheCall: 'Start the call',
  smallTalkAndBuildRapport: 'Small talk and build rapport',
  understandPersonalNeeds: 'Understand personal needs',
  presentProductOptions: 'Present product options',
  addressConcernsAndCloseTheDeal: 'Address concerns and close the deal',
  appointmentSetting: 'Appointment setting',
  objectionHandling: 'Objection handling',
  faceToFaceMeeting: 'Face-to-face meeting',
  appointmentSettingDesc:
    'Practice calling a lead to secure an appointment for a meeting',
  productKnowledgeDesc:
    'Answer as many product questions as you can before meeting a customer',
  faceToFaceMeetingDesc:
    'Practice needs analysis and product recommendations at a sales meeting',
  fiveMin: '5 min',
  fiveMinutes: '5 minutes',
  eightMin: '8 min',
  selectAIPersona: 'Select an AI-persona to practice with!',
  appointmentCallWith: 'Setting appointment call with {{personaName}}',
  appointmentIntroduce: 'Introduce',
  appointmentBuildRapport: 'Build rapport and state the call purpose',
  appointmentInterest: 'Stir interest',
  appointmentAskQuestion: 'Ask questions to engage them',
  appointmentSetTimeDate: 'Set time & date',
  appointmentInsertATrial: 'Insert a trial close in your first call',
  appointmentConfirmMeetUp: 'Confirm meet-up',
  appointmentGiveAssurance: 'Give assurance & express gratitude',
  chatAgain: `Chat again`,
  exitAppointment: `Exit`,
  timeUp: `Time's up !`,
  goalAchieve: `Goal achieved!`,
  youHaveSecured: `You have secured an appointment with the customer`,
  yourInternetSpeed:
    'Your internet speed is too slow so we change it into audio call',
  lowBandwidthTitle: 'Internet connection is slow',
  lowBandwidthDescription:
    'We’ll switched to audio-only mode for a smoother experience.',
  lowBandwidthOk: 'Got it !',
  noInternetTitle: 'Oops! No internet connection found',
  noInternetDescription: 'Please check your network and try again',
  noInternetRetry: 'Retry',
  youHaveRanOutTime:
    'You have run out of time. Check your report to see how  you can do better next time!',
  noHistoryYet: 'No reports yet. Start a session to kick things off!',
};
