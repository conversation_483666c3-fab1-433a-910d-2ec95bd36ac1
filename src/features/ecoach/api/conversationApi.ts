import { ecoachClient } from 'features/ecoach/api/ecoachClient';
import useBoundStore from 'hooks/useBoundStore';

export type SkillDetail = {
  reason: string;
  improvement: string;
  assessment: string;
  excerpt: string;
  atom_summary: string;
  try_next_time: string;
};

type SkillSetDetails = {
  applied_product_knowledge: { [key: string]: SkillDetail }[];
  customer_relationship: { [key: string]: SkillDetail }[];
  objection_handling_closing: { [key: string]: SkillDetail }[];
  communication_skills: { [key: string]: SkillDetail }[];
  customer_discovery: { [key: string]: SkillDetail }[];
};

export type Report =
  | {
      overall_score: string;
      applied_product_knowledge_score: string;
      customer_discovery_score: string;
      customer_relationship_score: string;
      objection_handling_closing_score: string;
      communication_skills_score: string;
      well_done_comment: string;
      improvement_comment: string;
      skill_set_details: SkillSetDetails;
    }
  | Record<string, never>;

export type ConversationData = {
  report_is_ready: string;
  conversation_id: string;
  created_at: string;
  datetime: string;
  difficulty: string;
  duration: number;
  product_selection?: string;
  session_number?: number;
  report: Report;
  score?: number;
  conversation_scenario?: string;
  conversation_type: string;
};

export type ConversationHistoryResponse = {
  items: ConversationData[];
  last_evaluated_key: string;
  order: 'asc' | 'desc';
  agent_id: string;
  has_more: boolean;
  total_count: number;
};

export enum ConversationType {
  CUSTOMER = 'customer',
  PRODUCT_TRAINER = 'product_trainer',
  QUIZ = 'quiz',
  QUICKFIRE = 'quickfire',
  HK_EAIC = 'hk-eaic',
  UNKNOWN = 'unknown',
  APPOINTMENT_SETTING = 'appointment-setting',
  OBJECTION_HANDLING = 'objection-handling',
}

export type ConversationFeedBackType = 'ROLE_PLAY' | 'REPORT' | 'OVERALL';

export type ConversationFeedbackPayload = {
  conversation_id?: string;
  star: number;
  feedback_type: ConversationFeedBackType;
  comments: string[];
};

export type MaintenanceStatus = {
  start_datetime: string;
  end_datetime: string;
};

export const getConversationData = async (
  conversationId: string,
): Promise<ConversationData> => {
  const state = useBoundStore.getState();
  const token = state.auth.authInfo?.accessToken;

  // console.log('getConversationData token', token);
  // console.log('getConversationData conversationId', conversationId);
  const response = await ecoachClient.get<ConversationData>(
    `/conversation/report/${conversationId}`,
    { headers: { access_token: token } },
  );
  // console.log('getConversationData response', JSON.stringify(response.data));
  return response.data;
};

export const getConversationHistory = async () => {
  const state = useBoundStore.getState();
  const token = state.auth.authInfo?.accessToken;

  // console.log('getConversationHistory token', token);
  const response = await ecoachClient.get<ConversationData[]>(
    `/conversation/report_history`,
    { headers: { access_token: token } },
  );

  // console.log('getConversationHistory response', JSON.stringify(response.data));
  return response.data.filter(
    conversation => conversation.report_is_ready === 'true',
  );
};

export const getConversationHistoryByPaging = async (
  limit = 100,
  conversationType = ConversationType.CUSTOMER,
  pageParam = '',
) => {
  // console.log('getConversationHistory token', pageParam, token);
  const response = await ecoachClient.get<ConversationHistoryResponse>(
    `/conversation/report_history_v2?limit=${limit}&conversation_type=${conversationType}${
      pageParam ? `&last_evaluated_key=${pageParam || ''}` : ''
    }`,
  );

  // console.log('getConversationHistory response', response.data, pageParam);
  return response.data;
};

export const submitConversationFeedback = async (
  data: ConversationFeedbackPayload,
) => {
  const state = useBoundStore.getState();
  const token = state.auth.authInfo?.accessToken;
  const postData: ConversationFeedbackPayload = {
    star: data.star,
    feedback_type: data.feedback_type,
    comments: data.comments,
    conversation_id: data.conversation_id,
  };

  let response;
  const url = `/conversation/feedback`;

  // console.log('submitConversationFeedback postData', url, token, postData);
  try {
    response = await ecoachClient.postDirectPayLoad<
      ConversationFeedbackPayload,
      any
    >(url, postData, {
      headers: { access_token: token },
    });
  } catch (error) {
    console.log('error', error);
  }

  return response?.data;
};

export const checkMaintenanceStatus = async (): Promise<MaintenanceStatus> => {
  const state = useBoundStore.getState();
  const token = state.auth.authInfo?.accessToken;

  // console.log('checkMaintenanceStatus token', token);
  const response = await ecoachClient.get<MaintenanceStatus>(
    `/maintenance/list`,
    {
      headers: { access_token: token },
    },
  );

  return response.data;
};

export const checkAvatarAvailability = async (): Promise<boolean> => {
  const state = useBoundStore.getState();
  const token = state.auth.authInfo?.accessToken;
  try {
    const response = await ecoachClient.get<{ voice_call_available: boolean }>(
      `/conversation/available`,
      {
        headers: { access_token: token },
      },
    );
    // console.log(
    //   'checkAvatarAvailability response.data?.voice_call_available',
    //   response.data?.voice_call_available,
    // );

    return response.data?.voice_call_available;
  } catch (e) {
    // console.log('checkAvatarAvailability error', e);
    return false;
  }
};
