import { ecoachClient } from 'features/ecoach/api/ecoachClient';
import useBoundStore from 'hooks/useBoundStore';

export type Config = {
  update_at: string;
  value: any;
  created_at: string;
  key: string;
  uid: string;
};

export type ConfigurationData = {
  config: Config[];
};

export const getConfigurationData = async (): Promise<Config[]> => {
  const state = useBoundStore.getState();
  const token = state.auth.authInfo?.accessToken;
  // console.log('getConfigurationData token', token);
  const response = await ecoachClient.get<Config[]>(`/configuration/list`, {
    headers: { access_token: token },
  });
  // console.log('getConfigurationData response', JSON.stringify(response.data));
  return response.data;
};
