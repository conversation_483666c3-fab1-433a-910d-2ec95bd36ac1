import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { ActivityIndicator, FlatList, ScrollView, View } from 'react-native';
import styled from '@emotion/native';
import { useTranslation } from 'react-i18next';
import { Box, H6, H7, H8, Label } from 'cube-ui-components';
import Spacer from 'features/lead/my/LeadProfile/components/Spacer';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useTheme } from '@emotion/react';
import { ConversationType } from 'features/ecoach/api/conversationApi';
import { useReportHistory } from 'features/ecoach/hooks/useReportHistory';
import SessionQuickFireCard from 'features/ecoach/components/cards/SessionQuickFireCard';
import LoadingIndicator from 'components/LoadingIndicator';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { EcoachParamList, ModuleAvailability } from 'types';
import useBoundStore from 'hooks/useBoundStore';
import { country } from 'utils/context';
import { CHANNELS } from 'types/channel';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';

const TabContainer = styled.View(() => ({
  width: '100%',
  height: sizes[11],
}));

const TabScrollView = styled.ScrollView(() => ({
  width: '100%',
  height: '100%',
}));

const LoadingView = styled.View<{
  isTabletMode?: boolean;
}>(({ isTabletMode }) => ({
  minHeight: isTabletMode ? 132 : 234,
  justifyContent: 'center',
  alignItems: 'center',
}));
const TabButton = styled.TouchableOpacity<{
  isActive: boolean;
  isTabletMode?: boolean;
}>(({ isActive, isTabletMode }) => ({
  height: '100%',
  paddingHorizontal: sizes[1],
  justifyContent: 'center',
  alignItems: 'center',
  flex: isTabletMode ? 1 : 0,
  borderBottomWidth: isActive ? 3 : 0,
  borderBottomColor: isActive ? colors.fwdOrange[100] : 'transparent',
}));

const ContentContainer = styled.View(() => ({
  width: '100%',
  flex: 1,
  marginTop: sizes[8],
}));

const ViewAllSSButton = styled.TouchableOpacity(() => ({
  width: '100%',
  alignItems: 'center',
  marginTop: sizes[4],
}));

interface HistoryCallTabsProps {
  isHomePage: boolean;
  defaultActiveTabIndex?: number;
}

type TabName = keyof ModuleAvailability;

const HistoryCallTabs: React.FC<HistoryCallTabsProps> = ({
  isHomePage,
  defaultActiveTabIndex = 0,
}) => {
  const { t } = useTranslation('ecoach');
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { space } = useTheme();
  const [activeTabIndex, setActiveTabIndex] = useState(defaultActiveTabIndex);
  const [isFetching, setIsFetching] = useState(false);
  const navigation =
    useNavigation<NativeStackNavigationProp<EcoachParamList>>();
  const channel = useGetCubeChannel();
  const isIBBanca = country === 'ib' && channel === CHANNELS.BANCA;
  // Access moduleAvailability from store
  const moduleAvailability = useBoundStore(
    state => state.ecoach.moduleAvailability,
  );

  // Filter tabs based on moduleAvailability
  const availableTabs = useMemo(() => {
    const HISTORY_CALL_TABS: { name: TabName }[] = [];
    if (isIBBanca || !moduleAvailability) {
      return [];
    }

    // Define the desired order of tabs
    const tabOrder: TabName[] = [
      'face_to_face_meeting',
      'appointment_setting',
      'product_knowledge',
      'objection_handling'
    ];

    // Add tabs in the specified order if they are available
    for (const tabName of tabOrder) {
      if (moduleAvailability[tabName] === true) {
        HISTORY_CALL_TABS.push({ name: tabName });
      }
    }

    // console.log('availableTabs', HISTORY_CALL_TABS);
    return HISTORY_CALL_TABS;
  }, [isIBBanca, moduleAvailability]);

  // Reference to the ScrollView for programmatic scrolling
  const scrollViewRef = useRef<ScrollView>(null);

  // Map activeTabIndex to conversation type based on available tabs
  const getConversationType = useCallback(() => {
    const currentTab = availableTabs[activeTabIndex];
    switch (currentTab?.name) {
      case 'face_to_face_meeting':
        return ConversationType.CUSTOMER;
      case 'appointment_setting':
        return ConversationType.APPOINTMENT_SETTING;
      case 'product_knowledge':
        return ConversationType.QUICKFIRE;
      case 'objection_handling':
        return ConversationType.OBJECTION_HANDLING;
      default:
        return ConversationType.CUSTOMER;
    }
  }, [availableTabs, activeTabIndex]);

  const getTabName = useCallback(
    (tab: { name: TabName }) => {
      switch (tab.name) {
        case 'face_to_face_meeting':
          return t('faceToFaceMeeting');
        case 'appointment_setting':
          return t('appointmentSetting');
        case 'product_knowledge':
          return t('productKnowledge');
        case 'objection_handling':
          return t('objectionHandling');
        default:
          return t('faceToFaceMeeting');
      }
    },
    [t],
  );

  const {
    data: fullHistoryData,
    isFetchingNextPage,
    isLoading,
    hasNextPage,
    fetchNextPage,
    refetch: refetchFullHistory,
  } = useReportHistory(true, getConversationType());

  // Reusable function to handle data fetching
  const handleDataFetch = useCallback(async () => {
    setIsFetching(true);
    try {
      await refetchFullHistory();
    } catch (error) {
      console.log('Error fetching data:', error);
    } finally {
      setIsFetching(false);
    }
  }, [refetchFullHistory]);

  // Refetch data when tab changes
  useEffect(() => {
    handleDataFetch();
  }, [activeTabIndex, handleDataFetch]);

  // Refetch data when screen is focused
  useFocusEffect(
    useCallback(() => {
      handleDataFetch();
    }, [handleDataFetch]),
  );

  // Update activeTabIndex when defaultActiveTabIndex changes
  useEffect(() => {
    if (
      defaultActiveTabIndex !== undefined &&
      defaultActiveTabIndex !== activeTabIndex
    ) {
      setActiveTabIndex(defaultActiveTabIndex);
    }
  }, [defaultActiveTabIndex]);

  // Reset activeTabIndex if it becomes invalid due to tab filtering
  useEffect(() => {
    if (activeTabIndex >= availableTabs.length) {
      setActiveTabIndex(0);
    }
  }, [availableTabs.length, activeTabIndex]);

  // Handle tab press
  const handleTabPress = useCallback(
    (index: number) => {
      if (index !== activeTabIndex) {
        setActiveTabIndex(index);
        handleDataFetch();
      }
    },
    [activeTabIndex, handleDataFetch],
  );
  // Process data for full history view
  const sessionHistory = useMemo(() => {
    if (!fullHistoryData) {
      return [];
    }
    let result = fullHistoryData?.pages
      ?.map(page => page.items)
      ?.flat()
      .filter(item => item.report_is_ready === 'true');
    if (isHomePage) {
      result = result.slice(0, isTabletMode ? 4 : 3);
    }
    return result;
  }, [fullHistoryData, isHomePage, isTabletMode]);

  // Navigate to session history screen
  const goToSSHistory = () => {
    navigation.navigate('SessionHistory', { activeTabIndex });
  };

  // Render history list - unified function for both home page and full history
  const renderHistoryList = () => {
    if (isLoading || (!isHomePage && isFetching)) {
      return (
        <LoadingView>
          <ActivityIndicator size="large" color={colors.fwdOrange[100]} />
        </LoadingView>
      );
    }

    if (sessionHistory.length === 0) {
      return <Label color={colors.fwdGreyDarker}>{t('noHistoryYet')}</Label>;
    }

    return (
      <FlatList
        data={sessionHistory}
        numColumns={isTabletMode ? 2 : 1}
        renderItem={({ item }) => (
          <SessionQuickFireCard
            activeTabIndex={activeTabIndex}
            session={item}
            sessionNumber={item.session_number}
          />
        )}
        keyExtractor={(item, index) => `${item.conversation_id}-${index}`}
        ItemSeparatorComponent={() => <Spacer height={sizes[3]} />}
        columnWrapperStyle={isTabletMode ? { gap: sizes[3] } : undefined}
        scrollEnabled={!isHomePage}
        ListFooterComponent={
          !isHomePage && sessionHistory.length > 0 && isFetchingNextPage ? (
            <Box
              justifyContent="center"
              alignItems="center"
              pt={space[4]}
              pb={space[4]}>
              <LoadingIndicator size={space[5]} />
            </Box>
          ) : null
        }
        onEndReachedThreshold={!isHomePage ? 0.1 : undefined}
        onEndReached={() => {
          if (!isHomePage && hasNextPage && fetchNextPage) {
            fetchNextPage();
          }
        }}
      />
    );
  };

  // Handle tab press and scroll to the tab
  const handleTabButtonPress = useCallback(
    (index: number) => {
      handleTabPress(index);
      // Scroll to the tab if on mobile
      if (!isTabletMode && scrollViewRef.current) {
        scrollViewRef.current.scrollTo({
          x: index * 150,
          animated: true,
        });
      }
    },
    [handleTabPress, isTabletMode],
  );

  return (
    <View style={{ flex: 1 }}>
      {isHomePage && <Spacer height={sizes[10]} />}
      <H6 fontWeight="bold" color={colors.white}>
        {t('yourReports')}
      </H6>
      <Spacer height={sizes[2]} />
      {availableTabs.length > 1 && (
        <TabContainer>
          <TabScrollView
            ref={scrollViewRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            bounces={false}
            contentContainerStyle={{
              alignItems: 'center',
              gap: sizes[3],
            }}>
            {availableTabs.map((tab, index) => (
              <TabButton
                key={tab.name}
                isActive={activeTabIndex === index}
                isTabletMode={isTabletMode}
                onPress={() => handleTabButtonPress(index)}>
                <H7
                  fontWeight={activeTabIndex === index ? 'bold' : 'normal'}
                  color={
                    activeTabIndex === index
                      ? colors.fwdOrange[100]
                      : colors.white
                  }>
                  {getTabName(tab)}
                </H7>
              </TabButton>
            ))}
          </TabScrollView>
        </TabContainer>
      )}

      <ContentContainer>{renderHistoryList()}</ContentContainer>

      {isHomePage && sessionHistory.length > 0 && (
        <ViewAllSSButton onPress={goToSSHistory}>
          <H8 fontWeight="bold" color={colors.fwdOrange[100]}>
            {t('viewAllSS')}
          </H8>
        </ViewAllSSButton>
      )}
    </View>
  );
};

export default HistoryCallTabs;
