import React, { useCallback, useState } from 'react';
import { Dimensions, Modal } from 'react-native';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { useTranslation } from 'react-i18next';
import styled from '@emotion/native';
import { But<PERSON>, H2, H6 } from 'cube-ui-components';
import Spacer from 'features/lead/my/LeadProfile/components/Spacer';
import { BlurView } from 'expo-blur';

const { width: windowWidth } = Dimensions.get('window');

const CenterView = styled.View(() => ({
  justifyContent: 'center',
  width: windowWidth - sizes[4] * 2,
  borderRadius: sizes[4],
  paddingHorizontal: sizes[8],
  paddingVertical: sizes[12],
  borderWidth: 5,
  borderColor: colors.white,
  maxWidth: 440,
}));

const Container = styled(BlurView)(() => ({
  flex: 1,
  backgroundColor: 'rgba(24, 48, 40, 0.7)',
  justifyContent: 'center',
  alignItems: 'center',
}));

type ModalProps = {
  title?: string;
  avatarName: string;
  reTake: () => void;
  onExitRole: () => void;
};

const LeavingSoSoonModal = ({
  title,
  avatarName,
  reTake,
  onExitRole,
}: ModalProps) => {
  const [modalVisible, setModalVisible] = useState(true);
  const { t } = useTranslation('ecoach');

  const tryAgain = useCallback(() => {
    setModalVisible(false);
    reTake();
  }, [reTake]);

  return (
    <Modal animationType="slide" transparent={true} visible={modalVisible}>
      <Container tint="extraLight" intensity={30}>
        <CenterView>
          <H2
            fontWeight="bold"
            color={colors.white}
            style={{ textAlign: 'center', paddingBottom: sizes[8] }}>
            {title || t('leavingSoSoon')}
          </H2>
          <H6
            fontWeight={'normal'}
            color={colors.fwdGrey[20]}
            style={{ textAlign: 'center', paddingVertical: sizes[4] }}>
            {t('isSadToSeeYouLeave', { avatarName })}
          </H6>
          <Spacer height={sizes[4]} />
          <Button
            variant={'secondary'}
            onPress={tryAgain}
            text={t('tryAgain')}
          />
          <Spacer height={sizes[4]} />
          <Button
            variant={'tertiary'}
            onPress={onExitRole}
            text={t('exitQuickfire')}
          />
        </CenterView>
      </Container>
    </Modal>
  );
};

export default LeavingSoSoonModal;
