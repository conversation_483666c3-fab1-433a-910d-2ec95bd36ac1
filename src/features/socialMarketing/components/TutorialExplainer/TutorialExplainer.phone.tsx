import React, { useState, useEffect } from 'react';
import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { Icon } from 'cube-ui-components';
import { useLayoutStore } from 'features/socialMarketing/hooks/useLayoutStore';
import { TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import TutorialExplainerStepThreePhone from './steps/TutorialExplainerStepThree.phone';
import TutorialExplainerStepTwoPhone from './steps/TutorialExplainerStepTwo.phone';
import TutorialExplainerStepOnePhone from './steps/TutorialExplainerStepOne.phone';

const TutorialExplainerContainer = styled(SafeAreaView)(
  ({ theme: { colors, space } }) => ({
    position: 'absolute',
    flex: 1,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    alignItems: 'center',
    paddingHorizontal: space[4],
    paddingVertical: space[2],
    backgroundColor: colors.palette.fwdDarkGreenTransparent[85],
  }),
);
const TouchableOpacityContainer = styled(TouchableOpacity)(() => ({
  alignSelf: 'flex-end',
}));

export default function TutorialExplainerPhone() {
  const { colors } = useTheme();
  const [visible, setVisible] = useState(true);
  const [step, setStep] = useState(1);
  const { setOnboardSocial } = useLayoutStore();

  const onClose = () => {
    setVisible(false);
    setOnboardSocial(true);
  };

  useEffect(() => {
    if (!visible) return;

    setStep(1);

    const timeout1 = setTimeout(() => setStep(2), 2400);
    const timeout2 = setTimeout(() => setStep(3), 2400 * 2);

    return () => {
      clearTimeout(timeout1);
      clearTimeout(timeout2);
    };
  }, [visible]);

  if (!visible) {
    return null;
  }

  const renderStep = () => {
    switch (step) {
      case 1:
        return <TutorialExplainerStepOnePhone />;
      case 2:
        return <TutorialExplainerStepTwoPhone />;
      case 3:
        return <TutorialExplainerStepThreePhone />;
      default:
        return null;
    }
  };

  return (
    <TutorialExplainerContainer edges={['top', 'bottom']}>
      <TouchableOpacityContainer onPress={onClose}>
        <Icon.Close fill={colors.palette.white} />
      </TouchableOpacityContainer>
      {renderStep()}
    </TutorialExplainerContainer>
  );
}
