import { useTheme } from '@emotion/react';
import { Typography } from 'cube-ui-components';
import TutorialArrowTwoSVG from 'features/socialMarketing/assets/TutorialArrowTwoSVG';
import React from 'react';
import { useTranslation } from 'react-i18next';
import Animated, { FadeIn, LinearTransition } from 'react-native-reanimated';
import {
  HandDrawText,
  RotateSVG,
  TutorialGridCol,
  TutorialGridContainer,
  TutorialImage,
} from './styled';

export default function TutorialExplainerStepTwoPhone() {
  const { colors, animation, space } = useTheme();
  const { t } = useTranslation('socialMarketing');

  return (
    <>
      <Typography.H6 color={colors.palette.fwdOrange[100]} fontWeight="bold">
        {t('tutorial.feelingAdventurous')}
      </Typography.H6>
      <HandDrawText>{t('tutorial.makeYourOwn')}</HandDrawText>
      <TutorialGridContainer>
        <TutorialGridCol>
          <Animated.View
            layout={LinearTransition.duration(animation.duration)}
            entering={FadeIn}>
            <Typography.LargeBody
              color={colors.palette.fwdOrange[100]}
              fontWeight="medium"
              style={{
                maxWidth: 200,
                paddingBottom: space[2],
              }}>
              {t('tutorial.createFromScratch')}
            </Typography.LargeBody>
            <RotateSVG>
              <TutorialArrowTwoSVG width={80} height={100} />
            </RotateSVG>
          </Animated.View>
        </TutorialGridCol>
      </TutorialGridContainer>
      <TutorialImage
        source={require('assets/bottom-blur-navigation.png')}
        resizeMode="cover"
      />
    </>
  );
}
