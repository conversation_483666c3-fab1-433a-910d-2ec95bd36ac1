import styled from '@emotion/native';
import { View, Image } from 'react-native';
import { Text } from 'cube-ui-components';
import { Dimensions } from 'react-native';

const deviceWidth = Dimensions.get('window').width;

export const TutorialGridContainer = styled(View)(
  ({ theme: { colors, space } }) => ({
    alignItems: 'center',
    paddingVertical: space[3],
    flex: 1,
  }),
);

export const TutorialGalleryHighlight = styled(View)<{
  height?: number;
  width?: number;
}>(({ theme: { colors, space }, height }) => ({
  width: '100%',
  height,
  top: space[5],
}));

export const TutorialGridItem = styled(View)(
  ({ theme: { colors, space } }) => ({
    flex: 1,
  }),
);

export const TutorialGrid = styled(View)(({ theme: { colors, space } }) => ({
  flexDirection: 'row',
  gap: space[1],
}));

export const TutorialGridTextItem = styled(View)(
  ({ theme: { colors, space } }) => ({
    flex: 1,
  }),
);

export const TransformSVG = styled(View)(({ theme: { colors, space } }) => ({
  transform: 'scaleX(-1)',
}));

export const RotateSVG = styled(View)(({ theme: { space } }) => ({
  transform: [
    { rotate: '3deg' },
    {
      translateX: space[16],
    },
  ],
}));

export const TutorialGridCol = styled(View)(({ theme: { colors, space } }) => ({
  justifyContent: 'center',
  position: 'absolute',
  bottom: space[26],
  gap: space[24],
  left: 0,
  flex: 1,
}));

export const HandDrawText = styled(Text)<{ fontSize?: number }>(
  ({ theme: { colors, typography }, fontSize }) => ({
    fontFamily: 'FWDHandwriting',
    fontSize: fontSize || typography.h2.size,
    lineHeight: typography.h1.lineHeight,
    color: colors.primary,
  }),
);

export const HandDrawBlackText = styled(Text)(({ theme }) => ({
  fontFamily: 'FWDHandwriting-Black',
  fontSize: theme.typography.h1.size,
  lineHeight: theme.typography.h1.lineHeight,
  color: theme.colors.primary,
}));

export const TutorialJournalCol = styled(View)(({ theme }) => ({
  flex: 1,
  width: deviceWidth,
  justifyContent: 'center',
  alignItems: 'center',
}));

export const TutorialImage = styled(Image)(({ theme }) => ({
  position: 'absolute',
  bottom: 0,
  left: 0,
  right: 0,
  width: deviceWidth,
  height: 140,
}));
