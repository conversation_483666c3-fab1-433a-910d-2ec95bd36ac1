import { useTheme } from '@emotion/react';
import { Typography } from 'cube-ui-components';
import TutorialArrowSVG from 'features/socialMarketing/assets/TutorialArrowSVG';
import React from 'react';
import { useTranslation } from 'react-i18next';
import Animated, { FadeIn, LinearTransition } from 'react-native-reanimated';
import GalleryMediaCard from '../../GalleryMediaCard';
import {
  HandDrawText,
  TransformSVG,
  TutorialGalleryHighlight,
  TutorialGrid,
  TutorialGridContainer,
  TutorialGridItem,
  TutorialGridTextItem,
} from './styled';

export default function TutorialExplainerStepOnePhone() {
  const { colors, space, animation } = useTheme();
  const { t } = useTranslation('socialMarketing');

  return (
    <>
      <Typography.H6 color={colors.palette.fwdOrange[100]} fontWeight="bold">
        {t('tutorial.createPost')}
      </Typography.H6>
      <HandDrawText>{t('tutorial.chooseATemplate')}</HandDrawText>
      <TutorialGridContainer>
        <TutorialGrid>
          <TutorialGridTextItem>
            <Animated.View
              layout={LinearTransition.duration(animation.duration)}
              entering={FadeIn}>
              <TransformSVG>
                <TutorialArrowSVG width={80} height={100} />
              </TransformSVG>
              <Typography.LargeBody
                color={colors.palette.fwdOrange[100]}
                fontWeight="medium"
                style={{
                  paddingLeft: space[6],
                  paddingVertical: space[2],
                  textAlign: 'right',
                }}>
                {t('tutorial.customizeYourVideo')}
              </Typography.LargeBody>
            </Animated.View>
          </TutorialGridTextItem>
          <TutorialGridItem>
            <TutorialGalleryHighlight height={300}>
              <GalleryMediaCard
                media={{
                  uid: 'video-1',
                  title: 'Short video 1',
                  url: 'https://cdn.pixabay.com/video/2021/04/17/71350-538489808_large.mp4',
                  content_type: 'video/mp4',
                }}
                autoPlay
              />
            </TutorialGalleryHighlight>
          </TutorialGridItem>
        </TutorialGrid>
      </TutorialGridContainer>
    </>
  );
}
