import { useTheme } from '@emotion/react';
import { Typography } from 'cube-ui-components';
import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { HandDrawBlackText, HandDrawText, TutorialJournalCol } from './styled';

export default function TutorialExplainerStepThreePhone() {
  const { colors, typography } = useTheme();
  const { t } = useTranslation('socialMarketing');

  const translateY = useSharedValue(-150);
  const opacity = useSharedValue(0);

  useEffect(() => {
    translateY.value = withTiming(0, {
      duration: 1500,
      easing: Easing.out(Easing.exp),
    });
    opacity.value = withTiming(1, {
      duration: 1500,
      easing: Easing.out(Easing.exp),
    });
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
    opacity: opacity.value,
  }));

  return (
    <Animated.View style={animatedStyle}>
      <TutorialJournalCol>
        <Typography.H6 color={colors.palette.fwdOrange[100]} fontWeight="bold">
          {t('tutorial.madeSimple')}
        </Typography.H6>
        <HandDrawText fontSize={typography.h3.size}>
          {t('tutorial.yourJourney')}
        </HandDrawText>
        <HandDrawBlackText>{t('tutorial.here')}</HandDrawBlackText>
      </TutorialJournalCol>
    </Animated.View>
  );
}
