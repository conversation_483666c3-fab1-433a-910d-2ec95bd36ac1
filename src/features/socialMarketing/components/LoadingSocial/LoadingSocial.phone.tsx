import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import Skeleton from 'components/Skeleton';
import { Box, Column, Typography } from 'cube-ui-components';
import React from 'react';
import { Image, View } from 'react-native';
import { LoadingSocialProps } from './types';

const ContainerSvg = styled(View)(({ theme }) => ({
  justifyContent: 'center',
  alignItems: 'center',
  paddingHorizontal: theme.space[10],
  paddingVertical: theme.space[20],
}));

const Percentage = styled(View)(({ theme }) => ({
  flexDirection: 'row',
  gap: theme.space[2],
}));

const Title = styled(Typography.H7_2)(({ theme }) => ({
  color: theme.colors.palette.fwdDarkGreen[100],
  marginBottom: theme.space[1],
  marginTop: theme.space[6],
  fontWeight: 'bold',
}));

const Description = styled(Typography.H8)(({ theme }) => ({
  color: theme.colors.palette.fwdGreyDarkest,
  textAlign: 'center',
}));

export default function LoadingSocialPhone({
  title,
  description,
}: LoadingSocialProps) {
  const { colors, space } = useTheme();

  return (
    <>
      <View>
        <Image
          source={require('assets/loading-bg.jpg')}
          style={{
            position: 'absolute',
            width: '100%',
            height: '100%',
          }}
          resizeMode="cover"
        />
        <ContainerSvg>
          <Image
            source={require('assets/loading-temp.png')}
            style={{
              width: 180,
              height: 165,
            }}
            resizeMode="cover"
          />
          <Percentage>
            <Title>{title}</Title>
          </Percentage>
          {description && <Description>{description}</Description>}
        </ContainerSvg>
      </View>

      <Column gap={space[2]}>
        <Box px={space[6]} py={space[6]} bgColor={colors.background}>
          <Column style={{ gap: space[3] }}>
            <Skeleton width={'100%'} height={18} radius={4} />
            <Skeleton width={'100%'} height={18} radius={4} />
            <Skeleton width={'100%'} height={18} radius={4} />
            <Skeleton width={'50%'} height={18} radius={4} />
          </Column>
        </Box>
      </Column>
    </>
  );
}
