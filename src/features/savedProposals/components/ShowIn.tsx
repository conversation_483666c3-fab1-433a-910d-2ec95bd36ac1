import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import { DropdownPanel, Icon, Text } from 'cube-ui-components';
import { useEffect } from 'react';
import { memo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';

const Group = styled.Pressable(() => ({
  flexDirection: 'row',
  alignItems: 'center',
}));

const Label = styled(Text)(({ theme }) => ({
  color: theme.colors.secondary,
  fontSize: theme.typography.label.size,
}));

const Status = styled(Text)(({ theme }) => ({
  color: theme.colors.palette.fwdAlternativeOrange[100],
  fontSize: theme.typography.label.size,
  marginLeft: theme.space[1],
}));

interface Props {
  value?: string;
  onApply?: (value: string) => void;
}

export const ShowIn = memo(function ShowIn({
  value,
  onApply: onApplyAction,
}: Props) {
  const { t } = useTranslation(['savedProposals']);
  const { colors } = useTheme();
  const [visible, setVisible] = useState(false);
  const [option, setOption] = useState<{ label: string; value: string }>({
    label: t('savedProposals:lastDays', { day: 60 }),
    value: '60',
  });
  useEffect(() => {
    if (visible) {
      if (value === '60') {
        setOption({
          label: t('savedProposals:lastDays', { day: 60 }),
          value: '60',
        });
      } else if (value === '30') {
        setOption({
          label: t('savedProposals:lastDays', { day: 30 }),
          value: '30',
        });
      }
    }
  }, [visible, value]);

  const handleOpen = useCallback(() => {
    setVisible(true);
  }, []);

  const handleClose = useCallback(() => {
    setVisible(false);
  }, []);

  const onApply = useCallback(
    (newOption: typeof option) => {
      setVisible(false);
      onApplyAction?.(newOption.value);
    },
    [onApplyAction],
  );

  return (
    <Group onPress={handleOpen}>
      <Label>{t('savedProposals:showIn')}</Label>
      <Status fontWeight="bold">
        {t('savedProposals:inDays', { day: value })}
      </Status>
      <Icon.ChevronDown
        size={18}
        fill={colors.palette.fwdAlternativeOrange[100]}
      />
      <DropdownPanel
        visible={visible}
        onDismiss={handleClose}
        onDone={onApply}
        title={t('savedProposals:showDataIn')}
        actionLabel={t('savedProposals:apply')}
        selectedItem={option}
        data={[
          { label: t('savedProposals:lastDays', { day: 60 }), value: '60' },
          { label: t('savedProposals:lastDays', { day: 30 }), value: '30' },
        ]}
        getItemValue={item => item.value}
        getItemLabel={item => item.label}
      />
    </Group>
  );
});
