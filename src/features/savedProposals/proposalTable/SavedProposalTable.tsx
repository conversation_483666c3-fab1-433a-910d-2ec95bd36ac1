import styled from '@emotion/native';
import { Pressable, View } from 'react-native';
import * as ScreenOrientation from 'expo-screen-orientation';
import { StatusBar } from 'expo-status-bar';
import { useTheme } from '@emotion/react';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';
import CloseButton from './components/CloseButton';
import { Platform, useWindowDimensions, StyleSheet } from 'react-native';
import {
  NavigationProp,
  RouteProp,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { RootStackParamList } from 'types';
import HeaderCell from './components/HeaderCell';
import TextCell from './components/TextCell';
import StageCell from './components/StageCell';
import ProposalCell from './components/ProposalCell';
import { useTranslation } from 'react-i18next';
import { Proposal } from 'types/proposal';
import { formatCurrency } from 'utils';
import { format } from 'date-fns';
import * as Device from 'expo-device';
import { Icon, Table } from 'cube-ui-components';
import { useGetSavedProposals } from 'hooks/useGetSavedProposals';
import { dateFormatUtil } from 'utils/helper/formatUtil';

type ValueOf<T> = T[keyof T];

type HeaderContent = string | JSX.Element;

type HeaderCellContent = {
  content: HeaderContent;
  span?: number;
  isSticky?: boolean;
};

type ColumnDefinition<T> = {
  key: keyof T;
  headers: HeaderContent | HeaderContent[];
  width?: number;
  flex?: number;
  group?: number;
  render?: (row: T, key: keyof T, data: T[]) => string | number | JSX.Element;
};

type HeaderDefinition = {
  headers: HeaderCellContent[];
};

const Container = styled.View(({ theme }) => ({
  backgroundColor: theme.colors.surface,
  flex: 1,
}));

export const SavedProposalTable = memo(function SaveProposalTable() {
  const { t } = useTranslation(['common', 'savedProposals']);
  const { width, height } = useWindowDimensions();
  const { colors, space } = useTheme();
  const params =
    useRoute<RouteProp<RootStackParamList, 'ProposalTable'>>()?.params.params;
  const {
    isLoading,
    isFetchingNextPage,
    refetch,
    hasNextPage,
    fetchNextPage,
    data: paginationData,
  } = useGetSavedProposals(params);

  const styles = StyleSheet.create({
    firstFreeze: {
      paddingLeft: space[6],
    },
  });

  const renderHeader = useCallback(
    (
      data: { title: string; icon?: JSX.Element | undefined },
      freezeCol?: boolean,
    ) => {
      return <HeaderCell data={data} style={freezeCol && styles.firstFreeze} />;
    },
    [],
  );

  const rowDefs: HeaderDefinition = {
    headers: [
      {
        content: renderHeader(
          {
            title: `${t('savedProposals:proposalName')}/\n${t(
              'savedProposals:insured',
            )}`,
          },
          true,
        ),
        isSticky: true,
      },
      {
        content: renderHeader({ title: t('savedProposals:productName') }),
      },
      {
        content: renderHeader({
          title: `${t('savedProposals:premiumAmount')}\n${t('common:php')}`,
        }),
      },
      {
        content: renderHeader({
          title: `${t('savedProposals:sumAssured')}\n${t('common:php')}`,
        }),
      },
      {
        content: renderHeader({ title: t('savedProposals:proposalNo') }),
      },
      {
        content: renderHeader({ title: t('savedProposals:status') }),
      },
      {
        content: renderHeader({
          title: t('savedProposals:lastUpdate'),
          icon:
            params.order === 'newest' ? (
              <Icon.ArrowUp fill="white" />
            ) : (
              <Icon.ArrowDown fill="white" />
            ),
        }),
      },
    ],
  };
  const colDefs: ColumnDefinition<Proposal>[] = [
    {
      key: 'proposalName',
      headers: (
        <HeaderCell
          data={{
            title: `${t('savedProposals:proposalName')}/\n${t(
              'savedProposals:insured',
            )}`,
          }}
          style={styles.firstFreeze}
        />
      ),
      width: space[52],
      render: (row, key, data) => {
        return (
          <ProposalCell
            rowIndex={2}
            onPress={() => onPressItem(row)}
            style={[styles.firstFreeze]}
            data={row}
          />
        );
      },
    },
    {
      key: 'productName',
      headers: <HeaderCell data={{ title: t('savedProposals:productName') }} />,
      width: space[40],
      render: (row, key, data) => {
        return (
          <TextCell
            rowIndex={1}
            data={row['productName']?.en || '--'}
            onPress={() => onPressItem(row)}
          />
        );
      },
    },
    {
      key: 'modalPremium',
      headers: (
        <HeaderCell
          data={{
            title: `${t('savedProposals:premiumAmount')}\n${t('common:php')}`,
          }}
        />
      ),
      width: space[40],
      render: (row, key, data) => {
        return (
          <TextCell
            rowIndex={2}
            data={
              row['modalPremium'] ? formatCurrency(row['modalPremium']) : '--'
            }
            onPress={() => onPressItem(row)}
          />
        );
      },
    },
    {
      key: 'sumAssured',
      headers: (
        <HeaderCell
          data={{
            title: `${t('savedProposals:sumAssured')}\n${t('common:php')}`,
          }}
        />
      ),
      width: space[40],
      render: (row, key, data) => {
        return (
          <TextCell
            rowIndex={2}
            data={row['sumAssured'] ? formatCurrency(row['sumAssured']) : '--'}
            onPress={() => onPressItem(row)}
          />
        );
      },
    },
    {
      key: 'proposalId',
      headers: <HeaderCell data={{ title: t('savedProposals:proposalNo') }} />,
      width: space[40],
      render: (row, key, data) => {
        return (
          <TextCell rowIndex={1} data={'--'} onPress={() => onPressItem(row)} />
        );
      },
    },
    {
      key: 'status',
      headers: <HeaderCell data={{ title: t('savedProposals:status') }} />,
      width: space[36],
      render: (row, key, data) => {
        return (
          <StageCell
            rowIndex={1}
            data={t(`savedProposals:filter.${row['status']}`)}
            onPress={() => onPressItem(row)}
          />
        );
      },
    },
    {
      key: 'updatedAt',
      headers: (
        <HeaderCell
          data={{
            title: t('savedProposals:lastUpdate'),
            icon:
              params.order === 'newest' ? (
                <Icon.ArrowUp fill="white" />
              ) : (
                <Icon.ArrowDown fill="white" />
              ),
          }}
        />
      ),
      width: space[55],
      render: (row, key, data) => {
        return (
          <TextCell
            rowIndex={1}
            data={dateFormatUtil(new Date(row['updatedAt']))}
            onPress={() => onPressItem(row)}
          />
        );
      },
    },
  ];

  const data = useMemo(() => {
    const pagedData = paginationData
      ? paginationData.pages
          ?.reduce<Proposal[]>((data, page) => data.concat(page.list), [])
          .map(p => {
            return p;
          })
      : [];
    return pagedData;
  }, [paginationData, t]);

  const [isTablet, setIsTablet] = useState(false);
  useEffect(() => {
    Device.getDeviceTypeAsync().then(type =>
      setIsTablet(type === Device.DeviceType.TABLET),
    );
  }, []);
  const isPhone =
    Platform.OS === 'android' || (Platform.OS === 'ios' && !isTablet);

  const { navigate } = useNavigation<NavigationProp<RootStackParamList>>();
  const onPressItem = useCallback((data?: Proposal) => {
    navigate('SalesIllustrationForm', { from: 'saved_proposals' });
  }, []);

  useEffect(() => {
    const rotateLandscape = async () => {
      await ScreenOrientation.unlockAsync();
      await ScreenOrientation.lockAsync(
        ScreenOrientation.OrientationLock.LANDSCAPE_LEFT,
      );
    };
    const rotatePortrait = async () => {
      await ScreenOrientation.unlockAsync();
      await ScreenOrientation.lockAsync(
        ScreenOrientation.OrientationLock.PORTRAIT_UP,
      );
    };
    rotateLandscape();
    return () => {
      rotatePortrait();
    };
  }, []);

  const renderCell = useCallback(
    (rIndex: number, cIndex: number, proposal: Proposal, data: string) => {
      if (cIndex === 0) {
        return (
          <ProposalCell
            rowIndex={rIndex}
            onPress={() => onPressItem(proposal)}
            style={styles.firstFreeze}
            data={proposal}
          />
        );
      }
      if (cIndex === 5) {
        return (
          <StageCell
            rowIndex={rIndex}
            data={data}
            onPress={() => onPressItem(proposal)}
          />
        );
      }
      return (
        <TextCell
          rowIndex={rIndex}
          data={data}
          style={cIndex === 0 && styles.firstFreeze}
          onPress={() => onPressItem(proposal)}
        />
      );
    },
    [paginationData, onPressItem],
  );

  return (
    <Container>
      <StatusBar hidden />
      <Table
        style={{ width: '100%' }}
        headers={rowDefs}
        columns={colDefs}
        keyExtractor={item => `${item.caseId}`}
        data={data}
        rowStyle={(r, i) => ({
          backgroundColor:
            i % 2 === 0 ? colors.palette.white : colors.palette.fwdGrey[20],
        })}
      />
      <CloseButton isPhone={isPhone} />
    </Container>
  );
});
export default SavedProposalTable;
