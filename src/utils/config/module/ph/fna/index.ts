import { FNAConfig } from 'types/moduleConfig';

const fnaConfig: FNAConfig = {
  nameInputMode: 'modal',
  hasDefaultGoalValue: false,
  hasDisclaimer: false,
  hasVulnerableCustomerCheck: false,
  hasGoalPlan: false,
  hasGoalTooltip: true,
  hasShortcut: true,
  lifeStageItems: {
    SINGLE: ['ageToRetire'],
    SINGLE_WITH_DEPENDENT: ['ageToRetire'],
    COUPLE: ['ageToRetire'],
    COUPLE_WITH_KIDS: ['ageToRetire', 'numberOfKids'],
    EMPTY_NESTER: ['ageToRetire', 'havePartner', 'planToTravel'],
    RETIRED: ['havePartner', 'planToTravel'],
  },
  concerns: {
    minSelection: 1,
    maxSelection: 3,
    items: [
      'INCOME_PROTECTION',
      'HEALTH_PROTECTION',
      'EDUCATION',
      'RETIREMENT',
      'LOAN_PROTECTION',
      'LEGACY_PLANNING',
      'INVESTMENT',
    ],
    additionalItems: [],
  },
  goalGroupUIRatio: {
    saving: 3,
    protection: 4,
  },
  incomeType: 'monthly',
  incomeOptions: {
    monthly: [
      { from: null, to: 50000 },
      { from: 50000, to: 100000 },
      { from: 100000, to: 150000 },
      { from: 150000, to: 200000 },
      { from: 200000, to: 250000 },
      { from: 250000, to: null },
    ],
    annually: [
      { from: null, to: 2000000 },
      { from: 2000000, to: null },
    ],
  },
  productSelectionGuruFAB: false,
  previewFNAMandatory: false,
  previewFNASharable: true,
  shouldDisplaySummaryNeeds: true,
  incomeTooltip: [],
  shouldHideProgressBarFor0Needs: false,
};

export default fnaConfig;
