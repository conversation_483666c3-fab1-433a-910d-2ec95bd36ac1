import { ConcernId } from 'features/fna/types/concern';
import { FNAConfig } from 'types/moduleConfig';

const allConcerns: ConcernId[] = [
  'ACCIDENT_EXPENSES',
  'CRITICAL_ILLNESS',
  'HEALTH_PROTECTION',
  'LEGACY_PLANNING',
  'EDUCATION',
  'RETIREMENT',
  'SAVINGS',
];

const fnaConfig: FNAConfig = {
  nameInputMode: 'text',
  hasDefaultGoalValue: false,
  hasDisclaimer: false,
  hasVulnerableCustomerCheck: false,
  hasGoalPlan: false,
  hasGoalTooltip: false,
  lifeStageItems: {
    SINGLE: [],
    COUPLE: [],
    COUPLE_WITH_KIDS: [],
    RETIRED: [],
  },
  hasShortcut: true,
  concerns: {
    minSelection: 1,
    maxSelection: allConcerns.length,
    items: allConcerns,
    additionalItems: [],
  },
  goalGroupUIRatio: {
    saving: 4,
    protection: 3,
  },
  incomeType: 'monthly',
  incomeOptions: {
    annually: [
      { from: null, to: 25000 },
      { from: 25000, to: 35999 },
      { from: 36000, to: 50999 },
      { from: 51000, to: 75999 },
      { from: 76000, to: 99999 },
      { from: 100000, to: 149999 },
      { from: 150000, to: 200000 },
      { from: 200000, to: null },
    ],
    monthly: [
      { from: null, to: 5000000 },
      { from: 5000000, to: 25000000 },
      { from: 25000000, to: 100000000 },
      { from: 100000000, to: null },
    ],
  },
  insuranceProtectionPeriodOptions: [
    { from: null, to: 99 },
    { from: 5, to: 10 },
    { from: 10, to: 20 },
    { from: 20, to: null },
  ],
  productSelectionGuruFAB: false,
  previewFNAMandatory: false,
  previewFNASharable: false,
  shouldDisplaySummaryNeeds: false,
  incomeTooltip: [],
  shouldHideProgressBarFor0Needs: false,
};

export default fnaConfig;
