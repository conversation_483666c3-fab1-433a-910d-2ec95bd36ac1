import { ConcernId } from 'features/fna/types/concern';
import { myNumberOfDependents } from 'features/fna/types/lifeJourney';
import { CHANNELS } from 'types/channel';
import { FNAConfig } from 'types/moduleConfig';
import { build } from 'utils/context';

const allConcerns: ConcernId[] = [
  'INCOME_PROTECTION',
  'RETIREMENT',
  'EDUCATION',
  'SAVINGS',
  'INVESTMENT',
  'HEALTH_PROTECTION',
];

const fnaConfig: FNAConfig = {
  nameInputMode: 'text',
  hasDefaultGoalValue: true,
  hasGoalPlan: true,
  hasGoalTooltip: false,
  hasDisclaimer: false,
  hasVulnerableCustomerCheck: false,
  hasShortcut: true,
  lifeStageItems: {
    SINGLE: ['ageToRetire'],
    SINGLE_WITH_DEPENDENT: [myNumberOfDependents, 'ageToRetire'],
    COUPLE: ['ageToRetire'],
    COUPLE_WITH_KIDS: [myNumberOfDependents, 'ageToRetire'],
  },
  concerns: {
    minSelection: 6,
    maxSelection: 6,
    items: allConcerns,
    additionalItems: [],
  },
  goalGroupUIRatio: {
    saving: 4,
    protection: 3,
  },
  compulsory: {
    FULL: { byConcerns: allConcerns },
    PARTIAL: { byPriorities: 3, byConcerns: ['HEALTH_PROTECTION'] },
    UNKNOWN: { byPriorities: 0, byConcerns: [] },
  },
  incomeType: 'annually',
  incomeOptions: {
    annually: [
      { from: null, to: 25000 },
      { from: 25000, to: 35999 },
      { from: 36000, to: 50999 },
      { from: 51000, to: 75999 },
      { from: 76000, to: 99999 },
      { from: 100000, to: 149999 },
      { from: 150000, to: 200000 },
      { from: 200000, to: null },
    ],
    monthly: [
      { from: null, to: 2083 },
      { from: 2083, to: 3000 },
      { from: 3000, to: 4250 },
      { from: 4250, to: 6333 },
      { from: 6333, to: 8333 },
      { from: 8333, to: 12500 },
      { from: 12500, to: 16667 },
      { from: 16667, to: null },
    ],
  },
  productSelectionGuruFAB: false,
  previewFNAMandatory: true,
  previewFNASharable: false,
  shouldDisplaySummaryNeeds: true,
  incomeTooltip: [CHANNELS.BANCA],
  shouldHideProgressBarFor0Needs: true,
};

export default fnaConfig;
