import {
  BlackListAgentResponse,
  DukcapilRequestBody,
  DukcapilValidationResponse,
} from 'types/dukcapil';
import { LocaleHeader } from 'types/localeHeader';
import { ApplicationPdfResponse } from 'types/pdfResponse';
import { cubeClient } from './cubeClient';

type ExclusiveLetterType = 'HCB' | 'CI';

export const generateApplicationPdf = async (
  body: unknown,
  localeHeader: LocaleHeader,
) => {
  const response = await cubeClient.post<unknown, ApplicationPdfResponse>(
    '/exp/api/pdf/app',
    {
      case: body,
    },
    {
      headers: {
        ...localeHeader,
      },
    },
  );

  return (
    response?.report?.pdfFiles?.UNPROTECTED_PDF ??
    response?.pdfFiles?.UNPROTECTED_PDF
  );
};

export const sendRSEmailConfirmation = (
  caseId: string,
  exclusiveLetter?: ExclusiveLetterType,
) => {
  return cubeClient.post(
    `/exp/case/send-remote-selling-confirmation/${caseId}`,
    {
      exclusiveLetter,
    },
  );
};

export const getInitialPremium = (caseId: string) => {
  return cubeClient.get<{ currency: 'string'; initialPremium: number }>(
    `/exp/case/${caseId}/payment-info`,
  );
};

// IDN Eapp validation with local service
export const getDukcapilValidation = (body: DukcapilRequestBody) => {
  return cubeClient.post<unknown, DukcapilValidationResponse>(
    '/exp/api/nric/validate',
    body,
  );
};

// IDN Eapp blacklist agent checking with local service
export const getBlacklistValidation = (body: { idNumber: string }) => {
  return cubeClient.post<unknown, BlackListAgentResponse>(
    '/exp/api/nric/blacklist-check',
    body,
  );
};
