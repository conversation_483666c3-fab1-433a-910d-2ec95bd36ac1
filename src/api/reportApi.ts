import { format } from 'date-fns';
import { cubeClient } from './cubeClient';
import {
  ReportStatement,
  ReportListResponseData,
  reportDownloadRequestObj,
  Status,
  PolicyAnniversaryItem,
  UnsuccessfulAdaAcaItem,
  CreditCardExpirationItem,
  ReportResponseData,
} from 'types/report';
import * as FileSystem from 'expo-file-system';
import * as Sharing from 'expo-sharing';
import { TRANSACTION_REPORT_POST_BODY } from 'features/reportGeneration/my/util/transactionReportTableConfig';
import { DUE_DATE_REPORT_POST_BODY } from 'features/reportGeneration/my/util/dueDateReportTableConfig';
import { APPNOTPROCEED_REPORT_POST_BODY } from 'features/reportGeneration/my/util/applicationNotProceedTableConfig';
import { INQUIRIES_REPORT__POST_BODY } from 'features/reportGeneration/my/util/inquiriesReportTableConfig';

/**
 * Commission statement report
 * Tax statement report
 * Persistency report
 */
export async function getReportListByReportStatement(
  ReportStatement: ReportStatement,
) {
  const ENDPOINT = `/exp/api/report/list/${ReportStatement}`;
  return await cubeClient.get<ReportListResponseData>(ENDPOINT, {});
}

export async function getDownloadReport(
  reportDownloadRequestObj: reportDownloadRequestObj,
) {
  const ENDPOINT = `/exp/api/report/download`;
  return await cubeClient.get<ReportListResponseData>(ENDPOINT, {
    params: {
      documentIndex: reportDownloadRequestObj.documentIndex,
      documentCode: reportDownloadRequestObj.documentCode,
    },
    headers: {
      tenant: reportDownloadRequestObj.tenant,
    },
  });
}

/**
 * Lapsed policies report
 * Premium received report
 * Credit card expiration report
 * Unsuccessful ADA/ACA report
 * Policy anniversary
 */
export async function getInquiriesReport({
  agentId,
  from,
  to,
  dueDateReportPolicyInfo,
  team,
  status,
}: {
  agentId?: string;
  from: string;
  to: string;
  dueDateReportPolicyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
  status: { code: string; meaning: string }[];
}) {
  const ENDPOINT = '/exp/api/report/policy-report';
  return await cubeClient.get<ReportResponseData>(ENDPOINT, {
    params: {
      agent_id: agentId,
      date_from: from,
      date_to: to,
      policy_owner_name: dueDateReportPolicyInfo?.policyHolderName,
      policy_number: dueDateReportPolicyInfo?.policyNumber,
      team: team,
      policy_status: status && status.map(item => item.code).join(','),
    },
  });
}

export async function getTransactionReport({
  agentId,
  from,
  to,
  transactionReportPolicyInfo,
  team,
  dateType,
}: {
  agentId?: string;
  from: string;
  to: string;
  transactionReportPolicyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
  dateType: string;
}) {
  const ENDPOINT = '/exp/api/report/policy-transaction-report';

  return await cubeClient.get<ReportResponseData>(ENDPOINT, {
    params: {
      agent_id: agentId,
      policy_number: transactionReportPolicyInfo?.policyNumber,
      policy_owner_name: transactionReportPolicyInfo?.policyHolderName,
      team: team,
      date_from: from,
      date_to: to,
      date_type: dateType,
    },
  });
}

export async function getDueDateReport({
  agentId,
  from,
  to,
  dueDateReportPolicyInfo,
  team,
}: {
  agentId?: string;
  from: string;
  to: string;
  dueDateReportPolicyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  const ENDPOINT = '/exp/api/report/due-date-report';
  return await cubeClient.get<ReportResponseData>(ENDPOINT, {
    params: {
      agent_id: agentId,
      date_from: from,
      date_to: to,
      policy_owner_name: dueDateReportPolicyInfo?.policyHolderName,
      policy_number: dueDateReportPolicyInfo?.policyNumber,
      team: team,
    },
  });
}

export async function getApplicationNotProceedReport({
  agentId,
  from,
  to,
  policyInfo,
  team,
}: {
  agentId?: string;
  from: string;
  to: string;
  policyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  const ENDPOINT = '/exp/api/report/application-not-proceed-report';
  return await cubeClient.get<ReportResponseData>(ENDPOINT, {
    params: {
      agent_id: agentId,
      date_from: from,
      date_to: to,
      policy_owner_name: policyInfo?.policyHolderName,
      policy_number: policyInfo?.policyNumber,
      team: team,
    },
  });
}

export async function getLapseReport({
  agentId,
  from,
  to,
  status,
  lapseReportPolicyInfo,
  team,
}: {
  agentId?: string;
  from?: string;
  to?: string;
  status: Status;
  lapseReportPolicyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  const ENDPOINT = '/exp/api/report/lapse-report';
  return await cubeClient.get<ReportResponseData>(ENDPOINT, {
    params: {
      agent_id: agentId,
      date_from: from,
      date_to: to,
      policy_status: status,
      policy_owner_name: lapseReportPolicyInfo?.policyHolderName,
      policy_number: lapseReportPolicyInfo?.policyNumber,
      team: team,
    },
  });
}

export async function getPremiumReceivedReport({
  agentId,
  from,
  to,
  premiumReceivedPolicyInfo,
  team,
}: {
  agentId?: string;
  from: string;
  to: string;
  premiumReceivedPolicyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  const ENDPOINT = '/exp/api/report/premium-received';
  return await cubeClient.get<ReportResponseData>(ENDPOINT, {
    params: {
      agent_id: agentId,
      date_from: from,
      date_to: to,
      policy_owner_name: premiumReceivedPolicyInfo?.policyHolderName,
      policy_number: premiumReceivedPolicyInfo?.policyNumber,
      team: team,
    },
  });
}

export async function getCreditCardExpirationReport({
  agentId,
  dateFilter,
  from,
  to,
  creditCardExpiationPolicyInfo,
  team,
}: {
  agentId?: string;
  dateFilter: 'ISSUEDATE' | 'DUEDATE';
  from: string;
  to: string;
  creditCardExpiationPolicyInfo: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  const { policyHolderName, policyNumber } = creditCardExpiationPolicyInfo;
  const ENDPOINT = '/exp/api/report/credit-card-expiration';
  return cubeClient.get<CreditCardExpirationItem[]>(ENDPOINT, {
    params: {
      agent_id: agentId,
      date_filter: dateFilter,
      date_from: from,
      date_to: to,
      policy_owner_name: policyHolderName,
      policy_number: policyNumber,
      team: team,
    },
  });
}

export async function getUnsuccessfulAdaAcaReport({
  agentId,
  dateFilter,
  from,
  to,
  unsuccessfulAdaAcaPolicyInfo,
  team,
}: {
  agentId?: string;
  dateFilter: 'ISSUEDATE' | 'DUEDATE';
  from: string;
  to: string;
  unsuccessfulAdaAcaPolicyInfo: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  const { policyHolderName, policyNumber } = unsuccessfulAdaAcaPolicyInfo;
  const ENDPOINT = '/exp/api/report/unsuccessful-ada-aca';
  return cubeClient.get<UnsuccessfulAdaAcaItem[]>(ENDPOINT, {
    params: {
      agent_id: agentId,
      date_filter: dateFilter,
      date_from: from,
      date_to: to,
      policy_owner_name: policyHolderName,
      policy_number: policyNumber,
      team: team,
    },
  });
}

export async function getPolicyAnniversaryReport({
  from,
  to,
}: {
  from: string;
  to: string;
}) {
  const ENDPOINT = '/exp/api/report/policy-anniversary';
  return await cubeClient.get<PolicyAnniversaryItem[]>(ENDPOINT, {
    params: { date_from: from, date_to: to },
  });
}

function arrayBufferToBase64(arrayBuffer: ArrayBuffer): string {
  const bytes = new Uint8Array(arrayBuffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
}

// TODO: CHLOE - refactor code later
// postTransactionReport
export async function postTransactionReport({
  agentId,
  from,
  to,
  transactionReportPolicyInfo,
  team,
  dateType,
}: {
  agentId?: string;
  from: string;
  to: string;
  transactionReportPolicyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
  dateType: string;
}) {
  const ENDPOINT = '/exp/api/report/policy-transaction-report/export';
  const params = new URLSearchParams({
    agent_id: agentId || '',
    policy_number: transactionReportPolicyInfo?.policyNumber || '',
    policy_owner_name: transactionReportPolicyInfo?.policyHolderName || '',
    team: team ? 'true' : 'false',
    date_from: from,
    date_to: to,
    date_type: dateType,
  });
  const transactionReportPayload = team
    ? TRANSACTION_REPORT_POST_BODY
    : TRANSACTION_REPORT_POST_BODY.filter(
        item => item.path !== 'agentCode' && item.path !== 'agentName',
      );
  const payload = {
    requestData: {
      headers: transactionReportPayload,
    },
  };

  const timeStamp = format(new Date(), 'MM-dd-yyyy-hh:mm:ss');
  const fileName =
    'CertificateTransactionReport-' + dateType + '-' + timeStamp + '.csv';
  const fileUri = `${FileSystem.documentDirectory}${fileName}`;

  try {
    const response = await cubeClient.postBinary(
      `${ENDPOINT}?${params.toString()}`,
      payload,
    );
    const arrayBuffer = response.data as ArrayBuffer;
    const base64String = arrayBufferToBase64(arrayBuffer);

    await FileSystem.writeAsStringAsync(fileUri, base64String, {
      encoding: FileSystem.EncodingType.Base64,
    });

    await Sharing.shareAsync(fileUri);
    return { fileUri, message: 'File successfully shared!' };
  } catch (error) {
    console.error('Error downloading or sharing the file:', error);
    throw new Error('Failed to download or share the file');
  }
}
// postDueReport
export async function postDueDateReport({
  agentId,
  from,
  to,
  dueDateReportPolicyInfo,
  team,
}: {
  agentId?: string;
  from: string;
  to: string;
  dueDateReportPolicyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  const ENDPOINT = '/exp/api/report/due-date-report/export';
  const params = new URLSearchParams({
    agent_id: agentId || '',
    date_from: from,
    date_to: to,
    policy_owner_name: dueDateReportPolicyInfo?.policyHolderName || '',
    policy_number: dueDateReportPolicyInfo?.policyNumber || '',
    team: team ? 'true' : 'false',
  });

  const dueDateReportPayload = team
    ? DUE_DATE_REPORT_POST_BODY
    : DUE_DATE_REPORT_POST_BODY.filter(
        item => item.path !== 'agentCode' && item.path !== 'agentName',
      );
  const payload = {
    requestData: {
      headers: dueDateReportPayload,
    },
  };

  const timeStamp = format(new Date(), 'MM-dd-yyyy-hh:mm:ss');
  const fileName = `DueDateReport-${timeStamp}.csv`;
  const fileUri = `${FileSystem.documentDirectory}${fileName}`;

  try {
    const response = await cubeClient.postBinary(
      `${ENDPOINT}?${params.toString()}`,
      payload,
    );
    const arrayBuffer = response.data as ArrayBuffer;
    const base64String = arrayBufferToBase64(arrayBuffer);

    await FileSystem.writeAsStringAsync(fileUri, base64String, {
      encoding: FileSystem.EncodingType.Base64,
    });

    await Sharing.shareAsync(fileUri);
    return { fileUri, message: 'File successfully shared!' };
  } catch (error) {
    console.error('Error downloading or sharing the file:', error);
    throw new Error('Failed to download or share the file');
  }
}

// applicationNotProceedReport
export async function postApplicationNotProceedReport({
  agentId,
  from,
  to,
  policyInfo,
  team,
}: {
  agentId?: string;
  from: string;
  to: string;
  policyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
}) {
  const ENDPOINT = '/exp/api/report/application-not-proceed-report/export';
  const params = new URLSearchParams({
    agent_id: agentId || '',
    date_from: from,
    date_to: to,
    policy_owner_name: policyInfo?.policyHolderName || '',
    policy_number: policyInfo?.policyNumber || '',
    team: team ? 'true' : 'false',
  });

  const applicationNotProceedReportPayload = team
    ? APPNOTPROCEED_REPORT_POST_BODY
    : APPNOTPROCEED_REPORT_POST_BODY.filter(
        item => item.path !== 'agentCode' && item.path !== 'agentName',
      );
  const payload = {
    requestData: {
      headers: applicationNotProceedReportPayload,
    },
  };

  const timeStamp = format(new Date(), 'MM-dd-yyyy-hh:mm:ss');
  const fileName = `ApplicationNotProceedReport-${timeStamp}.csv`;
  const fileUri = `${FileSystem.documentDirectory}${fileName}`;

  try {
    const response = await cubeClient.postBinary(
      `${ENDPOINT}?${params.toString()}`,
      payload,
    );

    const arrayBuffer = response.data as ArrayBuffer;
    const base64String = arrayBufferToBase64(arrayBuffer);

    await FileSystem.writeAsStringAsync(fileUri, base64String, {
      encoding: FileSystem.EncodingType.Base64,
    });

    await Sharing.shareAsync(fileUri);
    return { fileUri, message: 'File successfully shared!' };
  } catch (error) {
    console.error('Error downloading or sharing the file:', error);
    throw new Error('Failed to download or share the file');
  }
}

// postInquiriesReport
export async function postInquiriesReport({
  agentId,
  from,
  to,
  dueDateReportPolicyInfo,
  team,
  status,
}: {
  agentId?: string;
  from: string;
  to: string;
  dueDateReportPolicyInfo?: {
    policyHolderName: string;
    policyNumber: string;
  };
  team?: boolean;
  status: { code: string; meaning: string }[];
}) {
  const ENDPOINT = '/exp/api/report/policy-report/export';
  const params = new URLSearchParams({
    agent_id: agentId || '',
    date_from: from,
    date_to: to,
    policy_owner_name: dueDateReportPolicyInfo?.policyHolderName || '',
    policy_number: dueDateReportPolicyInfo?.policyNumber || '',
    team: team ? 'true' : 'false',
    policy_status: status && status.map(item => item.code).join(','),
  });

  const inquiriesReportPayload = team
    ? INQUIRIES_REPORT__POST_BODY
    : INQUIRIES_REPORT__POST_BODY.filter(
        item => item.path !== 'agentCode' && item.path !== 'agentName',
      );
  const payload = {
    requestData: {
      headers: inquiriesReportPayload,
    },
  };

  const timeStamp = format(new Date(), 'MM-dd-yyyy-hh:mm:ss');
  const fileName = `InquiriesReport-${timeStamp}.csv`;
  const fileUri = `${FileSystem.documentDirectory}${fileName}`;

  try {
    const response = await cubeClient.postBinary(
      `${ENDPOINT}?${params.toString()}`,
      payload,
    );

    const arrayBuffer = response.data as ArrayBuffer;
    const base64String = arrayBufferToBase64(arrayBuffer);

    await FileSystem.writeAsStringAsync(fileUri, base64String, {
      encoding: FileSystem.EncodingType.Base64,
    });

    await Sharing.shareAsync(fileUri);
    return { fileUri, message: 'File successfully shared!' };
  } catch (error) {
    console.error('Error downloading or sharing the file:', error);
    throw new Error('Failed to download or share the file');
  }
}
