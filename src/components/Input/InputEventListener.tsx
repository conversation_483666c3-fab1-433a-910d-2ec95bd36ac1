import React, {
  forwardRef,
  useContext,
  useImperativeHandle,
  useMemo,
} from 'react';
import { UseFormSetValue } from 'react-hook-form';

type EventCallback =
  | ((name?: string) => void)
  | ((name?: string) => Promise<void>);

type InputEventListenerProps = {
  onBlur?: EventCallback;
  onFocus?: EventCallback;
  onDropdownPicked?: EventCallback;
  setFormValue?: UseFormSetValue<any>;
  children?: React.ReactNode;
};

type InputEventListenerContextType = {
  onBlur?: EventCallback;
  onFocus?: EventCallback;
  onDropdownPicked?: EventCallback;
  setFormValue?: UseFormSetValue<any>;
};

const InputEventListenerContext =
  React.createContext<InputEventListenerContextType>({});

const InputEventListener = forwardRef(
  (
    { children, ...props }: InputEventListenerProps,
    ref: React.ForwardedRef<unknown>, // ref is not consumed by radio group, passing ref to remove warning
  ) => {
    const context = useMemo(() => props, [props]);
    useImperativeHandle(ref, () => null);

    return (
      <InputEventListenerContext.Provider value={context}>
        {children}
      </InputEventListenerContext.Provider>
    );
  },
);

const useInputEventListenerContext = () =>
  useContext(InputEventListenerContext);

export default InputEventListener;
export { EventCallback, useInputEventListenerContext };
