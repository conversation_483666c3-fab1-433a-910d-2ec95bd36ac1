import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { LayoutChangeEvent, StyleSheet, View } from 'react-native';

import { useTheme } from '@emotion/react';
import {
  Blur,
  Canvas,
  Group,
  Mask,
  RoundedRect,
  SweepGradient,
  vec,
} from '@shopify/react-native-skia';
import {
  Easing,
  useDerivedValue,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';

interface GlowBorderBoxIndicatorProps {
  glowColor?: string;
  glowSize?: number; // percentage of sweep (e.g. 0.3 for 30%)
  blurRadius?: number;
  borderRadius?: number;
}

const GlowBorderBoxIndicator = (props: GlowBorderBoxIndicatorProps) => {
  const { colors, space } = useTheme();
  const rotation = useSharedValue(0);
  const [width, setWidth] = useState(0);
  const [height, setHeight] = useState(0);

  const glowColorHex = props.glowColor || colors.palette.fwdOrange[100];
  const blurRadius = props.blurRadius || space[4];
  const borderRadius = props.borderRadius || space[1];
  const glowSize = props.glowSize || 0.3; // Default to 30% of the sweep

  const onLayout = useCallback((e: LayoutChangeEvent) => {
    const { width, height } = e.nativeEvent.layout;
    setWidth(width);
    setHeight(height);
  }, []);

  const centerVec = useMemo(() => vec(width / 2, height / 2), [width, height]);

  const animatedRotation = useDerivedValue(() => {
    return [{ rotate: Math.PI * rotation.value }];
  }, [rotation]);

  const GlowGradient = () => (
    <RoundedRect r={borderRadius} x={0} y={0} width={width} height={height}>
      <SweepGradient
        origin={centerVec}
        c={centerVec}
        colors={[
          `${glowColorHex}00`,
          glowColorHex,
          glowColorHex,
          `${glowColorHex}00`,
        ]}
        start={0}
        end={360 * glowSize}
        transform={animatedRotation}
      />
    </RoundedRect>
  );

  useEffect(() => {
    rotation.value = withRepeat(
      withTiming(2, {
        duration: 3000,
        easing: Easing.linear,
      }),
      -1,
      false,
    );
  }, []);

  return (
    <View onLayout={onLayout} style={StyleSheet.absoluteFill}>
      {!!(width && height) && (
        <Canvas style={{ flex: 1 }}>
          <Mask
            mode="luminance"
            mask={
              <Group>
                <RoundedRect
                  x={0}
                  y={0}
                  width={width}
                  height={height}
                  r={borderRadius}
                  color="white" // Base rectangle for masking
                />
                <RoundedRect
                  x={space[1]}
                  y={space[1]}
                  width={width - space[2]}
                  height={height - space[2]}
                  r={borderRadius}
                  color="black" // Inner rectangle to create the border effect
                />
              </Group>
            }>
            <Group>
              <GlowGradient />
              <Blur blur={blurRadius} />
            </Group>

            <GlowGradient />
          </Mask>
        </Canvas>
      )}
    </View>
  );
};

export default GlowBorderBoxIndicator;
