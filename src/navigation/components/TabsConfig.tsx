import { MainTab, OtherMenuStackParamList, OtherMenuTabParamList } from 'types';
import React from 'react';
import { tabIconUtil } from './tabIconUtil';
import HomeScreen from 'screens/HomeScreen/HomeScreen';
import LeadScreen from 'screens/LeadScreen';
import PerformanceScreen from 'screens/PerformanceScreen';
import AgentAssistScreen from 'screens/AgentAssist';
import ProposalScreen from 'screens/ProposalScreen';
import PoliciesScreen from 'screens/PoliciesScreen';
import TeamManagementScreen from 'screens/TeamManagementScreen';
import ReportGenerationScreen from 'screens/ReportGenerationScreen';
import { Icon, PictogramIcon, SvgIconProps } from 'cube-ui-components';
import DocumentsScreen from 'screens/DocumentsScreen/DocumentsScreen';
import ERecruitScreen from 'screens/ERecruitScreen';
import AiBotScreen from 'screens/AiBotScreen';
import ChatBotTabIcon from 'features/aiBot/components/Common/TabIcon';
import AffiliateScreen from 'screens/AffiliateScreen';
import { country } from 'utils/context';
import ECoachHomePage from 'features/ecoach/screens/home/<USER>';
import { useEcoachEnable } from 'features/aiBot/utils/useEcoachEnable';
import { useCheckIsAiBotEnabled } from 'features/aiBot/utils/useCheckIsAiBotEnabled';
import { colors, sizes } from 'cube-ui-components/dist/cjs/theme/base';
import { CareSvg } from 'features/agentAssist/assets/CareSvg';
import SocialMarketingScreen from 'screens/SocialMarketingScreen/SocialMarketingScreen';
import { useSocialMarketingEnable } from 'features/socialMarketing/hooks/useFeatureFlag';

export type CubeUIComponentIconProps = (props: SvgIconProps) => JSX.Element;

// TODO_Alex: to fix with the navigation
const PlaceHolderComponent = () => <></>;

type OtherMenuTabBase = Omit<MainTab, 'name'> & {
  navigationType?: 'tab' | 'stack';
  isNew?: boolean;
};

export type OtherMenuTab = OtherMenuTabBase &
  (
    | {
        name: keyof OtherMenuStackParamList;
        navigationType: 'stack';
      }
    | {
        name: keyof OtherMenuTabParamList;
        navigationType: 'tab';
      }
  );

type CombinedTab = MainTab | OtherMenuTab;

export const useGetTabsMobile = (): {
  combinedTabs: CombinedTab[];
  mainTabs: MainTab[];
  otherMenuTabs: OtherMenuTab[];
} => {
  const isAiBotEnabled = useCheckIsAiBotEnabled();
  const isTrainerAiBotEnabled = useEcoachEnable();
  const isSocialMarketingEnabled = useSocialMarketingEnable();

  const mainTabs = [
    HomeTab,
    LeadTab,
    // ===============
    ...(isAiBotEnabled
      ? country === 'id'
        ? [AiBotTab, ERecruitTab]
        : [AiBotTab, ProposalsTab]
      : country === 'id'
      ? [ERecruitTab, PoliciesTab]
      : [ProposalsTab, PoliciesTab]),
    // ===============
    OthersTab,
  ];

  // Other menu tabs logic
  const initialOtherMenuTabs = [
    ...(country === 'id'
      ? [
          {
            name: 'Proposals',
            component: ProposalScreen,
            focusedIcon: othersTabIconMapping.Proposals.focused,
            icon: othersTabIconMapping.Proposals.stale,
            showHeader: false,
            feature: 'si',
            navigationType: 'tab',
          } satisfies OtherMenuTab,
        ]
      : []),
    // {
    //   name: 'Customers',
    //   component: HomeScreen,
    //   focusedIcon: othersTabIconMapping.Customers.focused,
    //   icon: othersTabIconMapping.Customers.stale,
    //   showHeader: false,
    // },

    ...(isAiBotEnabled
      ? [
          // ensure icon can be displayed when component is mounted
          {
            name: 'Policies',
            component: PoliciesScreen,
            focusedIcon: othersTabIconMapping?.Policies.focused,
            icon: othersTabIconMapping?.Policies.stale,
            showHeader: false,
            feature: 'policy',
            navigationType: 'tab',
          } satisfies OtherMenuTab,
        ]
      : []),
    // TODO: CHLOE - CUBEFIB-4451 - keith requested to hide 'performance' temporarily on ib mobile only
    // {
    //   name: 'Performance',
    //   component: PerformanceScreen,
    //   focusedIcon: othersTabIconMapping.Performance.focused,
    //   icon: othersTabIconMapping.Performance.stale,
    //   showHeader: false,
    //   feature: 'performance',
    // } satisfies OtherMenuTab,
    {
      name: 'Performance',
      component: PerformanceScreen,
      focusedIcon: othersTabIconMapping.Performance.focused,
      icon: othersTabIconMapping.Performance.stale,
      showHeader: false,
      navigationType: 'tab',
      feature: 'performance',
    } satisfies OtherMenuTab,
    ...(country === 'ph' || country === 'ib' || country === 'my'
      ? [
          {
            name: 'ERecruit',
            component: ERecruitScreen,
            focusedIcon: othersTabIconMapping.ERecruit.focused,
            icon: othersTabIconMapping.ERecruit.stale,
            showHeader: false,
            navigationType: 'tab',
            feature: 'eRecruit',
          } satisfies OtherMenuTab,
        ]
      : []),
    {
      name: 'Affiliate',
      component: AffiliateScreen,
      focusedIcon: othersTabIconMapping.Affiliate.focused,
      icon: othersTabIconMapping.Affiliate.stale,
      showHeader: true,
      navigationType: 'tab',
      feature: 'affiliate',
    } satisfies OtherMenuTab,
    {
      name: 'ReportGeneration',
      component: ReportGenerationScreen,
      focusedIcon: othersTabIconMapping.ReportGeneration.focused,
      icon: othersTabIconMapping.ReportGeneration.stale,
      showHeader: false,
      navigationType: 'tab',
    } satisfies OtherMenuTab,
    {
      name: 'Document',
      component: DocumentsScreen,
      focusedIcon: othersTabIconMapping.Document.focused,
      icon: othersTabIconMapping.Document.stale,
      showHeader: false,
      navigationType: 'tab',
      // headerTitle: 'Policy tracking',
    } satisfies OtherMenuTab,
    {
      name: 'TeamManagement',
      component: TeamManagementScreen,
      focusedIcon: othersTabIconMapping.TeamManagement.focused,
      icon: othersTabIconMapping.TeamManagement.stale,
      showHeader: false,
      navigationType: 'tab',
      feature: 'team',
      // headerTitle: 'Policy tracking',
    } satisfies OtherMenuTab,
    ...(isTrainerAiBotEnabled
      ? [
          {
            name: 'TrainerAiBot',
            component: ECoachHomePage,
            focusedIcon: othersTabIconMapping.TrainerAiBot.focused,
            icon: othersTabIconMapping.TrainerAiBot.stale,
            showHeader: false,
            navigationType: 'tab',
          } satisfies OtherMenuTab,
        ]
      : []),
    ...(isSocialMarketingEnabled
      ? [
          {
            name: 'SocialMarketing',
            component: SocialMarketingScreen,
            focusedIcon: othersTabIconMapping.SocialMarketing.focused,
            icon: othersTabIconMapping.SocialMarketing.stale,
            showHeader: false,
            navigationType: 'stack',
            // TODO: Add feature flag when ready
            // feature: 'socialMarketing',
            isNew: true,
          } satisfies OtherMenuTab,
        ]
      : []),
    {
      name: 'AgentAssist',
      component: AgentAssistScreen,
      focusedIcon: othersTabIconMapping.AgentAssist.focused,
      icon: othersTabIconMapping.AgentAssist.stale,
      showHeader: false,
      navigationType: 'tab',
      feature: 'agentAssist',
    } satisfies OtherMenuTab,
  ];

  const setOfMainTabNames = new Set(mainTabs.map(tab => tab.name));

  // prevent duplicate tabs
  const otherMenuTabs = initialOtherMenuTabs.filter(
    tab => !setOfMainTabNames.has(tab.name),
  );

  // Combine main tabs and other menu tabs, keeping main tabs at the top
  const combinedTabs = [...mainTabs, ...otherMenuTabs];

  return {
    combinedTabs,
    mainTabs,
    otherMenuTabs,
  };
};

const HomeTab: MainTab = {
  name: 'Home',
  component: HomeScreen,
  focusedIcon: tabIconUtil({ tab: 'Home', focused: true }),
  icon: tabIconUtil({ tab: 'Home', focused: false }),
  showHeader: false,
};

const LeadTab: MainTab = {
  name: 'Lead',
  component: LeadScreen,
  focusedIcon: tabIconUtil({ tab: 'Lead', focused: true }),
  icon: tabIconUtil({ tab: 'Lead', focused: false }),
  showHeader: false,
  feature: 'lead',
};

const ProposalsTab: MainTab = {
  name: 'Proposals',
  //! temp fix
  component: ProposalScreen,
  focusedIcon: tabIconUtil({ tab: 'Proposals', focused: true }),
  icon: tabIconUtil({ tab: 'Proposals', focused: false }),
  showHeader: false,
  feature: 'si',
};

const AiBotTab: MainTab = {
  name: 'AiBot',
  component: AiBotScreen,
  focusedIcon: ChatBotTabIcon,
  icon: ChatBotTabIcon,
  onPress: (_, navigation) => {
    navigation && navigation.navigate('AiBotChat');
  },
  showHeader: false,
  hideTitle: true,
};

const PoliciesTab: MainTab = {
  name: 'Policies',
  component: PoliciesScreen,
  focusedIcon: tabIconUtil({ tab: 'Policies', focused: true }),
  icon: tabIconUtil({ tab: 'Policies', focused: false }),
  showHeader: false,
  feature: 'policy',
};

const OthersTab: MainTab = {
  name: 'Others',
  // TODO to fix
  component: PlaceHolderComponent,
  focusedIcon: tabIconUtil({ tab: 'Others', focused: true, isClose: true }),
  icon: tabIconUtil({ tab: 'Others', focused: false }),
  showHeader: false,
};

const othersTabIconMapping: Record<
  | keyof OtherMenuTabParamList
  | keyof Pick<OtherMenuStackParamList, 'SocialMarketing'>,
  { focused: JSX.Element; stale: JSX.Element }
> = {
  Customers: {
    focused: <Icon.Team fill={colors.fwdOrange[100]} size={sizes[7]} />,
    stale: <Icon.Team fill={colors.white} size={sizes[7]} />,
  },
  Performance: {
    focused: (
      <Icon.InvestorInformation fill={colors.fwdOrange[100]} size={sizes[7]} />
    ),

    stale: <Icon.InvestorInformation fill={colors.white} size={sizes[7]} />,
  },
  ReportGeneration: {
    focused: (
      <Icon.SearchForDocument fill={colors.fwdOrange[100]} size={sizes[7]} />
    ),
    stale: <Icon.SearchForDocument fill={colors.white} size={sizes[7]} />,
  },
  Document: {
    focused: <Icon.DocumentCopy fill={colors.fwdOrange[100]} size={sizes[7]} />,
    stale: <Icon.DocumentCopy fill={colors.white} size={sizes[7]} />,
  },
  TeamManagement: {
    focused: <Icon.ChartBar fill={colors.fwdOrange[100]} size={sizes[7]} />,
    stale: <Icon.ChartBar fill={colors.white} size={sizes[7]} />,
  },
  ERecruit: {
    focused: <Icon.Career fill={colors.fwdOrange[100]} size={sizes[7]} />,
    stale: <Icon.Career fill={colors.white} size={sizes[7]} />,
  },
  Policies: {
    focused: (
      <Icon.DocumentCertified fill={colors.fwdOrange[100]} size={sizes[7]} />
    ),
    stale: <Icon.DocumentCertified fill={colors.white} size={sizes[7]} />,
  },
  TrainerAiBot: {
    focused: (
      <Icon.CorporateGovernance fill={colors.fwdOrange[100]} size={sizes[7]} />
    ),
    stale: <Icon.CorporateGovernance fill={colors.white} size={sizes[7]} />,
  },
  Affiliate: {
    focused: <Icon.Teams fill={colors.fwdOrange[100]} size={sizes[7]} />,
    stale: <Icon.Teams fill={colors.white} size={sizes[7]} />,
  },
  AgentAssist: {
    focused: <CareSvg fill={colors.fwdOrange[100]} size={sizes[7]} />,
    stale: <CareSvg fill={colors.white} size={sizes[7]} />,
  },
  Proposals: {
    focused: <Icon.Document fill={colors.fwdOrange[100]} size={sizes[7]} />,
    stale: <Icon.Document fill={colors.white} size={sizes[7]} />,
  },
  AgentPolicies: {
    // TBC
    focused: <></>,
    stale: <></>, // TBC
  },
  SocialMarketing: {
    focused: <Icon.MagicWand fill={colors.fwdOrange[100]} size={sizes[7]} />,
    stale: <Icon.MagicWand fill={colors.white} size={sizes[7]} />,
  },
};

const ERecruitTab: MainTab = {
  name: 'ERecruit',
  //! temp fix
  component: ERecruitScreen,
  focusedIcon: tabIconUtil({ tab: 'ERecruit', focused: true }),
  icon: tabIconUtil({ tab: 'ERecruit', focused: false }),
  showHeader: false,
  feature: 'eRecruit',
};
