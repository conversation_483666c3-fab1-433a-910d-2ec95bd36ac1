import { useTheme } from '@emotion/react';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import useBoundStore from 'hooks/useBoundStore';
import React, { useEffect, useRef, useState } from 'react';
import { AppState, PanResponder, View } from 'react-native';
import useCheckIsLoggedIn from 'hooks/useCheckIsLoggedIn';

// * Types
// import { RootStackParamList, RootStackParamListMap } from 'types/navigation';
import { RootStackParamListMap } from 'types/navigation';
import { TeamPerformanceViewType } from 'types/team';

//* Screens
import HealthQuestionsReview from 'features/eAppV2/common/components/review/healthQuestions/HealthQuestionsReview';
import PaymentGatewayTablet from 'features/eAppV2/ph/components/payment/paymentGateway/PaymentGateway';
import PaymentGateway from 'features/eAppV2/ph/components/payment/paymentGateway/PaymentGateway';
import PaymentProcessingTablet from 'features/eAppV2/ph/components/payment/paymentProcessing/PaymentProcessing.tablet';
import PaymentSuccessfulResultTablet from 'features/eAppV2/ph/components/payment/paymentSuccessfulResult/PaymentSuccessfulResult.tablet';
import DataPrivacyReview from 'features/eAppV2/ph/components/review/dataPrivacy/DataPrivacyReview';
import PolicyReplacementReview from 'features/eAppV2/ph/components/review/policyReplacement/PolicyReplacementReview';
import ReviewCandidateApplicationScreen from 'features/eRecruit/ph/ReviewCandidateApplicationScreen';
import CandidatesSearchScreen from 'features/eRecruit/ph/phone/CandidatesSearchScreen';
import GYBAttendeesScreen from 'features/eRecruit/ph/phone/GYBAttendeesScreen';
import PolicyDetailScreen from 'features/policy/components/PolicyDetails';
import FundIllustrationForm from 'features/proposal/components/FundIllustrationForm/FundIllustrationForm';
import CustomHeaderBackButton from 'navigation/components/HeaderBackButton';
import ACRScreen from 'screens/ACRScreen/ACRScreen';
import AgentPerformanceDetailsScreen from 'screens/AgentPerformanceDetailsScreen';
import AgentProfileScreen from 'screens/AgentProfileScreen/AgentProfileScreen';
import PersonalDetailsScreen from 'screens/AgentProfileScreen/PersonalDetailsScreen';
import SettingScreen from 'screens/AgentProfileScreen/SettingScreen';
import CoverageDetailsScreen from 'screens/CoverageDetailsScreen';
import DocumentsScreen from 'screens/DocumentsScreen/DocumentsScreen';
import EAppScreen from 'screens/EAppScreen';
import ERecruitCandidateListScreen from 'screens/ERecruitScreen/ERecruitCandidateListScreen';
import ERecruitCandidateProfileScreen from 'screens/ERecruitScreen/ERecruitCandidateProfileScreen';
import ERecruitMaterialDetailsScreen from 'screens/ERecruitScreen/ERecruitMaterialDetailsScreen';
import ERecruitMaterialsScreen from 'screens/ERecruitScreen/ERecruitMaterialsScreen';
import LoginScreen from 'screens/LoginScreen';
import PdfViewerScreen from 'screens/PdfViewerScreen';
import PoliciesScreen from 'screens/PoliciesScreen/PoliciesScreen';
import ProductRecommendationScreen from 'screens/ProductRecommendationScreen';
import ProductSelection from 'screens/ProductSelection';
import ProposalTableScreen from 'screens/ProposalTableScreen';
import CreditCardExpirationReportScreen from 'screens/ReportGenerationScreen/CreditCardExpirationReportScreen';
import LapsedPoliciesReportScreen from 'screens/ReportGenerationScreen/LapsedPoliciesReportScreen';
import PolicyAnniversaryListScreen from 'screens/ReportGenerationScreen/PolicyAnniversaryListScreen';
import PremiumReceivedReportScreen from 'screens/ReportGenerationScreen/PremiumReceivedReportScreen';
import ReportGenerationListScreen from 'screens/ReportGenerationScreen/ReportGenerationListScreen';
import UnsuccessfulAdaAcaScreen from 'screens/ReportGenerationScreen/UnsuccessfulAdaAcaScreen';
import RPQQuestionFormScreen from 'screens/RpqQuestionFormScreen';
import RpqResultScreen from 'screens/RpqResultScreen';
import SalesIllustrationForm from 'screens/SalesIllustrationForm';
import TeamOperationScreen from 'screens/TeamOperationScreen';
import TeamPerformanceDetailsScreen from 'screens/TeamPerformanceDetailsScreen';
import TeamPerformanceListScreen from 'screens/TeamPerformanceList';
import TeamTargetScreen from 'screens/TeamTargetScreen';

// Ai Bot
import AiBotFeedbackScreen from 'screens/AiBotScreen/AiBotFeedbackScreen';
import AiBotHistoryScreen from 'screens/AiBotScreen/AiBotHistoryScreen';

//* ScreenLots
import FnaScreen from 'screens/FnaScreen';
import SellerExpScreens from 'screens/SellerExperienceScreens';
import { SimulationTableScreen } from 'screens/SimulationTableScreen';
import TeamMemberTargetsEditScreen from 'screens/TeamTargetEditScreen';

// * Context
import { default as ReportGenerationToolbarProvider } from 'features/reportGeneration/ph/ToolbarProvider';

//* Navigator
import PersonalInformationReview from 'features/eAppV2/common/components/review/personalInformationReview/PersonalInformationReview';
import DetailSummaryPage from 'features/ecoach/screens/detailSummary/DetailSummary';
import DetailSummaryTabletPage from 'features/ecoach/screens/detailSummary/DetailSummary.tablet';
import GuideLinesPage from 'features/ecoach/screens/guideLines/GuideLinesPage';
import OverallFeedback from 'features/ecoach/screens/OverallFeedback';
import SelectDifficultyPage from 'features/ecoach/screens/SelectDifficulty';
import SelectPolicyPage from 'features/ecoach/screens/selectPolicy/SelectPolicy';
import SessionHistory from 'features/ecoach/screens/SessionHistory';
import SplashPage from 'features/ecoach/screens/Splash';
import SummaryPage from 'features/ecoach/screens/summary/Summary';
import SummaryTabletPage from 'features/ecoach/screens/summary/Summary.tablet';
import UserProfilePage from 'features/ecoach/screens/UserProfile';
import VideoCallPage from 'features/ecoach/screens/call/VideoCallPage';
import WatchVideoPage from 'features/ecoach/screens/WatchVideoPage';
import ECoachHomePage from 'features/ecoach/screens/home/<USER>';
import ProtectionGoal from 'features/fna/components/goals/protection/ProtectionGoal';
import SavingsGoal from 'features/fna/components/goals/savings/SavingsGoal';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useForceLogout } from 'hooks/useForceLogout';
import MainNavigator from 'navigation/MainNavigator/MainNavigator';
import AgentAssistDetailScreen from 'screens/AgentAssitDetail';
import AiBotScreen from 'screens/AiBotScreen';
import AiBotPromptLibraryScreen from 'screens/AiBotScreen/AiBotPromptLibraryScreen';
import AiBotTableScreen from 'screens/AiBotScreen/AiBotTableScreen';
import BirthdayCardScreen from 'screens/BirthdayCardScreen';
import CustomerFactFindScreen from 'screens/CustomerFactFindScreen';
import NotificationScreen from 'screens/NotificationScreen';
import { OcrScreen } from 'screens/OcrScreen';
import StartClaimScreen from 'screens/StartClaim';
import { BirthdayTasksScreen } from 'screens/TasksScreen';
import PaymentProcessing from 'features/eAppV2/ph/components/payment/paymentProcessing/PaymentProcessing';
import PaymentSuccessfulResult from 'features/eAppV2/ph/components/payment/paymentSuccessfulResult/PaymentSuccessfulResult';
import PersonalInfoReview from 'features/eAppV2/ph/components/review/personalInfo/PersonalInfoReview';
import EntityInfoReview from 'features/eAppV2/ph/components/review/entityInfo/EntityInfoReview';
import RiskProfileReview from 'features/eApp/components/phone/review/riskProfile/RiskProfileReview';
import ImageListScreen from 'screens/ImageListScreen';
import AppointmentSummary from 'features/ecoach/screens/summary/AppointmentSummary';
import AppointmentSummaryTablet from 'features/ecoach/screens/summary/AppointmentSummary.tablet';
import { useHasPermission } from 'hooks/useCheckClientScope';
import { useGetTabsMobile } from 'navigation/components/TabsConfig';
import { SocialMarketingCreateNewScreen } from 'screens/SocialMarketingCreateNewScreen';
import SocialMarketingPostCreationReviewScreen from 'screens/SocialMarketingPostCreationReviewScreen';

const Stack = createNativeStackNavigator<RootStackParamListMap['ph']>();
// const Stack = createNativeStackNavigator<RootStackParamList>();

export default function PhStackNavigator() {
  const { typography } = useTheme();
  const { isTabletMode } = useLayoutAdoptionCheck();

  const refreshToken = useBoundStore(state => state.auth.authInfo.refreshToken);
  const { forceLogout, shouldTriggerLogout } = useForceLogout();

  const { isLoggedIn } = useCheckIsLoggedIn();
  const hasPermission = useHasPermission();

  /**
   * CUBEPH-2472
   * Auto logoff the session of an agent/FSC if:
   * 1. he/she is idle / does not have any activity / put the device on sleep mode in his/her device for 30 minutes.
   * 2. he/she leaves/closes the application.
   */

  const timerId = useRef<ReturnType<any>>(false);
  const intervalLogoutTime = useRef<ReturnType<typeof setInterval>>();

  const timeForInactivityInSecond = 1500; // 25 minute inactivity
  const logoutTimeInSecond = 300; // 5 minute count down

  const [isIdle, setIsIdle] = useState(false);
  const [logoutTime, setLogoutTime] = useState(logoutTimeInSecond);

  const countDownLogoutTime = () => setLogoutTime(prev => prev - 1);

  const resetInactivityTimeout = () => {
    setLogoutTime(logoutTimeInSecond);
    clearTimeout(timerId.current);
    timerId.current = setTimeout(() => {
      setIsIdle(true); // Set isIdle === true after 25 minutes
    }, timeForInactivityInSecond * 1000);
  };

  const otherMenuTabsMobile = useGetTabsMobile().otherMenuTabs.filter(item => {
    if (!item.feature) {
      return true;
    }
    return hasPermission(item.feature);
  });

  const handleLogout = async () => {
    await forceLogout().finally(() => {
      setIsIdle(false);
    });
  };

  useEffect(() => {
    resetInactivityTimeout();
  }, []);

  useEffect(() => {
    if (isIdle) {
      intervalLogoutTime.current = setInterval(countDownLogoutTime, 1000); // Start counting down logoutTime when isIdle === true
      return () => clearInterval(intervalLogoutTime.current);
    }
  }, [isIdle]);

  useEffect(() => {
    // Logout when logoutTime reaches 0
    if (logoutTime <= 0) {
      setLogoutTime(logoutTimeInSecond);
      handleLogout();
    }
  }, [logoutTime]);

  // App state change
  const [appState, setAppState] = useState(AppState.currentState);

  const handleAppStateChange = (nextAppState: any) => {
    if (appState.match(/inactive|background/) && nextAppState === 'active') {
      console.log('App has come to the foreground!');
    } else if (nextAppState === 'background') {
      console.log('App has gone to the background!');
      // handleLogout(); // Logout when app goes to background
    }
    setAppState(nextAppState);
  };

  useEffect(() => {
    const sub = AppState.addEventListener('change', handleAppStateChange);
    return () => {
      sub.remove();
    };
  }, []);

  // Pan responder
  const panResponder = React.useRef(
    PanResponder.create({
      onStartShouldSetPanResponderCapture: () => {
        setIsIdle(false);
        resetInactivityTimeout();
        return false;
      },
    }),
  ).current;

  return (
    <ReportGenerationToolbarProvider>
      <View style={{ flex: 1 }} {...panResponder.panHandlers}>
        <Stack.Navigator
          screenOptions={{
            headerShown: false,
            animation: 'slide_from_right',
            headerTitleAlign: 'center',
          }}>
          {isLoggedIn ? (
            // * Authenticated Navigator
            <>
              <Stack.Screen name="Main">{() => <MainNavigator />}</Stack.Screen>
              <Stack.Screen
                name="NotificationScreen"
                component={NotificationScreen}
                options={{
                  headerShown: false,
                }}
              />
              <Stack.Group>
                <Stack.Screen
                  name="FWDNews"
                  component={SellerExpScreens.FWDNewsScreen}
                  options={{
                    headerShown: false,
                    animation: 'fade',
                  }}
                />
                <Stack.Screen
                  name="FWDNewsDetails"
                  component={SellerExpScreens.FWDNewsDetailsScreen}
                  options={{
                    headerShown: false,
                    animation: 'fade',
                  }}
                />
                <Stack.Screen
                  name="FWDNewsBookmarks"
                  component={SellerExpScreens.FWDNewsBookmarkScreen}
                  options={{
                    headerShown: false,
                    animation: 'fade',
                  }}
                />
              </Stack.Group>

              {/* // Task Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="BirthdayTasksScreen"
                  component={BirthdayTasksScreen}
                  options={{
                    headerShown: false,
                  }}
                />
              </Stack.Group>

              {/* // Birthday Card Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="BirthdayCardScreen"
                  component={BirthdayCardScreen}
                  options={{
                    headerShown: false,
                  }}
                />
              </Stack.Group>

              {/* // Lead Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="LeadProfile"
                  component={SellerExpScreens.LeadProfileScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ProfileDetails"
                  component={SellerExpScreens.LeadProfileDetailsScreen}
                  options={{
                    animation: 'fade',
                  }}
                />
                <Stack.Screen
                  name="CustomerProfileDetails"
                  component={SellerExpScreens.CustomerProfileDetailsScreen}
                  options={{
                    animation: 'fade',
                  }}
                />
                <Stack.Screen
                  name="AddNewLeadOrEntity"
                  component={SellerExpScreens.AddNewLeadOrEntityScreen}
                  options={{
                    animation: 'fade',
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ContactBook"
                  component={SellerExpScreens.ContactBookScreen}
                  options={{
                    animation: 'fade',
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="LeadAndCustomerSearch"
                  component={SellerExpScreens.LeadAndCustomerSearch}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="LogActivity"
                  component={SellerExpScreens.LogActivityScreen}
                  options={{
                    animation: 'fade',
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ExistingPolicyDetail"
                  component={PolicyDetailScreen}
                />
                <Stack.Screen
                  name="CreateInsured"
                  component={SellerExpScreens.CreateInsuredScreen}
                  options={{
                    animation: 'fade',
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="InsuredDetails"
                  component={SellerExpScreens.InsuredDetailsScreen}
                  options={{
                    animation: 'fade',
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="SearchExistingLead"
                  component={SellerExpScreens.SearchExistingLeadScreen}
                  options={{
                    headerShown: false,
                  }}
                />
              </Stack.Group>

              {/* // Performance Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="RecognitionDetails"
                  component={SellerExpScreens.RecognitionDetailsScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="PerformanceDetails"
                  component={SellerExpScreens.PerformanceDetailsScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="CampaignsDetails"
                  component={SellerExpScreens.CampaignsDetailsScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="SummitClubsDetails"
                  component={SellerExpScreens.SummitClubsScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="EliteAgencyDetails"
                  component={SellerExpScreens.EliteAgencyDetailsScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="EliteAgencyRequirements"
                  component={SellerExpScreens.EliteAgencyRequirementsScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="EliteAgencyBenefits"
                  component={SellerExpScreens.EliteAgencyBenefitsScreen}
                  options={{
                    headerShown: false,
                  }}
                />
              </Stack.Group>

              {/* // FNA Screens */}

              {/* // SI Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="SalesIllustrationForm"
                  component={SalesIllustrationForm}
                />
                <Stack.Screen
                  name="RPQQuestionForm"
                  component={RPQQuestionFormScreen}
                />
                <Stack.Screen name="RPQResult" component={RpqResultScreen} />
                <Stack.Screen
                  name="ProductSelection"
                  component={ProductSelection}
                />
                <Stack.Screen
                  name="CoverageDetailsScreen"
                  component={CoverageDetailsScreen}
                  options={{
                    animation: 'fade',
                  }}
                />
              </Stack.Group>

              {/* // Agent Profile Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="AgentProfile"
                  component={AgentProfileScreen}
                />
                <Stack.Screen
                  name="PersonalDetails"
                  component={PersonalDetailsScreen}
                />
                <Stack.Screen name="Setting" component={SettingScreen} />
              </Stack.Group>

              {/* // Uncategoried */}
              <Stack.Group>
                <Stack.Screen
                  name="BadgesCollection"
                  component={SellerExpScreens.BadgesCollectionScreen}
                />
              </Stack.Group>

              {/* // Ai Bot Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="AiBotChat"
                  component={AiBotScreen}
                  options={{
                    headerShown: false,
                    animation: 'slide_from_bottom',
                  }}
                />
                <Stack.Screen
                  name="AiBotHistory"
                  component={AiBotHistoryScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="AiBotPromptLibrary"
                  component={AiBotPromptLibraryScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="AiBotFeedback"
                  component={AiBotFeedbackScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="AiBotTable"
                  component={AiBotTableScreen}
                  options={{
                    headerShown: false,
                  }}
                />
              </Stack.Group>

              {/* // Ecoach Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="EcoachHome"
                  component={ECoachHomePage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="Splash"
                  component={SplashPage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="UserProfile"
                  component={UserProfilePage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="SelectPolicy"
                  component={SelectPolicyPage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="SelectDifficulty"
                  component={SelectDifficultyPage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="GuideLinesPage"
                  component={GuideLinesPage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="VideoCallPage"
                  component={VideoCallPage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="Summary"
                  component={SummaryPage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="SummaryTablet"
                  component={SummaryTabletPage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="AppointmentSummary"
                  component={AppointmentSummary}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="AppointmentSummaryTablet"
                  component={AppointmentSummaryTablet}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="DetailSummary"
                  component={DetailSummaryPage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="DetailSummaryTablet"
                  component={DetailSummaryTabletPage}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="SessionHistory"
                  component={SessionHistory}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="OverallFeedback"
                  component={OverallFeedback}
                  options={{ headerShown: false }}
                />
                <Stack.Screen
                  name="WatchVideoPage"
                  component={WatchVideoPage}
                  options={{
                    headerShown: true,
                    title: '',
                    headerBackTitle: 'Home',
                  }}
                />
              </Stack.Group>

              {/* // Policies */}
              <Stack.Group>
                <Stack.Screen
                  name="PoliciesNewBusiness"
                  component={SellerExpScreens.PoliciesNewBusinessNavigator}
                />
              </Stack.Group>

              {/* // Pos */}
              <Stack.Group>
                <Stack.Screen
                  name="PoliciesPOS"
                  component={SellerExpScreens.POSNavigator}
                />
              </Stack.Group>

              {/* // Proposal Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="ProposalTable"
                  component={ProposalTableScreen}
                />
              </Stack.Group>
              <Stack.Screen name="EApp" component={EAppScreen} />
              <Stack.Screen name="Ocr" component={OcrScreen} />
              <Stack.Screen name="ACR" component={ACRScreen} />
              <Stack.Screen
                name="SimulationTable"
                component={SimulationTableScreen}
                options={{
                  animation: 'fade',
                  orientation: isTabletMode ? 'landscape' : 'landscape_left',
                }}
              />

              <Stack.Screen
                name="FundIllustrationForm"
                component={FundIllustrationForm}
                options={{
                  animation: 'fade',
                  orientation: isTabletMode ? 'landscape' : 'landscape_left',
                }}
              />

              {/* // App Review Screens */}
              <Stack.Group screenOptions={{ headerShown: false }}>
                <Stack.Screen
                  name="PersonalInfoReview"
                  component={PersonalInfoReview}
                />

                <Stack.Screen
                  name="PersonalInformationReview"
                  component={PersonalInformationReview}
                />
                <Stack.Screen
                  name="EntityInfoReview"
                  component={EntityInfoReview}
                />
                <Stack.Screen
                  name="HealthQuestionsReview"
                  component={HealthQuestionsReview}
                />
                <Stack.Screen
                  name="PolicyReplacementReview"
                  component={PolicyReplacementReview}
                  options={
                    isTabletMode
                      ? {
                          animation: 'slide_from_bottom',
                        }
                      : undefined
                  }
                />
                <Stack.Screen
                  name="DataPrivacyReview"
                  component={DataPrivacyReview}
                  options={
                    isTabletMode
                      ? {
                          animation: 'slide_from_bottom',
                        }
                      : undefined
                  }
                />
                <Stack.Screen
                  name="RiskProfileReview"
                  component={RiskProfileReview}
                />
              </Stack.Group>

              {/* // Pdf Viewer Screen */}
              <Stack.Group>
                <Stack.Screen
                  name="PdfViewer"
                  component={PdfViewerScreen}
                  options={props => {
                    return {
                      animation: props.route.params.screenTransition
                        ? props.route.params.screenTransition
                        : 'slide_from_bottom',
                    };
                  }}
                />
                <Stack.Screen name="ImageList" component={ImageListScreen} />
              </Stack.Group>

              {/* // List Performance Screens */}
              <Stack.Group
                screenOptions={{
                  headerShown: true,
                  headerLeft: () => <CustomHeaderBackButton />,
                }}>
                <Stack.Screen
                  name="TeamListPerformance"
                  component={TeamPerformanceListScreen}
                  options={({ route }) => ({
                    headerTitle:
                      route.params?.viewType ===
                      TeamPerformanceViewType.Individual
                        ? 'View by individual'
                        : 'View by team',
                    headerTitleStyle: {
                      fontFamily: 'FWDCircularTT-Bold',
                      fontSize: typography.h7.size,
                    },
                  })}
                />
                <Stack.Screen
                  options={{ headerShown: false }}
                  name="TeamPerformanceDetails"
                  component={TeamPerformanceListScreen}
                />
              </Stack.Group>

              {/* // Team Operation Data Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="TeamOperation"
                  component={TeamOperationScreen}
                />
                <Stack.Screen name="AgentPolicies" component={PoliciesScreen} />
              </Stack.Group>

              {/* // Team Leads Conversion Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="TeamLeadsConversion"
                  component={SellerExpScreens.LeadsConversionScreen}
                />
              </Stack.Group>

              {/* // Team Target Screens */}
              <Stack.Group>
                <Stack.Screen name="TeamTarget" component={TeamTargetScreen} />
                <Stack.Screen
                  name="TeamMemberTargetsEdit"
                  component={TeamMemberTargetsEditScreen}
                />
              </Stack.Group>

              {/* // Group payment */}
              <Stack.Group screenOptions={{ gestureEnabled: false }}>
                <Stack.Screen
                  name="PaymentResult"
                  component={
                    isTabletMode
                      ? PaymentSuccessfulResultTablet
                      : PaymentSuccessfulResult
                  }
                />
                <Stack.Screen
                  name="PaymentProcessing"
                  component={
                    isTabletMode ? PaymentProcessingTablet : PaymentProcessing
                  }
                />
                <Stack.Screen
                  name="PaymentGateway"
                  component={
                    isTabletMode ? PaymentGatewayTablet : PaymentGateway
                  }
                  options={{
                    headerShown: false,
                    title: 'Payment',
                  }}
                />
              </Stack.Group>

              {/* // Team Performance Details Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="TeamView"
                  component={TeamPerformanceDetailsScreen}
                />
              </Stack.Group>

              {/* // Agent Performance Screens */}
              <Stack.Group>
                <Stack.Screen
                  name="AgentPerformance"
                  component={AgentPerformanceDetailsScreen}
                />
              </Stack.Group>

              {/* // FNA */}
              <Stack.Group>
                <Stack.Screen
                  name="Fna"
                  component={FnaScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="SavingsGoal"
                  component={SavingsGoal}
                  options={{
                    headerShown: false,
                    animation: 'slide_from_right',
                  }}
                />
                <Stack.Screen
                  name="ProtectionGoal"
                  component={ProtectionGoal}
                  options={{
                    headerShown: false,
                    animation: 'slide_from_right',
                  }}
                />
                <Stack.Screen
                  name="ProductRecommendation"
                  component={ProductRecommendationScreen}
                  options={{
                    headerShown: false,
                  }}
                />
              </Stack.Group>

              {/* // Report Screens */}
              <Stack.Group screenOptions={{ gestureEnabled: false }}>
                <Stack.Screen
                  name="ReportGenerationListScreen"
                  component={ReportGenerationListScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="PolicyAnniversaryListScreen"
                  component={PolicyAnniversaryListScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="LapsedPoliciesReportScreen"
                  component={LapsedPoliciesReportScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="PremiumReceivedReportScreen"
                  component={PremiumReceivedReportScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="CreditCardExpirationReportScreen"
                  component={CreditCardExpirationReportScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="UnsuccessfulAdaAcaScreen"
                  component={UnsuccessfulAdaAcaScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="CustomerFactFind"
                  component={CustomerFactFindScreen}
                />
              </Stack.Group>

              {/* // Documents */}
              <Stack.Group>
                <Stack.Screen
                  name="DocumentsScreen"
                  component={DocumentsScreen}
                  options={{
                    headerShown: false,
                  }}
                />
              </Stack.Group>

              {/* Recruitment */}
              <Stack.Group>
                <Stack.Screen
                  name="GYBAttendees"
                  component={GYBAttendeesScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="CandidateProfile"
                  component={ERecruitCandidateProfileScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="ReviewCandidateApplication"
                  component={ReviewCandidateApplicationScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="MaterialDetails"
                  component={ERecruitMaterialDetailsScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="CandidatesSearch"
                  component={CandidatesSearchScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="CandidateList"
                  component={ERecruitCandidateListScreen}
                  options={{
                    headerShown: false,
                  }}
                />
                <Stack.Screen
                  name="Materials"
                  component={ERecruitMaterialsScreen}
                  options={{
                    headerShown: false,
                  }}
                />
              </Stack.Group>

              {/* Assist */}
              <Stack.Group>
                <Stack.Screen
                  name="StartClaim"
                  component={StartClaimScreen}
                  options={{
                    headerShown: false,
                  }}
                />
              </Stack.Group>
              <Stack.Group>
                <Stack.Screen
                  name="agentAssistDetail"
                  component={AgentAssistDetailScreen}
                  options={{
                    headerShown: false,
                  }}
                />
              </Stack.Group>
              <Stack.Screen
                name="SocialMarketingCreateNew"
                component={SocialMarketingCreateNewScreen}
                options={{
                  presentation: 'modal',
                  gestureEnabled: true,
                  gestureDirection: 'vertical',
                  keyboardHandlingEnabled: true,
                  headerShown: false,
                }}
              />

              <Stack.Screen
                name="SocialMarketingPostCreationReview"
                component={SocialMarketingPostCreationReviewScreen}
              />

              {otherMenuTabsMobile
                .filter(menuItem => menuItem.navigationType === 'stack')
                .map(({ name, component }) => (
                  <Stack.Screen
                    key={'otherMenuTabsMobile_' + name}
                    name={name}
                    component={component}
                  />
                ))}
            </>
          ) : (
            // Unauthenticated Screens
            <Stack.Group>
              <Stack.Screen
                name="Login"
                component={LoginScreen}
                options={{
                  animationTypeForReplace: !isLoggedIn ? 'pop' : 'push',
                }}
              />
            </Stack.Group>
          )}
          {/* Common modal screens */}
        </Stack.Navigator>
      </View>
    </ReportGenerationToolbarProvider>
  );
}
