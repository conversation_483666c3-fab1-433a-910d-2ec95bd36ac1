import React from 'react';
import styled from '@emotion/native';
import AddNewLeadForm from 'features/lead/components/AddLeadForm/AddNewLeadInputForm';
import Animated, { LinearTransition } from 'react-native-reanimated';

export default function AddNewLeadScreen() {
  return (
    <AddNewLeadRootContainer layout={LinearTransition}>
      <AddNewLeadForm />
    </AddNewLeadRootContainer>
  );
}

const AddNewLeadRootContainer = styled(Animated.View)(
  ({ theme: { colors } }) => ({
    flex: 1,
    backgroundColor: colors.background,
  }),
);
