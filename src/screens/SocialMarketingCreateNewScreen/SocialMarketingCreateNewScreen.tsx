import SocialMarketingCreateNew from 'features/socialMarketing/components/SocialMarketingCreateNew';
import { useSocialMarketingEnable } from 'features/socialMarketing/hooks/useFeatureFlag';
import React from 'react';
import NotFoundScreen from 'screens/NotFoundScreen';
import { country } from 'utils/context';

export default function SocialMarketingCreateNewScreen() {
  const isSocialMarketingEnabled = useSocialMarketingEnable();

  if (!isSocialMarketingEnabled) {
    return <NotFoundScreen />;
  }

  switch (country) {
    case 'ib':
    case 'id':
    case 'my':
    case 'ph':
      return <SocialMarketingCreateNew />;
    default:
      return <NotFoundScreen />;
  }
}
