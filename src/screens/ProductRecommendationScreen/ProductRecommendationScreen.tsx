import styled from '@emotion/native';
import { useTheme } from '@emotion/react';
import {
  CommonActions,
  NavigationProp,
  useNavigation,
} from '@react-navigation/native';
import DialogPhone from 'components/Dialog.phone';
import { Box, Button, Icon, Row, Typography } from 'cube-ui-components';
import NeedsSummary from 'features/fna/components/exploreNeeds/summary/NeedsSummary';
import { ConcernId } from 'features/fna/types/concern';
import { useFnaStore } from 'features/fna/utils/store/fnaStore';
import { SaleIllustrationTargetType } from 'features/home/<USER>/CreateSaleIllustrationModal/types';
import PdfViewer, {
  PdfGenerator,
} from 'features/pdfViewer/components/PdfViewer';
import { CardProvider } from 'features/productSelection/components/CardContext/CardContext';
import ClientReasonModal from 'features/productSelection/components/ClientReasonModal/ClientReasonModal';
import ClientWaiverModal from 'features/productSelection/components/ClientWaiverModal/ClientWaiverModal';
import FamilySharePlan from 'features/productSelection/components/FamilySharePlan';
import OtherRecommendationProductList from 'features/productSelection/components/OtherRecommendationProductList';
import RecommendationHeader from 'features/productSelection/components/RecommendationHeader';
import RecommendationProductCard from 'features/productSelection/components/RecommendationProductCard/RecommendationProductCard';
import {
  useFormatProductListParties,
  useProductListQuery,
} from 'features/productSelection/hooks/useProducts';
import { getFnaMailConfig } from 'features/productSelection/utils/helpers/getFnaMailConfig';
import useBoundStore from 'hooks/useBoundStore';
import { useCreateFna } from 'hooks/useCreateFna';
import useLayoutAdoptionCheck from 'hooks/useDeviceCheck';
import { useGenerateFnaPdf } from 'hooks/useGenerateFnaPdf';
import { useGetActiveCase } from 'hooks/useGetActiveCase';
import { useGetAgentProfile } from 'hooks/useGetAgentProfile';
import { useGetCubeChannel } from 'hooks/useGetCubeChannel';
import useLatest from 'hooks/useLatest';
import { useSaveProductRecommendation } from 'hooks/useSaveProductRecommendation';
import useToggle from 'hooks/useToggle';
import useWindowAdaptationHelpers from 'hooks/useWindowAdaptationHelpers';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import { useTranslation } from 'react-i18next';
import {
  LayoutChangeEvent,
  LayoutRectangle,
  NativeScrollEvent,
  NativeSyntheticEvent,
  ScrollView,
  View,
  useWindowDimensions,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { RootStackParamList } from 'types';
import { CaseStatus, FnaRejectRecommendedReason } from 'types/case';
import { GroupCode, Product } from 'types/products';
import { countryModuleFnaConfig, moduleConfigs } from 'utils/config/module';
import { country } from 'utils/context';
import { hasLicense } from 'utils/helper/agentUtils';
import { mapProductsForRecommendation } from 'utils/helper/mapProducts';
import { shallow } from 'zustand/shallow';
import { useProductRecommendation } from './useProductRecommendation';

const Container = styled.View(({ theme: { colors } }) => {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return {
    backgroundColor: isTabletMode ? colors.palette.fwdGrey[50] : colors.primary,
    flex: 1,
  };
});

const RecommendationContainer = styled.View(({ theme: { colors, space } }) => {
  const { isTabletMode } = useLayoutAdoptionCheck();
  return {
    backgroundColor: colors.primary,
    paddingTop: space[isTabletMode ? 6 : 4],
    paddingBottom: space[isTabletMode ? 6 : 5],
    gap: space[isTabletMode ? 6 : 3],
    minHeight: isTabletMode ? 340 : undefined,
  };
});

const Title = styled(Typography.H6)(({ theme: { space } }) => {
  const { isTabletMode } = useLayoutAdoptionCheck();
  const { isNarrowScreen } = useWindowAdaptationHelpers();
  return {
    marginLeft: isTabletMode ? space[6] : space[isNarrowScreen ? 3 : 4],
  };
});

type ProductWithConcern = {
  need: ConcernId | '';
  product: Product;
} | null;

const ProductRecommendationScreen = () => {
  const { t } = useTranslation(['product', 'fna', 'common']);
  const { width } = useWindowDimensions();
  const { colors, space } = useTheme();
  const {
    setFamilySharePlanType,
    setFamilySharePlanCustomerId,
    setFamilySharePlanPolicyNumber,
    updateIdNumber,
  } = useFnaStore(
    state => ({
      setFamilySharePlanType: state.setFamilySharePlanType,
      setFamilySharePlanPolicyNumber: state.setFamilySharePlanPolicyNumber,
      setFamilySharePlanCustomerId: state.setFamilySharePlanCustomerId,
      updateIdNumber: state.updateIdNumber,
    }),
    shallow,
  );
  const setAppLoading = useBoundStore(state => state.appActions.setAppLoading);
  const setAppIdle = useBoundStore(state => state.appActions.setAppIdle);
  const [sectionLayouts, setSectionLayouts] = useState<Array<LayoutRectangle>>(
    [],
  );
  const [recommendedListWidth, setRecommendedListWidth] = useState(0);
  const latestSectionLayouts = useLatest(sectionLayouts);
  const scrollViewRef = useRef<ScrollView>(null);
  const scrollingRef = useRef<boolean>(false);
  const scrollOffsetRef = useRef(0);

  const [waiverVisible, showWaiver, hideWaiver] = useToggle();
  const [reasonVisible, showReason, hideReason] = useToggle();
  const [reason, setReason] = useState<FnaRejectRecommendedReason>(
    FnaRejectRecommendedReason.NOT_REJECT,
  );
  const [customReason, setCustomReason] = useState('');
  const [isViewedDoc, setIsViewedDoc] = useState(false);
  const [selectedProduct, setSelectedProduct] =
    useState<ProductWithConcern>(null);
  const [invalidProductName, setInvalidProductName] = useState<string>();
  const [isGeneratingFnaDoc, setGeneratingFnaDoc] = useState(false);
  const [viewingPdf, showPdf, hidePdf] = useToggle();
  const [familyPlanVisible, showFamilyPlan, hideFamilyPlan] = useToggle();

  const { data: agentInfo } = useGetAgentProfile();
  const channel = useGetCubeChannel();
  const { caseObj } = useGetActiveCase();
  const { mutateAsync: createFna, isLoading: isCreatingFna } = useCreateFna();
  const { isTabletMode } = useLayoutAdoptionCheck();

  const saveAcknowledgement = useCallback(
    (acknowledge: boolean) => {
      if (caseObj && caseObj.id && caseObj.fna) {
        return createFna({
          caseId: caseObj.id,
          fna: {
            ...caseObj.fna,
            acknowledge,
          },
        });
      }
    },
    [caseObj, createFna],
  );

  const validateLicense = useCallback(
    (product: Product) => {
      if (!agentInfo) return false;
      const { licenses, isAgentLicenseActive } = agentInfo;
      if (!isAgentLicenseActive) return false;

      // Check for TL or VL licenses
      if (hasLicense(licenses, 'TL') || hasLicense(licenses, 'VL')) {
        return true;
      }

      // Check for TL license and non-VUL product
      if (hasLicense(licenses, 'TL') && !product.isVUL) {
        return true;
      }

      // Check for VL license and VUL product
      if (hasLicense(licenses, 'VL') && product.isVUL) {
        return true;
      }

      return false;
    },
    [agentInfo],
  );

  const { lifeJourney, updateSelectedProductCode } = useFnaStore(
    state => ({
      lifeJourney: state.lifeJourney,
      updateSelectedProductCode: state.updateSelectedProductCode,
    }),
    shallow,
  );

  const {
    mutate: getProducts,
    data: products,
    isSuccess: isGetProductsSuccess,
  } = useProductListQuery();
  const { mutateAsync: generateFnaPdf } = useGenerateFnaPdf();

  const navigation = useNavigation<NavigationProp<RootStackParamList>>();
  const { recommendationProducts, otherProducts } = useProductRecommendation(
    products,
    lifeJourney,
    channel,
  );

  const {
    mutateAsync: saveProductRecommendation,
    isLoading: isSavingProductRecommendation,
  } = useSaveProductRecommendation();

  const otherProductSections = useMemo(() => {
    switch (country) {
      case 'ph':
      case 'id':
        return mapProductsForRecommendation(otherProducts);
      case 'my':
      case 'ib':
        return [
          {
            title: t('product:otherProducts') as GroupCode,
            data: otherProducts,
          },
        ];
      default:
        return [];
    }
  }, [otherProducts, t]);

  const onSectionLayout = (index: number) => (e: LayoutChangeEvent) => {
    e.persist();
    setSectionLayouts(layouts => {
      layouts[index] = e.nativeEvent.layout;
      return layouts;
    });
  };

  const { insureds, proposers } = useFormatProductListParties();

  useEffect(() => {
    const coverageDetailsCompleted = caseObj?.status?.includes(
      CaseStatus.COVERAGE,
    );

    const productListPayload = coverageDetailsCompleted
      ? {
          insureds,
          proposers,
        }
      : {};

    getProducts(productListPayload);
  }, [getProducts]);

  const [tabIndex, setTabIndex] = useState(0);
  const latestTabIndex = useLatest(tabIndex);

  const handleOnScroll = useCallback(
    (e: NativeSyntheticEvent<NativeScrollEvent>) => {
      const { y } = e.nativeEvent.contentOffset;
      const direction = y > scrollOffsetRef.current ? 'up' : 'down';
      scrollOffsetRef.current = y;
      if (scrollingRef.current) {
        return;
      }
      let index = -1;

      if (direction === 'up') {
        for (let i = 0; i < latestSectionLayouts.current.length; i++) {
          const space =
            i > 0 ? latestSectionLayouts.current[i - 1].height * 0.4 : 0;
          if (y >= latestSectionLayouts.current[i].y - space) {
            index = i;
          }
        }
      } else {
        for (let i = 0; i < latestSectionLayouts.current.length; i++) {
          const space =
            i > 0 ? latestSectionLayouts.current[i - 1].height * 0.3 : 0;
          if (
            y >= latestSectionLayouts.current[i].y - space &&
            y <=
              latestSectionLayouts.current[i].y +
                latestSectionLayouts.current[i].height * 0.4
          ) {
            index = i;
          }
        }
      }
      if (index !== latestTabIndex.current && index >= 0) {
        setTabIndex(index);
      }
    },
    [latestSectionLayouts, latestTabIndex],
  );

  const save = useCallback(
    async ({
      reason,
      selectedProduct,
    }: {
      reason: FnaRejectRecommendedReason;
      selectedProduct: ProductWithConcern;
    }) => {
      if (!caseObj || !selectedProduct) return;

      await saveProductRecommendation({
        caseId: caseObj?.id || '',
        data: {
          needs: Object.entries(
            recommendationProducts.reduce<
              Partial<{
                [need in ConcernId]: Product[];
              }>
            >((ret, item) => {
              ret[item.need] ||= [];
              ret[item.need]?.push(item.product);
              return ret;
            }, {}),
          ).map(([need, products]) => ({
            need,
            recommendedProducts: products.map(p => ({
              pid: p.pid,
              productName: p.productName,
            })),
          })),
          selectedNeed: selectedProduct.need,
          rejectRecommendedReason: reason,
          rejectRecommendedOtherReason: customReason,
          selectedProduct: {
            pid: selectedProduct.product.pid,
            productName: selectedProduct.product.productName,
          },
        },
      });
    },
    [caseObj, customReason, recommendationProducts, saveProductRecommendation],
  );

  const navigateToNextScreen = (args: { isMultipleInsureds?: boolean }) => {
    navigation.dispatch(state => {
      let numberOfScreensToPopOut = 1; // only product recommendation
      if (state.routes[state.routes.length - 2].name === 'Fna') {
        numberOfScreensToPopOut = 2; // fna + product recommendation
      }
      const routes = state.routes.slice(
        0,
        state.routes.length - numberOfScreensToPopOut,
      );
      if (caseObj?.status?.some(s => s === CaseStatus.COVERAGE)) {
        routes.push({
          name: 'SalesIllustrationForm',
          params: {
            lockMainInsuredToProposer: args.isMultipleInsureds,
          },
        } as (typeof state.routes)[0]);
      } else if (caseObj?.status?.some(s => s === CaseStatus.FNA)) {
        routes.push({
          name: 'CoverageDetailsScreen',
          params: {
            targetType: SaleIllustrationTargetType.INDIVIDUAL,
          },
        } as (typeof state.routes)[0]);
      } else {
        routes.push({
          name: 'CoverageDetailsScreen',
        } as (typeof state.routes)[0]);
      }
      return CommonActions.reset({
        ...state,
        routes,
        index: routes.length - 1,
      });
    });
  };

  const onPressOtherProduct = useCallback(
    async (p: Product) => {
      if (country === 'id') {
        updateSelectedProductCode(
          p?.pid || '',
          p?.templates?.isMultipleInsureds,
        );
        await save({ reason, selectedProduct: { need: '', product: p } });
        navigateToNextScreen({});
      } else if (country === 'ph') {
        const licenseValidated = validateLicense(p);
        if (licenseValidated) {
          setSelectedProduct({ need: '', product: p });
          setReason(FnaRejectRecommendedReason.NOT_REJECT);
          showReason();
        } else {
          setInvalidProductName(p.productName.en);
        }
      } else {
        setSelectedProduct({ need: '', product: p });
        setReason(FnaRejectRecommendedReason.NOT_REJECT);
        showReason();
      }
    },
    [showReason, save, reason, validateLicense],
  );

  const onRecommendedSelect = useCallback(
    async (data: { need?: ConcernId; product: Product }) => {
      if (country === 'id') {
        await save({
          reason,
          selectedProduct: { need: data?.need || '', product: data.product },
        });
        updateSelectedProductCode(
          data.product?.pid || '',
          data.product?.templates?.isMultipleInsureds,
        );
        navigateToNextScreen({});
      } else if (country === 'ph') {
        setReason(FnaRejectRecommendedReason.NOT_REJECT);
        const licenseValidated = validateLicense(data.product);
        if (licenseValidated) {
          setSelectedProduct({ need: data.need || '', product: data.product });
          if (isViewedDoc) setIsViewedDoc(false);
          showWaiver();
        } else {
          setInvalidProductName(data.product.productName.en);
        }
      } else {
        setSelectedProduct({ need: data.need || '', product: data.product });
        if (isViewedDoc) setIsViewedDoc(false);
        showWaiver();
      }
    },
    [showWaiver, validateLicense, save, reason, isViewedDoc, setIsViewedDoc],
  );

  const onConfirmReason = useCallback(
    (reason: FnaRejectRecommendedReason) => {
      setReason(reason);
      if (isViewedDoc) setIsViewedDoc(false);
      setTimeout(() => {
        showWaiver();
      }, 500);
    },
    [showWaiver, isViewedDoc, setIsViewedDoc],
  );

  const pdfGeneratorRef = useRef<PdfGenerator>(async () => ({
    base64: '',
    fileName: '',
  }));

  const mailConfig = useMemo(() => {
    return getFnaMailConfig({
      clientFirstName: caseObj?.fna?.firstName || '',
      clientEmail:
        caseObj?.parties && caseObj?.parties.length > 0
          ? caseObj?.parties[0].contacts.email
          : '',
      agentNumber: agentInfo?.contact?.mobilePhone || '',
      agentName: agentInfo?.person?.fullName || '',
      agentEmail: agentInfo?.contact?.email || '',
    });
  }, [caseObj, agentInfo]);

  const onGetFnaDoc = useCallback(
    async (acknowledge: boolean) => {
      if (caseObj && selectedProduct) {
        setGeneratingFnaDoc(true);
        try {
          await saveAcknowledgement(acknowledge);
          await save({ reason, selectedProduct });
          pdfGeneratorRef.current = async () => {
            let pdf = '';
            const res = await generateFnaPdf(caseObj.id);
            pdf = res.report;
            if (!pdf) {
              throw new Error('empty pdf');
            }
            return {
              fileName: t('product:previewFNA'),
              base64: pdf,
            };
          };
          hideWaiver();
          showPdf();
        } catch (e) {
          console.log(e);
        } finally {
          setGeneratingFnaDoc(false);
        }
      }
    },
    [
      caseObj,
      selectedProduct,
      saveAcknowledgement,
      save,
      reason,
      hideWaiver,
      showPdf,
      generateFnaPdf,
      t,
    ],
  );

  const acknowledge = useRef(false);

  const onConfirmWaiver = useCallback(async () => {
    if (caseObj && selectedProduct) {
      await saveAcknowledgement(acknowledge.current);
      await save({ reason, selectedProduct });
      const isMultipleInsureds =
        selectedProduct?.product?.templates.isMultipleInsureds;
      updateSelectedProductCode(
        selectedProduct?.product?.pid || '',
        isMultipleInsureds,
      );
      navigateToNextScreen({
        isMultipleInsureds,
      });
    }
  }, [
    caseObj,
    selectedProduct,
    save,
    reason,
    saveAcknowledgement,
    updateSelectedProductCode,
    navigation,
  ]);

  const title = `${t('product:recommended')} (${
    recommendationProducts?.length || 0
  })`;

  const { isNarrowScreen } = useWindowAdaptationHelpers();
  const insets = useSafeAreaInsets();

  const shouldShowFamilySharePlan =
    isTabletMode && selectedProduct?.product.pid === 'IJ7';

  const shouldDisplaySummaryNeeds =
    countryModuleFnaConfig?.shouldDisplaySummaryNeeds;

  return (
    <CardProvider>
      <Container>
        <RecommendationHeader />
        <ScrollView
          bounces={false}
          stickyHeaderIndices={isTabletMode ? undefined : [0]}>
          {shouldDisplaySummaryNeeds && <NeedsSummary />}
          <RecommendationContainer>
            <Title color={colors.onPrimary} fontWeight="bold">
              {title}
            </Title>
            <ScrollView
              showsHorizontalScrollIndicator={false}
              onContentSizeChange={w => {
                setRecommendedListWidth(w);
              }}
              contentContainerStyle={{
                paddingLeft: isTabletMode
                  ? space[4]
                  : space[isNarrowScreen ? 3 : 4],
                paddingRight: isTabletMode
                  ? space[4]
                  : space[isNarrowScreen ? 2 : 3],
              }}
              horizontal>
              {recommendationProducts.map((item, index) => {
                const isLast = index === recommendationProducts.length - 1;
                return (
                  <View
                    key={`${item.product.pid}_${index}`}
                    style={{ zIndex: 999 - index, overflow: 'visible' }}>
                    <RecommendationProductCard
                      {...item}
                      onPress={onRecommendedSelect}
                      popUpDirection={
                        recommendedListWidth > width && isLast
                          ? 'left'
                          : 'right'
                      }
                    />
                  </View>
                );
              })}
            </ScrollView>
          </RecommendationContainer>
          <OtherRecommendationProductList
            sections={otherProductSections}
            onSectionLayout={onSectionLayout}
            scrollViewRef={scrollViewRef}
            onScroll={handleOnScroll}
            isNarrowScreen={isNarrowScreen}
            contentContainerStyle={{
              paddingBottom: insets.bottom,
            }}
            onPressItemSelect={onPressOtherProduct}
          />
          {reasonVisible && (
            <ClientReasonModal
              reason={reason}
              setCustomReason={setCustomReason}
              handleClose={hideReason}
              onConfirm={onConfirmReason}
              visible={reasonVisible}
            />
          )}
          {waiverVisible && (
            <ClientWaiverModal
              handleClose={hideWaiver}
              reason={reason}
              onGetFnaDoc={onGetFnaDoc}
              onConfirm={async (ack: boolean) => {
                acknowledge.current = ack;
                if (shouldShowFamilySharePlan) {
                  showFamilyPlan();
                } else {
                  await onConfirmWaiver();
                }
              }}
              isGettingFnaDoc={isGeneratingFnaDoc}
              isSaving={isSavingProductRecommendation || isCreatingFna}
              isViewedDoc={isViewedDoc}
            />
          )}
          <LicenseInvalidModal
            productName={invalidProductName}
            handleClose={() => setInvalidProductName(undefined)}
          />
        </ScrollView>
      </Container>
      <PdfViewer
        visible={viewingPdf}
        title={t('product:previewFNA')}
        onClose={hidePdf}
        pdfGenerator={pdfGeneratorRef.current}
        downloadable
        sharable={moduleConfigs[country].fnaConfig.previewFNASharable}
        mailConfig={mailConfig}
        shareType={'email'}
        actionOption={
          moduleConfigs[country].fnaConfig.previewFNAMandatory
            ? {
                text: t('fna:done'),
                onPress: () => {
                  setIsViewedDoc(true);
                  hidePdf();
                  setTimeout(() => {
                    showWaiver();
                  }, 500);
                },
                actionMode: 'default',
                activeMode: !isViewedDoc ? 'end-of-file' : 'default',
              }
            : undefined
        }
      />
      {familyPlanVisible && (
        <FamilySharePlan
          visible={familyPlanVisible}
          onDismiss={hideFamilyPlan}
          onDone={async (
            familySharePlanType,
            policyNumber,
            customerIdNumber,
            customerId,
          ) => {
            setFamilySharePlanType(familySharePlanType);
            setFamilySharePlanPolicyNumber(policyNumber);
            setFamilySharePlanCustomerId(customerId || '');
            updateIdNumber(customerIdNumber);
            setAppLoading();
            try {
              await onConfirmWaiver();
            } finally {
              setAppIdle();
            }
          }}
          pid={selectedProduct?.product.pid || ''}
        />
      )}
    </CardProvider>
  );
};

const LicenseInvalidModal = ({
  productName,
  handleClose,
}: {
  productName?: string;
  handleClose: () => void;
}) => {
  const { t } = useTranslation(['product']);
  const { colors, space, sizes } = useTheme();

  return (
    <DialogPhone visible={productName !== undefined}>
      <Box alignSelf="center">
        <Icon.Warning fill={colors.palette.alertRed} size={sizes[16]} />
      </Box>
      <Box mt={space[4]}>
        <Typography.H7 fontWeight="medium" style={{ textAlign: 'center' }}>
          {t('product:invalidLicense', {
            productName,
          })}
        </Typography.H7>
      </Box>
      <Row mt={space[6]}>
        <Button
          text={t('product:gotIt')}
          // variant="secondary"
          onPress={handleClose}
          style={{ flex: 1 }}
        />
      </Row>
    </DialogPhone>
  );
};

export default ProductRecommendationScreen;
