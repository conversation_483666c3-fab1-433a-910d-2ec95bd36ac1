import React from 'react';
import ScreenHeader from 'navigation/components/ScreenHeader/phone';
import { useTranslation } from 'react-i18next';
import EliteAgencyBenefitsContent from 'features/recognition/EliteAgencyBenefitsContent';

export default function EliteAgencyBenefitsScreen() {
  const { t } = useTranslation('recognition');

  return (
    <>
      <ScreenHeader
        route={'EliteAgencyDetails'}
        customTitle={t('recognition.eliteAgent.benefits.title')}
        isLeftArrowBackShown
      />
      <EliteAgencyBenefitsContent />
    </>
  );
}





