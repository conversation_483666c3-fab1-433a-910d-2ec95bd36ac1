import { ContentStackBase } from './contentStack';

export enum Feature {
  OCR = 'OCR',
  GURU_NUDGE = 'guru_nudge',
  GURU_REPORT = 'guru_weekly_report',
  GURU_PRODUCT_SELECTION = 'guru_product_selection',
  GURU_QUICK_QUOTE = 'guru_quick_quote',
  OCR_BORDER = 'ocr_border',
  OCR_ALBUM = 'ocr_album',
  OCR_EDGE_DETECTION = 'ocr_edge_detection',
  LIVENESS_CHECK = 'liveness_check',
  IGNITE_FEATURE = 'ignite_feature',
}

export type FeatureToggleResponse = ContentStackBase & {
  feature: [
    {
      function: Feature;
      _metadata: {
        uid: string;
      };
      available: boolean;
      settings: {
        parameter: string;
        value: string;
      }[];
    },
  ];
  title: string;
};

export type FeatureFlag = {
  [key in Feature]: {
    available: boolean;
    settings: {
      parameter: string;
      value: string;
    }[];
  };
};

export const defaultFeatureFlag: FeatureFlag = {
  [Feature.OCR]: {
    available: false,
    settings: [],
  },
  [Feature.GURU_NUDGE]: {
    available: false,
    settings: [],
  },
  [Feature.GURU_REPORT]: {
    available: false,
    settings: [],
  },
  [Feature.GURU_PRODUCT_SELECTION]: {
    available: false,
    settings: [],
  },
  [Feature.GURU_QUICK_QUOTE]: {
    available: false,
    settings: [],
  },
  [Feature.OCR_BORDER]: {
    available: false,
    settings: [],
  },
  [Feature.OCR_EDGE_DETECTION]: {
    available: false,
    settings: [],
  },
  [Feature.OCR_ALBUM]: {
    available: false,
    settings: [],
  },
  [Feature.LIVENESS_CHECK]: {
    available: false,
    settings: [],
  },
  [Feature.IGNITE_FEATURE]: {
    available: false,
    settings: [],
  },
};
