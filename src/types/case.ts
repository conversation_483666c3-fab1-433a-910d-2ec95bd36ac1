import { LifeStage } from 'features/fna/types/lifeJourney';
import { AdviceType } from 'features/fna/utils/store/fnaStore';
import { BuildCountry } from 'types';
import { Party } from './party';
import { Gender, Name } from './person';
import { PaymentMode } from './proposal';
import { FamilyPlanInfo } from './quotation';

export enum CaseStatus {
  COVERAGE = 'COVERAGE',
  QUICK_SI = 'QUICK_SI',
  FNA = 'FNA',
  CFF = 'CFF',
  FULL_SI = 'FULL_SI',
  IN_APP = 'IN_APP',
  REMOTE_SELLING_COMPLETED = 'REMOTE_SELLING_COMPLETED',
  APP_SUBMITTED = 'APP_SUBMITTED',
  UNKNOWN = 'UNKNOWN',
  PENDING_FOR_LEADER = 'PENDING_FOR_LEADER',
  REJECTED_BY_LEADER = 'REJECTED_BY_LEADER',
  EXPIRED_AFTER_15_DAYS = 'EXPIRED_AFTER_15_DAYS',
  APPROVED_BY_LEADER = 'APPROVED_BY_LEADER',
}

export type BasicInfo = {
  proposeDT: Date;
  tsarInd: number;
  currency: 'RM' | 'PHP' | 'USD';
  paymentMode?: PaymentMode;
  rateDefineDate: Date;
  autoBalanceOption?: string;
  backDate?: string;
  toBackdate?: string;
};

export type UpdateCFF = {
  caseId: string;
  id: string;
  version: null;
  agentId: string;
  stageStatus: {};
  stage: string;
  vulnerable: {};
  proposalNum: string;
  acknowledge: true;
  pdfToken: string;
  createdDT: Date;
  updatedDT: Date;
  expiryDT: Date;
  clientPreference: {
    agentId: string;
    disclosure: string;
    purposeOfInsuranceList: [
      {
        agentId: string;
        type: string;
        planned: false;
        review: false;
        priority: 1;
      },
      {
        agentId: string;
        type: string;
        planned: false;
        review: false;
        priority: 2;
      },
      {
        agentId: string;
        type: string;
        planned: false;
        review: false;
        priority: 3;
      },
      {
        agentId: string;
        type: string;
        planned: false;
        review: false;
        priority: 4;
      },
      {
        agentId: string;
        type: string;
        planned: false;
        review: false;
        priority: 5;
      },
      {
        agentId: string;
        type: string;
        planned: false;
        review: false;
        priority: 6;
      },
    ];
  };
  adviceRecord: {
    agentId: string;
    recommendationList: [
      {
        agentId: string;
        nameofPerson: string;
        planName: string;
        paymentFrequency: string;
        sumAssured: number;
        contribution: number;
        term: number;
        participation: true;
        additionalBenefits: string;
        reasonForRecommendationList: [
          {
            agentId: string;
            reason: string;
            otherSpecify: string;
            removed: null;
          },
        ];
        removed: null;
      },
    ];
    removed: null;
    disclosure: false;
    comments: string;
  };
};

export type Case = {
  id: string;
  version?: number;
  appVersion?: string;
  latestStatus: CaseStatus;
  status?: CaseStatus[];
  parties?: Party[];
  agent: {
    id: string;
    supervisorCode?: string;
    supervisorName?: string;
    fullName?: string;
  };
  fna?: Fna;
  cff?: CFF;
  quotations?: {
    basicInfo: BasicInfo;
    id: string;
    version: number;
    pid: string;
    quotationName: string;
    expiryDate: string | Date;
    isPdfGenerated?: true;
    plans: (Plan | null)[];
    summary: quotationSummary;
    familyPlanInfo?: FamilyPlanInfo;
    insuredSetups?: {
      insuredId: string;
      isMainInsured: boolean;
    }[];
  }[];
  selectedQuotationId?: string;
  application?: Application;
  isRemoteSelling?: boolean;
  files?: File[];
  havePayer?: boolean;
  haveBeneficialOwner?: boolean;
  havePolicyControlAuthority?: boolean;
  havePayorRole?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  pendingForLeaderAt?: Date;
  approvedByLeaderAt?: Date;
  rejectedByLeaderAt?: Date;
  rejectReason?: string;
  reviewedBy?: string;
  prepopulatedBy?: string;
  prepopulatedAt?: Date;
  remoteSelling?: {
    finishedAt?: string;
    sentEmail?: string;
    receivedPOConfirmationCallback?: string;
    receivedPIConfirmationCallback?: string;
    isPoConfirmed: boolean | null;
    isPiConfirmed: boolean | null;
  };
};

interface DateOfBirth {
  date: string;
  isDobMonthZero: boolean;
  isDobDayZero: boolean;
  calendarType: string;
}

interface NumericRange {
  from: number | null;
  to: number | null;
}
interface ProductName {
  en: string;
}
export interface SelectedProduct {
  pid: string;
  productName: ProductName;
}

export enum ExpenseMode {
  Monthly = 'MONTHLY',
  Yearly = 'YEARLY',
}

export enum ExpenseType {
  Housing = 'housing',
  Travel = 'travel',
  Healthcare = 'healthcare',
  Transportation = 'transportation',
  Food = 'food',
  RegularCommitment = 'regularCommitment',
  Other = 'other',
}

export type CommonGoal = {
  targetAmount: number | null;
  coverageAmount: number | null;
  gapAmount: number | null;
  enabled: boolean | null;
  alreadyPlanned: boolean | null;
  toBeDiscussed: boolean | null;
};

export enum BenefitCoverage {
  DEATH = 'death',
  DISABILITY = 'disability',
  CRITICAL_ILLNESS = 'criticalIllness',
  OTHERS = 'others',
}

export interface ExistingPolicy {
  productLine: string;
  insuredName: string;
  totalPremium: number;
  paymentMode: string;
  maturityDate: string;
  premiumBenefits: {
    coverage: BenefitCoverage;
    totalPremium?: number | null;
  }[];
}

export enum DependentRelationship {
  kid = 'KID',
  spouse = 'SPOUSE',
  parent = 'PARENT',
  sibling = 'SIBLING',
}

export type Dependent = {
  id?: string;
  name?: Name;
  relationship: DependentRelationship;
  gender?: Gender;
  age: number;
};

export type Fna = {
  id?: string;
  caseId?: string | null;
  version?: number;
  name?: string;
  title?: string | null;
  firstName: string;
  middleName?: string;
  lastName?: string;
  gender: string;
  productWithPremiumDevelopmentPotential?: boolean;
  productCurrency: string;
  dateOfBirth: Partial<DateOfBirth>;
  isCompleted: boolean;
  lifeStage: LifeStage;
  ageToRetire: number;
  numberOfKids: number;
  kidsAge: number[];
  havePartner: boolean;
  partnerAge: number;
  planToTravel: boolean;
  goalPercentage: NumericRange;
  annuallyIncome: NumericRange;
  monthlyIncome: NumericRange;
  insuranceProtectionPeriod: NumericRange;
  expectedIncomeIncrement: number;
  totalAssets: number;
  totalLiabilities: number;
  existingPolicies: ExistingPolicy[] | null;
  concerns: string[];
  dependents: Dependent[];
  educationGoal: CommonGoal & {
    numberOfKids: number | null;
    goals: {
      targetAmount: number | null;
      coverageAmount: number | null;
      gapAmount: number | null;
      yearsToAchieve: number | null;
      firstName: string;
      middleName?: string;
      lastName?: string;
      dateOfBirth: {
        date?: string;
      } | null;
      childAge: number | null;
      gender: Gender | null;
      collegeType: number | null;
      yearsInCollege: number | null;
      annualTuitionCosts: number | null;
      inflationRate: number | null;
      hasSavings: boolean | null;
      expenseMode: ExpenseMode | null;
      universityLocation: string;
      annualLivingExpenses: number | null;
    }[];
    yearsToCollege: number | null;
    yearsInCollege: number | null;
    yearsToAchieve: number | null;
  };
  retirementGoal: CommonGoal & {
    yearsToAchieve: number | null;
    ageToRetire: number | null;
    monthlyAllowance: number | null;
    hasSavings: boolean | null;
    yearsToRetire: number | null;
    annualIncome: number | null;
    yearsToReplace: number | null;
    otherCoverageAmount: number | null;
    inflationRate: number | null;
    investmentRate: number | null;
    expenseMode: ExpenseMode | null;
    expenses: {
      type: ExpenseType;
      amount: number | null;
    }[];
  };
  investmentGoal: CommonGoal & {
    yearsToAchieve: number | null;
    purpose: string | null;
    hasSavings: boolean | null;
    initialInvestmentAmount: number | null;
    regularInvestmentAmount: number | null;
    investmentDuration: string;
    monthlyPayout: number | null;
    payoutPeriod: number | null;
  };
  savingsGoal: CommonGoal & {
    purpose: string;
    yearsToAchieve: number | null;
  };
  incomeProtectionGoal: CommonGoal & {
    hasSavings: boolean | null;
    monthlyExpenses: number | null;
    annualIncome: number | null;
    yearsToReplace: number | null;
    expenses: {
      type: ExpenseType;
      amount: number | null;
    }[];
  };
  healthProtectionGoal: CommonGoal & {
    hasSavings: boolean | null;
    healthEmergenciesAmount: number | null;
    insuranceCoverageAmount: number | null;
    hospitalisation: {
      targetAmount: number | null;
      coverageAmount: number | null;
      gapAmount: number | null;
      expiryAge: number | null;
    } | null;
    criticalIllness: {
      targetAmount: number | null;
      coverageAmount: number | null;
      gapAmount: number | null;
      expiryAge: number | null;
    } | null;
  };
  legacyPlanningGoal: CommonGoal & {
    netTaxableEstateAmount: number | null;
  };
  loanCoverageGoal: CommonGoal & {
    loanTerm: number | null;
    loanAmount: number | null;
    hasCIProtection: boolean | null;
    ciProtectionAmount: number | null;
  };
  annualAffordablePremium?: number;
  vulnerable: {
    isEmployed?: boolean;
    haveExistingMhit?: boolean;
    vulnerableCustomerTag?: string;
    vulnerableCustomerOption?: string;
  } | null;
  acknowledge: boolean | null;
  adviceType: AdviceType;
} & Partial<FnaProductSelection>;

export enum FnaRejectRecommendedReason {
  NOT_REJECT = '0',
  EXISTING_FWD_POLICY = '1',
  EXISTING_OTHER_POLICY = '2',
  USER_SELECTION = '3',
  NOT_WITHIN_MY_PREFERENCE = '4',
  NOT_MEET_MY_NEEDS = '5',
  EXISTING_POLICY_SAME_FEATURES = '6',
  PREFER_RETURNS = 'A',
  PREFER_INVESTMENT = 'B',
  PREFER_CHEAPER = 'C',
  PREFER_SHORTER = 'D',
  PREFER_LOWER_PAYMENT = 'E',
  OTHER_PLAN_FEATURES = 'F',
  OTHER_REASONS = 'G',
}

export type FnaProductSelection = {
  needs: {
    need: string;
    recommendedProducts: {
      pid: string;
      productName: {
        [lang: string]: string;
      };
    }[];
  }[];
  selectedNeed: string;
  rejectRecommendedReason: FnaRejectRecommendedReason;
  rejectRecommendedOtherReason: string;
  selectedProduct: {
    pid: string;
    productName: {
      [lang: string]: string;
    };
  };
};

export type Application = {
  id?: string;
  agent?: {
    agentChannelCode?: string;
    agentCode?: string;
    agentEmail?: string;
    agentName?: string;
    agentPhoneNumber?: string;
    placeOfSigning?: string;
  };
  secondaryAgent?: {
    agentCode?: string;
    agentName?: string;
  };
  policyId?: string;
  policyNum?: string;
  applicationNum?: string;
  replacementInfo?: ReplacementInfo[];
  proposerConsent?: ProposerConsent[];
  directCredit?: DirectCredit;
  initialPayment?: unknown;
  renewalPayment?: unknown;
  signature?: {
    agent?: Array<Signature | null>;
    insureds?: Array<Signature | null>;
    proposers?: Array<Signature | null>;
    witnesses?: Array<Signature | null>;
    trustees?: Array<Signature | null>;
    parents?: Array<Signature | null>;
    solicitors?: Array<Signature | null>;
  } | null;
  consentSignature?: {
    proposers?: Array<Signature | null>;
    insureds?: Array<Signature | null>;
  } | null;
  agentReport?: AgentReport;
  crossSelling?: {
    sbcProducts: string[];
  };
  paymentMethod?: string | null;
  chequeInfo?: {
    number: string;
    date: string;
    bankName: string;
    issueBank?: string;
    amount: string;
    currency: string;
    forMultipleApplication?: boolean;
  } | null;
  advancePayment?: {
    duration: number;
    amount: number;
  } | null;
  isInAppPayment?: boolean | null;
  passion?: string;
  creditCardIssuedCountry?: string;
};

export type ReplacementInfo = {
  // for ph
  agentId?: string | null;
  confirm?: boolean | null;
  inforcePolicy?: string | null;
  insuranceCompanyName?: string | null;
  paidByPolicyLoan?: string | null;
  parentId?: string | null;
  reinstatablePolicy?: string | null;
  replacedPolicyId?: string | null;
  replaceInfo?: string | null;
  replaceWithApplyFor?: string | null;
  sumAssured?: number | null;
  // for my
  replaceIntentions?: boolean;
  reason?: string;
  operator?: string;
  planName?: string;
  sumCovered?: number;
  terminateInfluence?: boolean;
  isFullySatisfied?: boolean;
  isExtention?: boolean;
  comment?: string;
};

export type ProposerConsent = {
  agentId?: string;
  // for ph
  consentProcessing?: string; // Y, N
  agreement?: string; // Y, N
  // for my
  pdpCheck?: boolean;
  otherCountryTax?: boolean;
  usGreenCardHolder?: boolean;
  otherCountryTaxArray?: {
    country: string;
    haveTIN: string; // yes, no
    tinNumber: string;
    noTinReason: string; // reasonA, reasonB, reasonC
    additionalReason?: string;
  }[];
  // for ib
  isTakeover?: boolean;
  takeoverPolicyNum?: string;
  takeoverPlanName?: string;
  authLevel?: string;
  eponoCheck?: boolean;
  //for id
  underwritingDecisionCheck?: boolean;
  closingAgentAnswers?: ClosingAgentAnswers[];
  passion?: string;
  otherPassion?: string;
  partyId?: string;
  noReason?: string;
};

export type ClosingAgentAnswers = {
  questionId: number;
  answer: string;
  details?: string;
};

export type DirectCredit<Country extends BuildCountry = BuildCountry> =
  Country extends 'ph'
    ? (
        | {
            paymentMethod: 'adda';
            accountHolderForRenewal: string;
            accountNumberForRenewal: string;
            agentId: string;
            bankNameForRenewal: string;
          }
        | {
            paymentMethod: 'aca';
            isSecurityBank: string | null;
            cardHolder: string;
            cardCompany: string;
            cardNumber: string;
            cardExpiryDate: string;
          }
        | {
            paymentMethod: 'cash' | 'pos' | 'cheque' | 'NA';
          }
      ) &
        PHBankDetails
    : Country extends 'my'
    ? {
        paymentMethod: string;
        accountHolder: string;
        accountNumber: string;
        agentId: string;
        bankName: string;
      }
    : never;

export type PHBankDetails = {
  accountHolder?: string;
  accountNumber?: string;
  agentId?: string;
  bankName?: string;
  branchCode?: string;
  proof?: string;
};

export type PHInitialPayment = {
  merchantId?: string;
  parentId?: string;
  paymentOption: string;
  paymentRefNo?: string;
  payType: string;
  updateDT?: string;
};

export type AgentReport = {
  agentId: string;
  changeInfocePolicy: string;
  changeInstatablePolicy: string;
  employmentBusinessPropertiesInfoOfPO: string;
  exactAffiliation1: string[];
  exactAffiliation2: string[];
  familyInfoOfPO: string;
  governementPositions: string;
  governementPositionsTitle: string;
  industryAffiliation1: string;
  industryAffiliation2: string;
  isConfirm: boolean;
  otherRemarks: string;
  pOBOIsPEPRCA: string;
  paidByPolicyLoan: string;
  relatedToFWPFSC: string;
  relatedToPOPI: string;
  relationship: string;
  secondaryAgentCode?: string;
  secondaryAgentName?: string;
};

export type Signature = {
  fileId?: string;
  signatureDt?: string;
  placeOfSignature?: string;
};

export type File = {
  fileName: string;
  filePath: string;
  partyId: string;
  docType: string;
  fromOcr?: boolean;
};

export type CFF = {
  agentId: string;
  stageStatus: unknown;
  stage: string;
  vulnerable?: {
    vulnerableCustomerTag?: string;
    vulnerableCustomerOption?: string;
  } | null;
  proposalNum: string;
  acknowledge: boolean;
  pdfToken: string;
  clientPreference: {
    agentId: string;
    disclosure: string;
    purposeOfInsuranceList: {
      agentId: string;
      type: string;
      planned: boolean;
      review: boolean;
      priority: number;
    }[];
  };
  financialStatement: {
    agentId: string;
    preference: string;
    haveExistingCertificate: boolean;
    existingCertificateList: {
      agentId: string;
      policyHolderName: string;
      lifeAssuredName: string;
      planType: string;
      insurerName: string;
      benefits: (null | number)[];
      contributions: number | null;
      paymentFrequency: string;
      maturityDate?: string;
      removed?: boolean;
    }[];
    incomeProtection: {
      agentId?: string;
      type?: string;
      field1?: string;
      field2?: number | null;
      field3?: number | null;
      field4?: number | null;
      field5?: number | null;
      field6?: number | null;
      field7?: number | null;
      field8?: number | null;
      field9?: number | null;
      field10?: boolean;
      removed?: boolean;
    };
    retirementIncome: {
      agentId?: string;
      type?: string;
      field1?: string;
      field2?: number | null;
      field3?: number | null;
      field4?: number | null;
      field5?: number | null;
      field6?: number | null;
      field7?: number | null;
      field8?: number | null;
      field9?: number | null;
      field10?: boolean;
      removed?: boolean;
    };
    childEducationList: {
      agentId?: string;
      type?: string;
      field1?: string;
      field2?: number | null;
      field3?: number | null;
      field4?: number | null;
      field5?: number | null;
      field6?: number | null;
      field7?: number | null;
      field8?: number | null;
      field9?: number | null;
      field10?: boolean;
      removed?: boolean;
    }[];
    savings: {
      agentId?: string;
      type?: string;
      field1?: string;
      field2?: number | null;
      field3?: number | null;
      field4?: number | null;
      field5?: number | null;
      field6?: number | null;
      field7?: number | null;
      field8?: number | null;
      field9?: number | null;
      field10?: boolean;
      removed?: boolean;
    };
    investments: {
      agentId?: string;
      type?: string;
      field1?: string;
      field2?: number | null;
      field3?: number | null;
      field4?: number | null;
      field5?: number | null;
      field6?: number | null;
      field7?: number | null;
      field8?: number | null;
      field9?: number | null;
      field10?: boolean;
      removed?: boolean;
    };
    medicalPlanningList: {
      agentId?: string;
      type?: string;
      field1?: string;
      field2?: number | null;
      field3?: number | null;
      field4?: number | null;
      field5?: number | null;
      field6?: number | null;
      field7?: number | null;
      field8?: number | null;
      field9?: number | null;
      field10?: boolean;
      removed?: boolean;
    }[];
    financialPlanningList: {
      agentId?: string;
      type?: string;
      field1?: string;
      field2?: number | null;
      field3?: number | null;
      field4?: number | null;
      field5?: number | null;
      field6?: number | null;
      field7?: number | null;
      field8?: number | null;
      field9?: number | null;
      field10?: boolean;
      removed?: boolean;
    }[];
    comments?: string;
    investmentPreference?: string;
    differentiateOthers?: string;
  };
  adviceRecord: {
    agentId: string;
    recommendationList: {
      agentId: string;
      nameofPerson: string;
      planName: string;
      paymentFrequency: string;
      sumAssured: number | null;
      contribution: number | null;
      term: number | null;
      participation: boolean;
      additionalBenefits: string;
      reasonForRecommendationList: {
        agentId: string;
        reason: string;
        otherSpecify: string;
        removed?: boolean;
      }[];
      removed?: boolean;
    }[];
    removed?: boolean;
    disclosure: boolean | null;
    comments?: string;
    preference?: string;
  };
};

type Plan = {
  pid: string;
  insuredId?: string;
  payoutOption?: string;
  paymentType?: string;
  productCode: string;
  policyAge?: number;
  premAge?: number;
  productName: {
    my: string;
    en: string;
  };
  productLine: string;
  version: number;
  brochureLink?: Array<{
    url: string;
  }>;
  paymentMode?: PaymentMode;
  selected: boolean;
  currency?: string;
  policyTerm: number;
  planCode: string;
  contractType?: string;
  riskType?: string;
  benefitSubType?: string;
  priority?: number;
  fatcaIndicator?: string;
  crsIndicator?: string;
  amlIndicator?: string;
  compareSeq?: number;
  premiumTerm: number;
  annualPrem: number;
  sumAssured: number;
  basePrem: number;
  premium?: number;
  annualizedPrem: number;
  loadingRate: number;
  loadedPrem: number;
  loading: number;
  discountRate: number;
  discount: number;
  totalPrem: number;
  totalPremWithTax: number;
  extraPrem: number;
  initialPrem: number;
  monthlyPrem: number;
  semiAnnualPrem: number;
  quarterlyPrem: number;
  stampDuty?: number;
  premiumType?: string;
  insuredIndex?: number;
  embedInd?: boolean;
  flatLoading?: number;
  percLoading?: string;
  isTsarCalculation?: boolean;
};

type quotationSummary = {
  basePrem: number;
  loadedPrem: number;
  loading: number;
  discount: number;
  extraPrem: number;
  totalPrem: number;
  totalPremWithTax: number;
  annualPrem: number;
  annualizedPrem: number;
  initialPrem: number;
  monthlyPrem: number;
  quarterlyPrem: number;
  semiAnnualPrem: number;
  sumAssured: number;
  wopPremiumField2: number;
  wopPremium: number;
  wopPremium2: number;
  tsarvalue: number;
  wopPremium3: number;
  educationCess: number;
  totRiderPremium: number;
};
